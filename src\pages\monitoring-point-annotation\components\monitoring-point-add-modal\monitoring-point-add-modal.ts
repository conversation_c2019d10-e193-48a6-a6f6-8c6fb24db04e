import { ControllerType, TreeOptions } from '../monitoring-point/monitoring-point';

export interface PropOptions {
  open: boolean;
  close: () => void; //关闭方法
  onOk?: () => void; //提交方法
  title: string; //标题
  nodeData: TreeOptions; //选中节点数据
  type: ControllerType; //操作类型
}

export interface PointParams {
  pointCode: string;
  pointName: string;
  name?: string;
  parentId?: string; //父节点id
  id?: string; //编辑节点id
}
