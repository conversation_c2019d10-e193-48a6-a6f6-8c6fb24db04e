.warning-info {
  position: absolute;
  bottom: 10px;
  right: 400px;

  &-tab {
    display: flex;
    align-items: center;
    background: rgba(30, 78, 134, 0.5);
    border-radius: 2px;
    padding: 6px 10px;
    gap: 11px;

    p {
      margin: 0;
      padding: 0;
      display: flex;
      gap: 2px;
    }

    &-item {
      color: #1a1a1a;
      font-size: 12px;
      display: inline-block;
      padding: 4px 7px;
    }
  }
}
