import React, { useEffect, useRef, useState } from 'react';
import { Column } from '@antv/g2plot';
import styles from './g2.component.less';
interface Props {
  data: Array<{
    time: string;
    value: number;
    type: string;
    [key: string]: any;
  }>;
}
const GTwoBar: React.FC<Props> = ({ data }) => {
  const [chart, setChart] = useState<Column>(); // 统计图实例
  const chartRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (data.length > 0) {
      chartInit();
    } else if (chart) {
      clear();
    }
  }, [data]);

  function clear(): void {
    if (chart) {
      chart.destroy();
      setChart(undefined);
    }
  }

  function chartInit(): void {
    if (chart) {
      chart.changeData(data);
    } else {
      // 创建新的图表实例
      const line = new Column(chartRef.current as HTMLElement, {
        data,
        isGroup: true,
        xField: 'time',
        yField: 'value',
        seriesField: 'type',
        appendPadding: [30, 0, 30, 0],
        autoFit: true, // 自动适应
        xAxis: {
          label: {
            offset: 20, // 轴线和文字的举例
            style: {
              fill: '#D4EBFD', // 文字颜色
              fontSize: 12, // 文字大小
            },
          },
          line: {
            style: {
              stroke: '#6CA1DD', // 轴线颜色
              opacity: 0.3, // 透明度
            },
          },
          tickLine: {
            style: {
              stroke: 'transparent', // 设置刻度线颜色（隐藏）
            },
          },
        },
        yAxis: {
          label: {
            autoRotate: false, // 关闭自动旋转
            style: {
              fill: '#D4EBFD',
              fontSize: 12,
              fontWeight: 'bold',
            },
          },
          grid: {
            line: {
              style: {
                lineWidth: 1, // 设置虚线宽度
                stroke: 'rgba(108, 161, 221, 0.3)',
                lineDash: [2, 2], // 虚线的密集度
              },
            },
          },
        },
        legend: {
          itemName: {
            style: {
              fill: '#fff',
            },
          },
          flipPage: true, // 分页
          maxRow: 1, // 两行分页
          pageNavigator: {
            marker: {
              style: {
                fill: '#8FD4FF', // 右边箭头颜色
                inactiveFill: '#8FD4FF', // 作伴箭头颜色
              },
            },
            text: {
              style: {
                fill: '#FFFFFF', // 设置文字颜色为白色
              },
            },
          },
        },
        animation: {
          appear: {
            animation: 'path-in', // 动画
            duration: 5000, // 动画持续
          },
        },
        slider: {
          start: 0, // 起始位置
          end: 1, // 结束位置
          formatter: () => '', // 隐藏缩略轴上的文字
        },
      });
      line.render();
      setChart(line);
    }
  }

  return <div ref={chartRef} className={styles['chart-container']}></div>;
};

export default GTwoBar;
