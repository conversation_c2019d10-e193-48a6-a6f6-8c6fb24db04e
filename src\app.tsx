import React from 'react';
import { StyleSheetManager } from 'styled-components';
import isPropValid from '@emotion/is-prop-valid';

import './global.less';
import './public-path';
import { socket as monitorPageSocket } from './pages/monitor/monitor.page';
import { otherSocketClient } from './pages/equip-status-bim/equip-status-bim.page';
let initialPropsState: any = null;

export const qiankun = {
  // 应用加载之前
  async bootstrap(props: any) {
    console.log(`子应用[${props?.name}] bootstrap`);
  },

  // 应用 render 之前触发
  async mount(props: any) {
    props.container.style.height = '100%';
    console.log(`子应用[${props?.name}] mount`, props);
    if (props) {
      initialPropsState = props;
    }
  },

  // 应用卸载之后触发
  async unmount(props: any) {
    console.log('子应用[WMS] unmount', props);
    monitorPageSocket && monitorPageSocket.deactivate(); // 断开连接
    otherSocketClient && otherSocketClient.deactivate(); // 断开连接
  },
};

export async function getInitialState(): Promise<{ currentProps: any }> {
  const currentProps = initialPropsState;
  return {
    currentProps,
  };
}

export function rootContainer(container: JSX.Element, args: any) {
  // This implements the default behavior from styled-components v5
  function shouldForwardProp(propName: string, target: unknown) {
    if (typeof target === 'string') {
      // For HTML elements, forward the prop if it is a valid HTML attribute
      return isPropValid(propName);
    }
    // For other elements, forward all props
    return true;
  }

  return React.createElement(
    StyleSheetManager,
    {
      disableCSSOMInjection: true,
      shouldForwardProp,
    },
    container,
  );
}
