import React, { memo, useRef, useCallback } from 'react';
import { Descriptions, Badge, Button, Space } from 'antd';
import type { DescriptionsProps } from 'antd';
import styles from './base-info.content.less';
import EditDetailModal from '../modal/edit.detail.component';
import { ProcessButtonsStyle } from '@/constants/process-buttons.style';
import { Pointlist } from '@/pages/point/type';
import { useSelector, useDispatch } from 'umi';
import { useMount } from 'ahooks';

interface Iprops {
  current: Pointlist.Details; // 详情数据
  pointValue: Pointlist.PointValue | undefined; // 测点值
  pointRelationInfo: Pointlist.PointUnitConfig | undefined; // 测点绑定单位配置
  getPointDetailsAPI: () => Promise<void>;
  setClearModal: (value: boolean) => void;
  setIsDevicesModal: (value: boolean) => void;
}
const BaseInfo: React.FC<Iprops> = (props) => {
  const {
    current,
    pointValue,
    getPointDetailsAPI,
    setClearModal,
    setIsDevicesModal,
    pointRelationInfo,
  } = props;
  const { unitData, pointName, pointCode, pointType, switchingValue, deviceResumes } = current;
  const editDetailModalRef = useRef<any>(null);
  const devicesList = useSelector((state: any) => state.point.devices || []);
  const dispatch = useDispatch();
  useMount(() => {
    dispatch({
      type: 'point/fetchDevices',
    });
  });
  const onSetUnit = useCallback(async () => {
    editDetailModalRef.current.openModal();
  }, []);
  const items: DescriptionsProps['items'] = [
    {
      key: 'pointName',
      label: '测点名称',
      children: pointName ?? '-',
    },
    {
      key: 'deviceResumes',
      label: '设备名称',
      children: (
        <Space>
          <a
            onClick={() => {
              window.location.href = `/supervision/equipHistoryDetail?id=${deviceResumes?.[0]?.id}&type=view`;
            }}
          >
            {deviceResumes?.map((el: any) => el.name)?.join(',') || '-'}
          </a>
          <ProcessButtonsStyle>
            <Button
              type="primary"
              size="small"
              onClick={() => {
                switchingValue === '1' ? setClearModal(true) : setIsDevicesModal(true);
              }}
            >
              {switchingValue === '1' ? '取消关联' : '关联'}设备
            </Button>
          </ProcessButtonsStyle>
        </Space>
      ),
    },
    {
      key: 'pointCode',
      label: '编码',
      children: pointCode ?? '-',
    },
    {
      key: 'pointType',
      label: '类型',
      children: devicesList?.find((el: any) => el.dictCode === pointType)?.dictName || '-',
    },
    {
      key: 'pointValue',
      label: '当前测点值',
      children: pointValue?.time ? (
        pointValue?.value
      ) : (
        <Badge style={{ color: '#fff' }} status="error" text="未同步" />
      ),
    },
    {
      key: 'unitData',
      label: '测点值单位',
      children: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span>{unitData || '-'}</span>
          <Button type="primary" size="small" onClick={onSetUnit} style={{ marginLeft: '10px' }}>
            设置测点值单位
          </Button>
        </div>
      ),
    },
  ];
  return (
    <>
      <Descriptions
        className={styles.descriptionsPanel}
        styles={{
          label: { background: 'rgba(33, 122, 255, 0.2)', color: '#fff' },
          content: { color: '#fff' },
        }}
        bordered
        items={items}
        column={2}
      />
      <EditDetailModal
        callback={getPointDetailsAPI}
        pointCode={pointCode}
        ref={editDetailModalRef}
        unitId={pointRelationInfo ? pointRelationInfo?.list[0].id : ''}
      />
    </>
  );
};

export default memo(BaseInfo);
