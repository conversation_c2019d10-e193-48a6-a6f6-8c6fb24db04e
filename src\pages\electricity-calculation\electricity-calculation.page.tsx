import React, { memo, useState } from 'react';
import type { ReactNode, FC } from 'react';

import { ElectricityCalculationWrapper } from './electricity-calculation.style';
import QueryForm from './c-cpns/query-form/query-form.component';
import DetailTable from './c-cpns/detail-table/detail-table.component';
import CalcAllModal from './c-cpns/calc-all-modal/calc-all-modal.component';
import CalcSingleModal from './c-cpns/calc-single-modal/calc-single-modal.component';

interface IProps {
  children?: ReactNode;
}

const ElectricityCalculation: FC<IProps> = () => {
  const [singleModalVisible, setSingleModalVisible] = useState<boolean>(false);
  const [allModalVisible, setAllModalVisible] = useState<boolean>(false);

  return (
    <ElectricityCalculationWrapper>
      <QueryForm setVisible={setAllModalVisible} />
      <DetailTable setVisible={setSingleModalVisible} />
      <CalcAllModal visible={allModalVisible} setVisible={setAllModalVisible} />
      <CalcSingleModal visible={singleModalVisible} setVisible={setSingleModalVisible} />
    </ElectricityCalculationWrapper>
  );
};

export default memo(ElectricityCalculation);
