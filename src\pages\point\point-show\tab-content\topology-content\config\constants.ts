const rootNodeTemplate = {
  id: '1',
  type: 'base',
  position: {
    x: -1280,
    y: -120,
  },
  data: {
    iconKey: '1',
    text: '当前测点',
    id: 'node_root',
    color: '#ec808d',
    type: '当前测点',
  },
  measured: {
    width: 136,
    height: 40,
  },
  selected: true,
  dragging: false,
};

const monitoringPointNodeTemplate = {
  id: '2',
  type: 'base',
  sourcePosition: 'right',
  targetPosition: 'left',
  position: {
    x: -1280,
    y: -235,
  },
  data: {
    id: 2,
    text: '测点',
    iconKey: '1',
    color: '#4d88fa',
    type: '测点',
  },
  measured: {
    width: 136,
    height: 40,
  },
  selected: false,
  dragging: false,
};

const equipPointNodeTemplate = {
  id: '2',
  type: 'base',
  sourcePosition: 'right',
  targetPosition: 'left',
  position: {
    x: -1280,
    y: -235,
  },
  data: {
    id: 2,
    text: '设备',
    iconKey: '1',
    color: '#4d88fa',
    type: '设备',
  },
  measured: {
    width: 136,
    height: 40,
  },
  selected: false,
  dragging: false,
};

export { rootNodeTemplate, monitoringPointNodeTemplate, equipPointNodeTemplate };
