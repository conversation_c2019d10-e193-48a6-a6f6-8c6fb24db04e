import { memo, useEffect, useState } from 'react';
import type { FC, ReactNode } from 'react';
import { ConfigProvider, Table } from 'antd';
import type { TableProps } from 'antd';

import { CapacityTableWrapper } from './capacity-table.style';

type RecordType = {
  key: string;
  capacity: string;
  '1F': string;
  '2F': string;
  '3F': string;
};

interface IProps {
  children?: ReactNode;
  calcResult: {
    [key: string]: string;
  };
}

const DetailTable: FC<IProps> = (props) => {
  const { calcResult } = props;

  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  useEffect(() => {
    setDataSource([
      {
        key: '1',
        capacity: '期初电字',
        '1F': calcResult['发电1F期初电字'] ?? '-',
        '2F': calcResult['发电2F期初电字'] ?? '-',
        '3F': calcResult['发电3F期初电字'] ?? '-',
      },
      {
        key: '2',
        capacity: '期末电字',
        '1F': calcResult['发电1F期末电字'] ?? '-',
        '2F': calcResult['发电2F期末电字'] ?? '-',
        '3F': calcResult['发电3F期末电字'] ?? '-',
      },
      {
        key: '3',
        capacity: '计算电量',
        '1F': calcResult['发电1F计算电量'] ?? '-',
        '2F': calcResult['发电2F计算电量'] ?? '-',
        '3F': calcResult['发电3F计算电量'] ?? '-',
      },
      {
        key: '4',
        capacity: '全厂合计',
        '1F': calcResult['全厂发电合计电量'] ?? '-',
        '2F': '',
        '3F': '',
      },
    ]);
  }, [calcResult]);

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'name',
      align: 'center',
      title: '名称',
      dataIndex: 'name',
      width: '22%',
      children: [
        {
          key: 'capacity',
          align: 'center',
          title: '电量',
          dataIndex: 'capacity',
          rowScope: 'row',
        },
      ],
    },
    {
      key: '1F',
      align: 'center',
      title: '1F',
      dataIndex: '1F',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '全厂合计' ? 3 : 1,
      }),
    },
    {
      key: '2F',
      align: 'center',
      title: '2F',
      dataIndex: '2F',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '全厂合计' ? 0 : 1,
      }),
    },
    {
      key: '3F',
      align: 'center',
      title: '3F',
      dataIndex: '3F',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '全厂合计' ? 0 : 1,
      }),
    },
  ];

  const tableHeaderRenderer = () => (
    <div style={{ position: 'relative' }}>
      <span>一、发电量</span>
      <span
        style={{
          position: 'absolute',
          left: '0',
          right: '0',
          margin: 'auto',
          textAlign: 'center',
        }}
      >
        单位：万千瓦时
      </span>
    </div>
  );

  return (
    <CapacityTableWrapper>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2f4f81',
            },
          },
        }}
      >
        <Table
          bordered
          size="small"
          title={tableHeaderRenderer}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </ConfigProvider>
    </CapacityTableWrapper>
  );
};

export default memo(DetailTable);
