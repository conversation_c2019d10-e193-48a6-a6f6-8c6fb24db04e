import dayjs from 'dayjs';

/**
 * 获取半小时时间
 * <AUTHOR>
 * @returns [startTime, endTime]
 */
export const getHalfHourTime = () => {
  const currentTime = dayjs();
  const halfHourAgo = currentTime.subtract(0.5, 'hour'); // 获取半小时前的时间
  const endTime = currentTime.format('YYYY-MM-DD HH:mm:ss');
  const startTime = halfHourAgo.format('YYYY-MM-DD HH:mm:ss');
  return [startTime, endTime];
};

// 添加当前时间的限制函数
export const disabledDate = (current: dayjs.Dayjs) => {
  // 不能选择未来的日期
  return current && current > dayjs().endOf('minute');
};

// 限制时间选择
export const disabledTime = (date: dayjs.Dayjs) => {
  const now = dayjs();

  // 如果是今天，限制小时和分钟
  if (date && date.format('YYYY-MM-DD') === now.format('YYYY-MM-DD')) {
    return {
      disabledHours: () => {
        const hours = [];
        for (let i = now.hour() + 1; i < 24; i++) {
          hours.push(i);
        }
        return hours;
      },
      disabledMinutes: (selectedHour: number) => {
        // 如果选择的小时等于当前小时，禁用大于当前分钟的选项
        if (selectedHour === now.hour()) {
          const minutes = [];
          for (let i = now.minute() + 1; i < 60; i++) {
            minutes.push(i);
          }
          return minutes;
        }
        return [];
      },
    };
  }

  return {};
};
