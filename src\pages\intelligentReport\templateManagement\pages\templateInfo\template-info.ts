export interface DetailOptions {
  name: string;
  type: string;
  remark: string;
  id: string;
  code: string;
  createTime: string;
  templateVersionList: TemplateVersionOptions[]; //版本列表
  createUserId: string;
  createUserName: string;
}

export interface TemplateVersionOptions {
  id: string;
  templateId: string;
  versionName: string;
  versionState: number; //版本状态;0--未发布；1--已发布
  createDate: string;
  createUserName: string;
}
export const versionStateMap = new Map([
  ['', '未发布'],
  ['0', '未发布'],
  ['1', '已发布'],
]);
