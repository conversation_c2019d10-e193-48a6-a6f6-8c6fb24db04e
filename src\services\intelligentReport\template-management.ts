import { request, jsonHeader } from '@/utils/request';
import { ResponseOptions } from '../monitoring-point-annotation';
import { FormOptions } from '@/pages/intelligentReport/templateManagement/components/addTemplateModal/add-template-modal';
import { PageParams } from '@/pages/intelligentReport/templateManagement/template-management';
import { FormEditOptions } from '@/pages/intelligentReport/templateManagement/components/editTemplateModal/edit-template-modal';
import {
  DetailOptions,
  ReleaseOptions,
} from '@/pages/intelligentReport/templateManagement/pages/addTemplateVersion/add-template-version';
import { ReportAttributeOptions } from './report-plan';
//模板管理

export interface DictOptions {
  dictCode: string;
  dictName: string;
  id: string;
  children: DictOptions[];
}
export interface PageOptions<T> {
  list: T[];
  total: string;
}
//新增模板基础信息
export function addTemplateBaseInfo(data: FormOptions): Promise<ResponseOptions<any>> {
  return request.post('/TemplateManager/insert', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//编辑模板基础信息
export function editTemplateBaseInfo(data: FormEditOptions): Promise<ResponseOptions<any>> {
  return request.post('/TemplateManager/updateByPrimaryKey', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//删除模板信息
export function delTemplateBaseInfo(id: string): Promise<ResponseOptions<any>> {
  return request.post(`/TemplateManager/deleteByPrimaryKey/${id}`);
}
//获取模板信息
export function templateInfo(id: string): Promise<ResponseOptions<any>> {
  return request.get(`/TemplateManager/${id}`);
}
//模板基础信息分页查询
export function listPage<T>(params: PageParams): Promise<ResponseOptions<PageOptions<T>>> {
  return request.get('/TemplateManager/listPage', { params });
}
//发布&编辑&暂存模版版本
export function releaseTemplate(data: ReleaseOptions): Promise<ResponseOptions<any>> {
  return request.post('/TemplateVersion/release', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//删除模板版本
export function delTemplateVersion(id: string): Promise<ResponseOptions<any>> {
  return request.post(`/TemplateVersion/deleteByPrimaryKey/${id}`);
}
//获取模板版本详情
export function templateVersionDetail(id: string): Promise<ResponseOptions<DetailOptions>> {
  return request.get(`/TemplateVersion/${id}`);
}
//获取模板专属计划属性列表
export function temPlanAttribute(): Promise<ResponseOptions<ReportAttributeOptions[]>> {
  return request.get('/PlanAttribute/selectPlanAttribute');
}
