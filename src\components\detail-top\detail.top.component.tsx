import { history } from 'umi';
import styles from './detail.top.component.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { memo } from 'react';
interface IProps {
  title: React.ReactNode;
  backFunc?: () => void;
}
/**
 * @description 详情页顶部组件（标题 + 返回按钮）
 * @param props {title: 标题, backFunc: 返回按钮点击事件}
 */
export default memo(function DetailTop(props: IProps) {
  const { title, backFunc } = props;
  return (
    <div className={styles['page-header']}>
      <div>{title}</div>
      <div className={styles.leftBtn}>
        <a
          className={styles.leftBtn_link}
          onClick={() => {
            if (backFunc) {
              backFunc();
            } else {
              history.go(-1);
            }
          }}
        >
          返回 <DoubleRightOutlined />
        </a>
      </div>
    </div>
  );
});
