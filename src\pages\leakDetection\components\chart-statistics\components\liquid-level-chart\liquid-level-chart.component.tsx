import { <PERSON><PERSON><PERSON>, MouseEventHandler, memo, useState } from 'react';
import styles from './liquid-level-chart.component.less';
import { LineChart } from '@/components/line-chart/line-chart.component';
import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
export const LiquidLevelChart = memo(() => {
  const [chartData, setChartData] = useState<DataOptions[]>([
    {
      label: '2025/07/01',
      value: 100,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/02',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/03',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/04',
      value: 120,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/05',
      value: 120,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/06',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/07',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/08',
      value: 100,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/09',
      value: 100,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/10',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/11',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/12',
      value: 120,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/13',
      value: 120,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/14',
      value: 200,
      type: '启动间隔时长',
    },
    {
      label: '2025/07/15',
      value: 200,
      type: '启动间隔时长',
    },
  ]);
  const [time, setTime] = useState<string>('1');
  //时间改变
  function timeChange(e: MouseEvent<HTMLDivElement>): void {
    const target: any = e.target;
    const value = target.getAttribute('data-value');
    if (value) {
      setTime(value);
    }
  }
  return (
    <div className={styles['liquid-level-chart']}>
      <div className={styles['liquid-level-chart-top']}>
        <div className={styles['liquid-level-chart-title']}>
          <span>启动间隔时长分析</span>
        </div>
        <div
          onClick={timeChange}
          className={`${styles['liquid-level-chart-check']} ${
            styles['liquid-level-chart-check-' + time]
          }`}
        >
          <span data-value="1">近七天</span>
          <span data-value="2">近一月</span>
        </div>
      </div>
      <div className={styles['liquid-level-chart-main']}>
        <LineChart
          stepType="vh"
          sliderColor="#75C575"
          color={['#75C575']}
          unitY={20}
          padding={[30, 20, 60, 40]}
          legend={false}
          smooth={false}
          unit="min"
          chartData={chartData}
        ></LineChart>
      </div>
    </div>
  );
});
