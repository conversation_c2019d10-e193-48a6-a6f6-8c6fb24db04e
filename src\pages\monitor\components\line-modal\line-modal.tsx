import { FC, memo, useEffect, useRef, useState } from 'react';
import styles from './line-modal.less';
import moment from 'moment';
import 'dayjs/locale/zh-cn';
import { LineChartModal } from '@/components/line-chart-modal-monitor/line-chart-modal.component';
import { type DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
import { getComparePoint, getListHistoryData } from '../../monitor.service';
import { QueryParams } from '@/components/line-chart-modal-monitor/line-chart-modal';
import { ColumnsOptions } from '@/components/table-component/table';
import { ColumnsType } from 'antd/es/table';
import { RowsDataType } from '@/components/line-chart-modal-monitor/components/point-modal/point-modal.component';
import { Client } from '@stomp/stompjs';
import { socketConnect } from '@/utils/socket';
import dayjs from 'dayjs';
import { connectUrl, sourceHead2, subTheme, unsubTheme } from '../../constant';
import { rsaEncrypt } from '@/utils/jsencrypt.util';
interface PropOptions {
  open: boolean;
  close: () => void; //关闭方法
  title: string;
  showName: string; //点位Id
  dataType: string; //数据类型
  pointCode: string; //数据类型的code
  client: Client | null; //websocket
}
let selectBegin = '';
let selectEnd = '';
let defaultStartTime = dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
let hasSub = false;
export const LineModal: FC<PropOptions> = memo(
  ({ showName, dataType, pointCode, title, open, close, client }) => {
    const [loading, setLoading] = useState<boolean>(true); //统计图数据加载控制
    const [pointLoading, setPointLoading] = useState<boolean>(true);
    const [dataList, setDataList] = useState<DataOptions[]>([]); //统计图数据
    const dataListRef = useRef<DataOptions[]>([]);
    const [pointList, setPointList] = useState<RowsDataType[]>([]); //对比点位列表数据
    const lineChartRef = useRef<any>(null); //统计图弹框实例
    const [curSocketPoints, setCurCurSocketPoints] = useState<string>(); //当前订阅了webSocket的点位
    // const [socketClient, setSocketClient] = useState<Client>();
    const columns: ColumnsType<ColumnsOptions> = [
      {
        title: '点位',
        dataIndex: 'name',
        align: 'center',
        ellipsis: true,
        width: 200,
      },
      {
        title: '数据类型',
        dataIndex: 'alias',
        align: 'center',
        ellipsis: true,
        width: 400,
      },
    ];
    // #region-接口获取历史数据
    async function getHistoryData(
      params: any,
      pointCodes: string = '',
      isReset: boolean = false,
    ): Promise<any> {
      setLoading(true);
      getListHistoryData({ ...params })
        .then((res) => {
          if (res.code === '1') {
            const list: any = (res.data || [])
              ?.sort((a: any, b: any) => a?.time - b?.time)
              ?.map((item: any) => {
                const formattedLabel = moment(item.time * 1000).format('MM/DD HH:mm:ss');
                return {
                  label: formattedLabel,
                  value: Number(item.value),
                  type: item.name,
                  timestamp: dayjs(formattedLabel, 'MM/DD HH:mm:ss').valueOf(), // 添加时间戳
                };
              });
            let newData;
            // 重置或查询历史时，获取当前点位的部分历史数据
            if (isReset) {
              newData = [...list]?.sort((a: any, b: any) => a?.timestamp - b?.timestamp);
            }
            // 新增点位时，只请求了新增的点位的部分历史数据，和当前已有的数据组合之后排序
            else {
              newData = [...dataListRef.current, ...list]?.sort(
                (a: any, b: any) => a?.timestamp - b?.timestamp,
              );
            }
            setDataList(newData);
            dataListRef.current = newData;
            if (pointCodes) {
              sendMsg(pointCodes, true);
            }
          }
          setLoading(false);
        })
        .catch((err) => {
          setLoading(false);
        });
    }
    // #region-接口获取对比点位列表
    const getComparePointData = (params: any) => {
      setPointLoading(true);
      getComparePoint({ ...params }).then((res) => {
        if (res.code === '1') {
          const data = res.data?.map((item: any) => ({
            ...item,
            name: item.showName,
            key: item.pointCode,
            id: item.pointCode,
          }));
          setPointList(data);
          setPointLoading(false);
        }
      });
    };

    // #region 事件-历史数据查询
    function onQuery({
      selectBegin,
      selectEnd,
      curList,
      addList,
      deleteList,
      isRealTime,
    }: QueryParams): void {
      const selectBeginTime = selectBegin || defaultStartTime;
      const selectEndTime = selectEnd || dayjs().format('YYYY-MM-DD HH:mm:ss');
      const params: any = { selectBegin: selectBeginTime, selectEnd: selectEndTime };
      const curPointList = [
        { alias: dataType, pointCode, showName },
        ...curList?.map((item) => ({
          alias: item.alias,
          pointCode: item.pointCode,
          showName: item.showName,
        })),
      ];
      const addPointList = addList?.map((item) => ({
        alias: item.alias,
        pointCode: item.pointCode,
        showName: item.showName,
      }));
      const deletePointList = deleteList?.map((item) => ({
        alias: item.alias,
        pointCode: item.pointCode,
        showName: item.showName,
      }));
      const curPointCodes = [...curPointList?.map((item: any) => item.pointCode)]?.join(',');
      const addPointCodes = [...addPointList?.map((item: any) => item.pointCode)]?.join(',');
      const deletePointCodes = [...deletePointList?.map((item: any) => item.pointCode)]?.join(',');
      // 走历史数据查询
      if (!isRealTime) {
        params.pointList = curPointList;
        getHistoryData(params, '', true);
        sendMsg(curPointCodes, false);
      }
      // 走webSocket
      else {
        if (client) {
          setCurCurSocketPoints(curPointCodes);

          // 有新增的点位，获取一部分历史数据再监听websocket
          if (addList?.length) {
            params.pointList = addPointList;
            getHistoryData(params, addPointCodes);
          }
          // 有删除的点位，隐藏对应折线，取消对应点位监听
          if (deleteList?.length) {
            const deleteNames = deleteList?.map((item: any) => item?.fullName) || [];
            const newData: DataOptions[] = dataListRef.current?.filter(
              (item: any) => !deleteNames?.includes(item.type),
            );
            dataListRef.current = newData;
            setDataList(newData);

            sendMsg(deletePointCodes, false);
          }
          // 第一次打开或者重置
          if (!addList?.length && !deleteList?.length) {
            params.pointList = curPointList;
            getHistoryData(params, curPointCodes, true);
          }
        }
      }
    }
    // #region 事件-对比点位列表查询
    function onPointModalSearch(searchParam: any): void {
      getComparePointData({ showName, dataType, nameLike: searchParam });
    }

    // #region socket-消息返回
    function messageReturn(soucre: string, client: Client | null = null) {
      if (client) {
        client.subscribe(soucre, function (message: any) {
          try {
            // 处理解析后的 JSON 数据
            const recv = JSON.parse(message.body);
            console.log('收到返回的消息(modal):', recv);
            const formattedLabel = dayjs(recv.timeStamp).format('MM/DD HH:mm:ss');
            const data = {
              label: formattedLabel,
              value: Number(recv.value),
              type: recv.name,
              timestamp: dayjs(formattedLabel, 'MM/DD HH:mm:ss').valueOf(), // 添加时间戳
            };
            const newData = [...dataListRef.current, data]?.sort(
              (a: any, b: any) => a?.timestamp - b?.timestamp,
            );
            dataListRef.current = newData;

            setDataList(newData);
            setLoading(false);
          } catch (error) {
            console.log(
              '收到返回的消息(modal)不是有效的 JSON 格式:',
              message.body,
              'error:',
              error,
            );
          }
        });
      }
    }
    // #region socket-发送消息
    function sendMsg(pointCodes: string, isSubscribe: boolean) {
      const userId = JSON.parse(localStorage.getItem('user') as string).id;
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes,
          userId,
          topicType: sourceHead2,
        }),
        destination: isSubscribe ? subTheme : unsubTheme,
        headers: {
          Authorization: rsaEncrypt(isSubscribe ? subTheme : unsubTheme).toString(),
        },
      };
      console.log(`消息体(${isSubscribe ? '弹窗订阅' : '取消弹窗订阅'})：`, msgBody);
      // 接受返回消息
      !hasSub && messageReturn(sourceHead2 + userId, client);
      // hasSub = true
      // 发送消息
      client && client.publish(msgBody);
    }
    // #region useEffect
    useEffect(() => {
      if (open) {
        selectBegin = '';
        selectEnd = '';
      }
    }, [open]);

    useEffect(() => {
      if (!open && curSocketPoints) {
        // 取消订阅
        sendMsg(curSocketPoints, false);
        // socketClient && socketClient.deactivate();
      }
    }, [open, curSocketPoints]);

    return (
      <LineChartModal
        ref={lineChartRef}
        chartData={dataList}
        pointList={pointList}
        loading={loading}
        pointLoading={pointLoading}
        onQuery={onQuery}
        onPointModalSearch={onPointModalSearch}
        close={close}
        open={open}
        title={title}
        columns={columns}
      />
    );
  },
);
