import { useMount } from 'ahooks';
import * as echarts from 'echarts';
import { useRef, useState, RefObject } from 'react';

/**
 * @description 图表实例初始化
 * <AUTHOR>
 */
export const useCharts = (): [RefObject<HTMLDivElement>, echarts.EChartsType | undefined] => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.EChartsType>();
  useMount(() => {
    const chart = echarts.init(chartRef.current as HTMLElement);
    setChartInstance(chart);
  });
  return [chartRef, chartInstance];
};
