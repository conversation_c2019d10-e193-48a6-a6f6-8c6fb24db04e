import { memo } from 'react';
import styles from './top-statistics.component.less';

export const TopStatistics = memo(() => {
  return (
    <section className={styles['top-statistics']}>
      <div className={styles['statistics-item']}>
        <span className={styles['statistics-item-timeIcon']}></span>
        <p className={styles['statistics-item-info']}>
          <span className={styles['statistics-item-info-title']}>系统本次运行时长</span>
          {/* 根据设定的阈值显示数据的颜色：正常-绿色，橙色和红色 */}
          <span
            style={{
              color: '#0AE6A0',
            }}
            className={`${styles['statistics-item-info-value']} ${styles['statistics-item-info-digital']}`}
          >
            01:20:18
          </span>
        </p>
      </div>
      <div className={styles['statistics-item']}>
        <span className={styles['statistics-item-waterIcon']}></span>
        <p className={styles['statistics-item-info']}>
          <span className={styles['statistics-item-info-title']}>集水井液位</span>
          {/* 根据设定的阈值显示数据的颜色：正常-绿色，橙色和红色 */}
          <span>
            <span
              style={{
                color: '#D49F2F',
              }}
              className={styles['statistics-item-info-value']}
            >
              2712.18
            </span>
            <span className={styles['statistics-item-info-unit']}>m</span>
          </span>
        </p>
      </div>
      <div className={styles['statistics-item']}>
        <span className={styles['statistics-item-changeIcon']}></span>
        <p className={styles['statistics-item-info']}>
          <span className={styles['statistics-item-info-title']}>液位变化速率</span>
          {/* 根据设定的阈值显示数据的颜色：正常-绿色，橙色和红色 */}
          <span>
            <span
              style={{
                color: '#CC514E',
              }}
              className={styles['statistics-item-info-value']}
            >
              54
            </span>
            <span className={styles['statistics-item-info-unit']}>m/h</span>
          </span>
        </p>
      </div>
    </section>
  );
});
