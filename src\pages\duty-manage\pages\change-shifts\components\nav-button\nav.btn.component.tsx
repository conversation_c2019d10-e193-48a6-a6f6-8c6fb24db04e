import { Button } from 'antd';
import { useMemo } from 'react';
import { ProcessButtonsStyle } from '@/constants/process-buttons.style';

export default function NavBtnComponent() {
  // 按钮点击事件
  const handleSaveData = () => {
    console.log('保存数据');
  };
  // 上一班
  const handlePreviousShift = () => {
    console.log('上一班');
  };

  // 当前班
  const handleCurrentShift = () => {
    console.log('当前班');
  };

  // 下一班
  const handleNextShift = () => {
    console.log('下一班');
  };

  // 打印
  const handlePrint = () => {
    console.log('打印');
  };

  // 按钮组
  const allsBtn = useMemo(
    () => [
      {
        text: '保存数据',
        click: handleSaveData,
      },
      {
        text: '上一班',
        click: handlePreviousShift,
      },
      {
        text: '当前班',
        click: handleCurrentShift,
      },
      {
        text: '下一班',
        click: handleNextShift,
      },
      {
        text: '打印',
        click: handlePrint,
      },
    ],
    [],
  );

  return (
    <ProcessButtonsStyle>
      {allsBtn.map((item) => {
        return (
          <Button key={item.text} onClick={item.click} type="primary">
            {item.text}
          </Button>
        );
      })}
    </ProcessButtonsStyle>
  );
}
