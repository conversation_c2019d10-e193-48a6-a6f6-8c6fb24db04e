import { useRef, useCallback, memo } from 'react';
import {
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  RedoOutlined,
  ExclamationCircleFilled,
  EditOutlined,
} from '@ant-design/icons';
import { Button, Space, DatePicker, Modal, message } from 'antd';
import styles from './detail.header.component.less';
import { PointAnalysis } from '@/pages/point/type';
import {
  icon_share,
  icon_collect,
  icon_del,
  icon_two,
  icon_four,
  icon_six,
  icon_two_active,
  icon_four_active,
  icon_six_active,
  icon_collect_active,
  icon_share_active,
} from '@/pages/point/point-chart/config';
import { history } from 'umi';
import dayjs from 'dayjs';
import {
  handleCollect,
  handleDelete,
  handleCancelShare,
  handleShareToOther,
  handleGetHistoryData,
} from '@/pages/point/point-chart/server';
import UserSelector from '@/components/user-selector/user.selector.component';
import { debounce } from 'lodash';
import _ from 'lodash';
import NewPointModal from '../add-legend/modal.component';
import { usePointAnalysisContext } from '@/pages/point/point-chart/point-analysis.provider';
import useQueryParams from '@/hooks/useQueryParams';
import { showLoading, hideLoading } from '@/utils/global-loading';
import { getHalfHourTime } from '@/pages/point/point-chart/utils';
interface IProps {
  addLegend: () => void; // 新建图例
  handleLayoutChange: (newLayout: 1 | 2 | 3) => void; // 更改布局
  layout: number; // 布局
}

const DetailTop = (props: IProps) => {
  // 路由传参
  const { id, type } = useQueryParams() as {
    id: string;
    type: 'check' | 'edit' | undefined;
  };
  const {
    addLegend, // 新建图例
    handleLayoutChange, // 更改布局
    layout, // 布局
  } = props;
  const { setState, detail, templates, searchDate, save, templateCount, getTime, getDetail } =
    usePointAnalysisContext();
  const userSelectorRef = useRef<React.ElementRef<typeof UserSelector>>(null); // 用户选择组件（点击分享）
  const modalRef = useRef<React.ElementRef<typeof NewPointModal>>(null); // 新建图例组件
  const isCheck = _.isEqual(type, 'check'); // 入口时查看还是编辑

  // 更改时间
  const onChangeDate = (dates: any, _dateStrings: [string, string]) => {
    setState({ searchDate: dates });
  };

  // 点击查询或重置，查询历史记录
  const handleSearch = useCallback(
    async (startTime: string, endTime: string) => {
      if (templateCount === 0) {
        message.warning('暂无图例，请先新建图例');
        return;
      }
      const params: PointAnalysis.HistoryQuery = {
        selectBegin: startTime,
        selectEnd: endTime,
        basePointList: templates.flatMap((template: any) => template.basePointDTOS),
      };
      showLoading();
      const data = await handleGetHistoryData(params);
      const newTem = templates.map((tem: any) => {
        return {
          ...tem,
          basePointDTOS: tem.basePointDTOS?.map((his: any) => {
            return {
              ...his,
              historyDatas: data?.find((__) => his.pointCode === __.pointCode)?.historyDatas || [],
            };
          }),
        };
      });
      setState({ templates: newTem });
      hideLoading();
    },
    [templates],
  );

  // 查询
  const search = debounce(async () => {
    const { startTime, endTime } = getTime('startTime', 'endTime');
    await handleSearch(startTime, endTime);
  }, 300);

  // 重置
  const resetDate = async () => {
    // 有详情时，使用详情的时间
    if (detail) {
      const { startTime, endTime } = detail;
      setState({
        searchDate: [dayjs(startTime), dayjs(endTime)],
      });
      await handleSearch(startTime, endTime);
    } else {
      // 无详情时，使用当前时间的前30分钟
      const [startTime, endTime] = getHalfHourTime();
      setState({
        searchDate: [startTime, endTime],
      });
      await handleSearch(
        startTime.format('YYYY-MM-DD HH:mm:ss'),
        endTime.format('YYYY-MM-DD HH:mm:ss'),
      );
    }
  };

  // 分享他人
  const handleShareRequest = useCallback(async (noticerIdList: string[]) => {
    const params = {
      noticerIdList,
      optionId: id,
    };
    await handleShareToOther(params, getDetail); // 执行分享请求
  }, []);

  // 分享图标点击
  const handleShare = debounce(async () => {
    if (detail?.myShare === 1) {
      await handleCancelShare({ id }, getDetail);
    } else {
      userSelectorRef.current?.openUserSelector();
    }
  }, 300);

  // 收藏图标点击
  const handleFavorite = debounce(async () => {
    const params = {
      optionId: id,
      myFavorites: detail?.myFavorites === 1 ? 0 : 1,
    };
    await handleCollect(params, getDetail);
  }, 300);

  // 删除图标点击
  const deleteItem = () => {
    Modal.confirm({
      title: '温馨提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确定删除此项?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => handleDelete(id, () => history.goBack()),
    });
  };

  // 编辑封面
  const handleEditCover = () => {
    const modalParams = detail
      ? {
          name: detail?.name,
          thumbnailId: detail?.thumbnailId,
        }
      : undefined;
    modalRef.current?.openModal('edit', modalParams);
  };

  const buttons = [
    {
      key: 'create',
      icon: <PlusOutlined />,
      onClick: debounce(addLegend, 300),
      text: '新建图例',
      disabled: isCheck,
    },
    {
      key: 'save',
      icon: <SaveOutlined />,
      onClick: debounce(async () => await save(), 300),
      text: '保存',
      disabled: isCheck,
    },
    {
      key: 'edit-cover-image',
      icon: <EditOutlined />,
      onClick: handleEditCover,
      text: '编辑封面',
      disabled: isCheck,
    },
    {
      key: 'date',
      type: 'date',
    },
    {
      key: 'search',
      icon: <SearchOutlined />,
      onClick: search,
      text: '查询',
      disabled: false,
    },
    {
      key: 'reset',
      icon: <RedoOutlined />,
      onClick: debounce(resetDate, 300),
      text: '重置',
      disabled: false,
    },
  ];

  const iconConfigs = [
    {
      onClick: handleShare,
      src: () => (detail?.myShare ? icon_share_active : icon_share),
      alt: '分享',
      visible: true,
    },
    {
      onClick: handleFavorite,
      src: () => (detail?.myFavorites ? icon_collect_active : icon_collect),
      alt: '收藏',
      visible: true,
    },
    {
      onClick: deleteItem,
      src: () => icon_del,
      alt: '删除',
      visible: !isCheck,
    },
    {
      onClick: () => handleLayoutChange(1),
      src: () => (layout === 1 ? icon_two_active : icon_two),
      alt: '单列',
      visible: true,
    },
    {
      onClick: () => handleLayoutChange(2),
      src: () => (layout === 2 ? icon_four_active : icon_four),
      alt: '双列',
      visible: true,
    },
    {
      onClick: () => handleLayoutChange(3),
      src: () => (layout === 3 ? icon_six_active : icon_six),
      alt: '三列',
      visible: true,
    },
  ];
  const renderButtons = () => {
    return buttons.map(({ key, icon, onClick, text, disabled, type }) => {
      if (type === 'date') {
        return (
          <DatePicker.RangePicker
            key={key}
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            onChange={onChangeDate}
            allowClear={false}
            value={searchDate}
          />
        );
      }
      if (disabled) return null;
      return (
        <Button key={key} type="primary" icon={icon} onClick={onClick} disabled={disabled}>
          {text}
        </Button>
      );
    });
  };
  const renderRightActions = () => {
    return iconConfigs
      .filter((config) => config.visible)
      .map((config, index) => (
        <img key={index} onClick={config.onClick} src={config.src()} alt={config.alt} />
      ));
  };

  return (
    <>
      <header className={styles.header}>
        <div className={styles.leftActions}>
          <Space size={10}>{renderButtons()}</Space>
        </div>
        <div className={styles.rightActions}>
          <Space size={16}>{renderRightActions()}</Space>
        </div>
      </header>
      <UserSelector okCallback={handleShareRequest} ref={userSelectorRef} />
      <NewPointModal
        ref={modalRef}
        callback={async (params: PointAnalysis.Add) => {
          await save(params);
        }}
      />
    </>
  );
};

export default memo(DetailTop);
