import { forwardRef, memo, useState, useImperativeHandle } from 'react';
import { Form, Select, Space, Button, message } from 'antd';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { setPointListUnit, addPointListUnit } from '@/pages/point/point.service';
import { findDictTree } from '@/pages/point/point.service';
import { useMount } from 'ahooks';
import { PointUnitDictionary } from '@/pages/point/type';
interface IProps {
  callback: () => Promise<void>;
  pointCode: string;
  unitId: string;
}
interface IRef {
  openModal: () => void;
}
const EditDetailModal = forwardRef<IRef, IProps>((props, ref) => {
  const { callback, pointCode, unitId } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [unitOptions, setUnitOptions] = useState<PointUnitDictionary[]>([]);
  useImperativeHandle(ref, () => ({
    openModal: () => {
      setVisible(true);
    },
  }));

  useMount(async () => {
    const res = await findDictTree('pointUnit'); // 获取字典配置测点值单位
    setUnitOptions(res.data?.[0]?.children || []);
  });

  // 新增确定
  const handleFinish = async () => {
    const valid = await form.validateFields();
    if (valid) {
      const param = {
        ...form.getFieldsValue(),
        pointCode,
      };
      const result = unitId
        ? await setPointListUnit({
            ...param,
            id: unitId,
          })
        : await addPointListUnit(param);
      if (result.code === '1') {
        message.success('设置成功');
        await callback();
        form.resetFields();
        setVisible(false);
      } else {
        message.error('设置失败');
      }
    }
  };

  return (
    <DragModal
      title="设置测点值单位"
      open={visible}
      onCancel={() => setVisible(false)}
      destroyOnClose // 关闭时销毁 Modal 里的子元素
      width={500}
      mask={true}
      centered
      forceRender // 强制渲染 Modal
      footer={false}
    >
      <Form form={form}>
        <Form.Item
          label="测点值单位"
          name="unitData"
          rules={[{ required: true, message: '请输入测点值单位' }]}
        >
          <Select
            options={unitOptions}
            showSearch
            optionFilterProp="label"
            fieldNames={{
              label: 'dictName',
              value: 'dictValue',
            }}
          />
        </Form.Item>
        <Form.Item
          label=""
          labelCol={{ span: 0 }}
          wrapperCol={{ span: 24 }}
          style={{ textAlign: 'right' }}
        >
          <Space size={10}>
            <Button onClick={() => setVisible(false)}>取消</Button>
            <Button type="primary" onClick={handleFinish}>
              确定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </DragModal>
  );
});

export default memo(EditDetailModal);
