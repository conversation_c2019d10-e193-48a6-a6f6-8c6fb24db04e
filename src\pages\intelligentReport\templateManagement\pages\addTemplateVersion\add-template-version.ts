import {
  AuthoritieOptions,
  InfluxesOptions,
  RegionsOptions,
} from '@/components/spread-table/spread-table';
import { TabsProps } from 'antd';

export const TabItems: TabsProps['items'] = [
  {
    key: '1',
    label: '基本信息',
  },
  {
    key: '2',
    label: '数据管理',
  },
];

export interface DetailOptions {
  id: string;
  templateId: string;
  versionName: string; //模板名称
  versionState: string; //版本状态;0--未发布；1--已发布
  templateData: string; //模板json数据
  createDate: string;
  createUserId: string;
  createUserName: string;
  authorities: AuthoritieOptions[]; //权限列表
  influxes: InfluxesOptions[]; //测点类型列表
  regions: RegionsOptions[]; //计划、历史列表
}
//发布/暂存配置
export interface ReleaseOptions {
  id?: string;
  templateId: string;
  versionName: string; //模板名称
  versionState: number; //版本状态;0--未发布；1--已发布
  templateData: string; //模板json数据
  authorities: AuthoritieOptions[]; //权限列表
  influxes: InfluxesOptions[]; //测点类型列表
  regions: RegionsOptions[]; //计划、历史列表
}
// worker主线程代码
const workerCode = `
  self.onmessage = function(e) {
    const { influxesList,permisList, planList, historyList } = e.data;
  const processedData = {
    authorities: permisList
      .filter((item) => item.startPosition && item.endPosition && !item.isEdit)
      .map((item) => ({ ...item, id: item.templateId ? item.id : undefined })),
      influxes:influxesList
      .filter((item) => item.startPosition && item.endPosition && !item.isEdit)
      .map((item) => ({ ...item, id: item.templateId ? item.id : undefined })),
    regions: [...planList, ...historyList]
      .filter((item) => item.startPosition && item.endPosition && !item.isEdit)
      .map((item) => ({ ...item, id: item.templateId ? item.id : undefined })),
  };
  self.postMessage(processedData);
  };
`;
const blob = new Blob([workerCode], { type: 'application/javascript' });
export const workerUrl = URL.createObjectURL(blob);
