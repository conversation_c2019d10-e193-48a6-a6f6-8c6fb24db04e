.button-modal-container {
  display: flex;
  column-gap: 50px;
  margin: 40px 0px 0px 28px;
  position: relative;
}
.button-modal-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  transition: all 0.3s;
  &:hover {
    cursor: pointer;
    opacity: 0.8;
  }
  &-icon {
    display: inline;
    width: 52px;
    height: 52px;
    background-size: contain;
    background-repeat: no-repeat;
  }
  &-label {
    font-family: DingTalk;
    text-shadow: 0 0 3px #fff;
    font-size: 16px;
  }
}
.warning-icon {
  background: url(~@/assets/images/leakDetection/icon_alarm_red.png);
}
.trend-icon {
  background: url(~@/assets/images/leakDetection/icon_trend.png);
}
.event-icon {
  background: url(~@/assets/images/leakDetection/icon_event.png);
}
.log-icon {
  background: url(~@/assets/images/leakDetection/icon_log.png);
}
