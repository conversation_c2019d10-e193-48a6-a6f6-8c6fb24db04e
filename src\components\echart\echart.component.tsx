import { useEffect } from 'react';
import type { EChartsOption } from 'echarts';
import { useCharts } from '@/hooks/useCharts';
import { useSize } from 'ahooks';

interface ChartsProps {
  options: EChartsOption;
  className?: string;
  style?: React.CSSProperties;
}

const Charts: React.FC<ChartsProps> = ({
  options, // 图表配置
  className, // 样式类名
  style, // 样式
}) => {
  const [chartRef, chartInstance] = useCharts(); // 图表实例 hooks
  const size = useSize(chartRef); // 容器大小

  // 监听 options 变化，更新图表
  useEffect(() => {
    if (chartInstance && options) {
      chartInstance.setOption(options); // 更新图表配置
    }
  }, [chartInstance, options]);

  // 监听容器大小变化，调整图表大小
  useEffect(() => {
    if (chartInstance && size) {
      chartInstance.resize(); // 调整图表大小
    }
  }, [chartInstance, size]);

  return (
    <div
      ref={chartRef} // 图表实例
      className={className} // 样式类名
      style={{ width: '100%', height: '100%', ...style }} // 样式
    />
  );
};
export default Charts;
