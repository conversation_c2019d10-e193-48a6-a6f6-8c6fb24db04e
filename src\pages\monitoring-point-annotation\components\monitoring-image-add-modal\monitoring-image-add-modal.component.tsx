import { FC, memo, useEffect, useRef, useState } from 'react';
import type { ImageParams, PropOptions } from './monitoring-image-add-modal';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import styles from './monitoring-image-add-modal.component.less';
import { Form, Input, message } from 'antd';
import { UploadComponent } from '@/components/upload-file/upload-file.component';
import { addImageNode, updateImageNode } from '@/services/monitoring-point-annotation';
import { FileOptions } from '@/services/system';
import { ControllerType } from '../monitoring-point/monitoring-point';

export const MonitoringImageAdd: FC<PropOptions> = memo(({ type, nodeData, close, open, title, onOk }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm();
  const uploadRef = useRef<any>(null);
  useEffect(() => {
    if (open && type === ControllerType.EDIT) {
      formDataShowHandler();
    }
    if (open) {
      //页面刷新或者退出时，删除已上传文件
      window.addEventListener('beforeunload', () => delFile(1));
    } else {
      window.removeEventListener('beforeunload', () => delFile(1));
    }
    return () => {
      window.removeEventListener('beforeunload', () => delFile(1));
    };
  }, [open]);
  //删除文件
  function delFile(status: number) {
    switch (status) {
      //删除已上传但不需要的文件
      case 0:
        uploadRef.current.fileRef.current.sureDelFile();
        break;
      //删除已上传但未提交的文件
      case 1:
        uploadRef.current.fileRef.current.initDelFile();
        break;
    }
  }
  //表单数据回显处理
  function formDataShowHandler(): void {
    forms.setFieldsValue({
      name: nodeData.name,
      attachId: nodeData.attachId,
    });
  }
  //表单数据处理
  function formDataHandler(values: any): ImageParams {
    const { attachmentId, fileList } = values.attachId;
    return {
      attachId: attachmentId,
      name: values.name,
      imgId: fileList.map((item: FileOptions) => item.id).join(','),
      parentId: type === ControllerType.ADD ? nodeData.id : nodeData.parentId,
      id: type === ControllerType.EDIT ? nodeData.id : undefined,
    };
  }
  function closeHandler(): void {
    delFile(1);
    resetForm();
    close();
  }
  //重置表单
  function resetForm(): void {
    forms.resetFields();
  }
  //新增请求
  function addRequest(data: ImageParams): void {
    setLoading(true);
    addImageNode<ImageParams>(data)
      .then((res) => {
        if (res.code == '1') {
          resetForm();
          delFile(0);
          message.success('添加成功');
          onOk && onOk('image');
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //编辑请求
  function updateRequest(data: ImageParams): void {
    setLoading(true);
    updateImageNode<ImageParams>(data, nodeData.id)
      .then((res) => {
        if (res.code == '1') {
          resetForm();
          delFile(0);
          message.success('更新成功');
          onOk && onOk('image');
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //提交数据
  function commit(): void {
    forms.validateFields().then((values) => {
      const data = formDataHandler(values);
      if (type === ControllerType.ADD) {
        addRequest(data);
      } else {
        updateRequest(data);
      }
    });
  }
  return (
    <SvgModal
      confirmText="保存"
      cancelText="取消"
      width="500px"
      height="350px"
      close={closeHandler}
      open={open}
      title={title + '设备图片'}
      confirmloading={loading}
      onOk={commit}
    >
      <div className={styles['point-add-form']}>
        <Form labelCol={{ span: 5 }} form={forms}>
          <Form.Item<ImageParams> label="上传图片" name="attachId" rules={[{ required: true, message: '请上传图片' }]}>
            <UploadComponent ref={uploadRef} maxcount={1}></UploadComponent>
          </Form.Item>
          <Form.Item<ImageParams> label="图片名称" name="name" rules={[{ required: true, message: '请输入图片名称' }]}>
            <Input placeholder="请输入..." />
          </Form.Item>
        </Form>
      </div>
    </SvgModal>
  );
});
