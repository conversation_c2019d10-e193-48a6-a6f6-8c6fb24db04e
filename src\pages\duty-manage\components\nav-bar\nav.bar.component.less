.navBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #2c4c7f;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.leftSection {
  flex: 1;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 1.4;
}

.rightSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.customComponent {
  display: flex;
  align-items: center;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  .backButtonText {
    font-size: 14px;
    color: #058ad1;
  }
  svg {
    color: #058ad1;
  }
}
