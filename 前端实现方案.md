# 三维配置功能前端实现方案

## 项目技术栈分析

通过分析项目代码，我发现这是一个基于以下技术栈的React项目：

- **前端框架**: React 18.2.0 + TypeScript + UmiJS 3.5.39
- **UI组件库**: Ant Design 5.17.2
- **三维引擎**: 自定义的 `bim-air-plugin` 组件（基于WebGL的BIM模型渲染引擎）
- **状态管理**: Redux + DVA
- **实时通信**: WebSocket + STOMP协议
- **图表库**: ECharts 5.4.3, AntV G2/G2Plot
- **动画库**: Framer Motion 12.4.7

## 三维配置功能前端实现方案

### 一、三维场景配置模块

#### 1.1 视角预设管理

基于现有的BIM引擎，我建议创建以下组件结构：

```typescript
// src/pages/3d-config/components/camera-preset/camera-preset.component.tsx
interface CameraPreset {
  id: string;
  name: string;
  position: number[];
  target: number[];
  up: number[];
  width: number;
  height: number;
  type: 'inspection' | 'global' | 'detail';
}

const CameraPresetManager: FC = () => {
  const [presets, setPresets] = useState<CameraPreset[]>([]);
  const [currentPreset, setCurrentPreset] = useState<CameraPreset | null>(null);
  
  // 保存当前视角为预设
  const saveCurrentView = () => {
    if (bimView) {
      const preset: CameraPreset = {
        id: generateId(),
        name: `预设_${Date.now()}`,
        position: bimView.camera.position,
        target: bimView.camera.target,
        up: bimView.camera.up,
        width: bimView.camera.width,
        height: bimView.camera.height,
        type: 'custom'
      };
      setPresets([...presets, preset]);
    }
  };
  
  // 应用预设视角
  const applyPreset = (preset: CameraPreset) => {
    if (bimView) {
      setCamera(bimView, preset);
    }
  };
  
  return (
    <div className="camera-preset-manager">
      <Button onClick={saveCurrentView}>保存当前视角</Button>
      <List
        dataSource={presets}
        renderItem={(preset) => (
          <List.Item
            actions={[
              <Button onClick={() => applyPreset(preset)}>应用</Button>,
              <Button danger onClick={() => deletePreset(preset.id)}>删除</Button>
            ]}
          >
            <List.Item.Meta title={preset.name} description={preset.type} />
          </List.Item>
        )}
      />
    </div>
  );
};
```

#### 1.2 标点配置功能

基于现有的标点管理代码，我建议扩展以下功能：

```typescript
// src/pages/3d-config/components/point-config/point-config.component.tsx
interface PointConfig {
  id: string;
  name: string;
  shape: {
    type: 'circle' | 'square' | 'triangle' | 'custom';
    size: number;
    rotation: number;
    customIcon?: string; // SVG/PNG路径
    anchorPoint: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  };
  position: {
    type: '3d' | 'component';
    coordinates?: [number, number, number];
    componentId?: string;
  };
  style: {
    primaryColor: string;
    alertColor: string;
    fontSize: number;
    fontWeight: number;
    textShadow: boolean;
    gradientTransition: boolean;
  };
  dataConnection: {
    type: 'websocket' | 'restful';
    websocket?: WebSocketConfig;
    restful?: RestfulConfig;
  };
}

interface WebSocketConfig {
  protocol: 'ws' | 'wss';
  url: string;
  port: number;
  heartbeatInterval: number;
  authToken?: string;
  messageFormat: 'json' | 'protobuf';
  dataMapping: Record<string, string>;
  reconnect: {
    enabled: boolean;
    maxRetries: number;
    interval: number;
  };
}

const PointConfigManager: FC = () => {
  const [points, setPoints] = useState<PointConfig[]>([]);
  const [selectedPoint, setSelectedPoint] = useState<PointConfig | null>(null);
  const [configMode, setConfigMode] = useState<'create' | 'edit'>('create');
  
  // 创建新标点
  const createPoint = (config: PointConfig) => {
    // 在三维场景中创建标点
    if (bimView) {
      const pointElement = createPointElement(config);
      bimView.addOverlay(pointElement);
    }
    setPoints([...points, config]);
  };
  
  // 拖拽定位标点
  const enableDragPositioning = (pointId: string) => {
    // 启用拖拽模式，监听鼠标事件
    bimView.onLButtonDown.add((sender, event) => {
      const worldPosition = event.viewer.screenToWorld(event.x, event.y);
      updatePointPosition(pointId, worldPosition);
    });
  };
  
  return (
    <div className="point-config-manager">
      <Tabs>
        <TabPane tab="基础属性" key="basic">
          <PointBasicConfig 
            point={selectedPoint}
            onChange={updatePointConfig}
          />
        </TabPane>
        <TabPane tab="数据连接" key="data">
          <PointDataConfig 
            point={selectedPoint}
            onChange={updatePointConfig}
          />
        </TabPane>
        <TabPane tab="动画绑定" key="animation">
          <PointAnimationConfig 
            point={selectedPoint}
            onChange={updatePointConfig}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};
```

### 二、业务配置模块

```typescript
// src/pages/3d-config/components/business-config/business-config.component.tsx
interface BusinessModule {
  id: string;
  name: string;
  code: string;
  modelId: string;
  routeConfig: {
    path: string;
    permissions: string[];
  };
  description?: string;
}

const BusinessConfigManager: FC = () => {
  const [businessModules, setBusinessModules] = useState<BusinessModule[]>([]);
  const [modelList, setModelList] = useState<any[]>([]);
  
  // 创建业务模块
  const createBusinessModule = (module: BusinessModule) => {
    // 验证业务编码唯一性
    const exists = businessModules.some(m => m.code === module.code);
    if (exists) {
      message.error('业务编码已存在');
      return;
    }
    
    setBusinessModules([...businessModules, module]);
    message.success('业务模块创建成功');
  };
  
  // 绑定模型与业务
  const bindModelToBusiness = (modelId: string, businessId: string) => {
    // 更新业务模块的模型绑定
    setBusinessModules(modules => 
      modules.map(m => 
        m.id === businessId ? { ...m, modelId } : m
      )
    );
  };
  
  return (
    <div className="business-config-manager">
      <Card title="业务模块管理">
        <Form onFinish={createBusinessModule}>
          <Form.Item name="name" label="业务名称" rules={[{ required: true }]}>
            <Input placeholder="请输入业务名称" />
          </Form.Item>
          <Form.Item name="code" label="业务编码" rules={[{ required: true }]}>
            <Input placeholder="请输入业务编码" />
          </Form.Item>
          <Form.Item name="modelId" label="关联模型">
            <Select placeholder="选择关联模型">
              {modelList.map(model => (
                <Option key={model.id} value={model.id}>{model.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="routePath" label="访问路径">
            <Input placeholder="/api/model/{modelId}/business/{bizCode}" />
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};
```

### 三、动画管理模块

基于Framer Motion库，实现动画管理系统：

```typescript
// src/pages/3d-config/components/animation-manager/animation-manager.component.tsx
interface AnimationConfig {
  id: string;
  name: string;
  type: 'basic' | 'business';
  category: 'movement' | 'rotation' | 'scale' | 'opacity' | 'alert' | 'equipment';
  keyframes: Keyframe[];
  duration: number;
  easing: string;
  loop: boolean;
  triggers: AnimationTrigger[];
}

interface AnimationTrigger {
  type: 'state_change' | 'threshold' | 'time_cycle' | 'user_action';
  condition: any;
  dataBinding?: {
    pointCode: string;
    valueMapping: Record<string, any>;
  };
}

const AnimationManager: FC = () => {
  const [animations, setAnimations] = useState<AnimationConfig[]>([]);
  const [selectedAnimation, setSelectedAnimation] = useState<AnimationConfig | null>(null);

  // 预设动画库
  const presetAnimations = {
    basic: {
      translate: {
        name: '平移动画',
        keyframes: [
          { x: 0, y: 0, z: 0 },
          { x: 100, y: 0, z: 0 }
        ]
      },
      rotate: {
        name: '旋转动画',
        keyframes: [
          { rotateZ: 0 },
          { rotateZ: 360 }
        ]
      },
      scale: {
        name: '缩放动画',
        keyframes: [
          { scale: 1 },
          { scale: 1.2 },
          { scale: 1 }
        ]
      }
    },
    business: {
      alert: {
        name: '报警闪烁',
        keyframes: [
          { opacity: 1, backgroundColor: '#ff0000' },
          { opacity: 0.3, backgroundColor: '#ff6666' },
          { opacity: 1, backgroundColor: '#ff0000' }
        ]
      },
      equipment: {
        name: '设备转动',
        keyframes: [
          { rotateZ: 0 },
          { rotateZ: 360 }
        ],
        loop: true
      }
    }
  };

  // 创建动画
  const createAnimation = (config: AnimationConfig) => {
    setAnimations([...animations, config]);
  };

  // 应用动画到标点
  const applyAnimationToPoint = (pointId: string, animationId: string) => {
    const animation = animations.find(a => a.id === animationId);
    if (animation) {
      // 使用Framer Motion应用动画
      const pointElement = document.getElementById(`point-${pointId}`);
      if (pointElement) {
        // 动态创建motion组件并应用动画
        const motionConfig = {
          animate: animation.keyframes,
          transition: {
            duration: animation.duration,
            ease: animation.easing,
            repeat: animation.loop ? Infinity : 0
          }
        };
        // 应用动画配置
      }
    }
  };

  return (
    <div className="animation-manager">
      <Tabs>
        <TabPane tab="动画库" key="library">
          <AnimationLibrary
            animations={animations}
            presets={presetAnimations}
            onSelect={setSelectedAnimation}
          />
        </TabPane>
        <TabPane tab="动画编辑器" key="editor">
          <AnimationEditor
            animation={selectedAnimation}
            onChange={updateAnimation}
          />
        </TabPane>
        <TabPane tab="数据绑定" key="binding">
          <DataAnimationBinding
            animation={selectedAnimation}
            onChange={updateAnimationBinding}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};
```

### 四、实时数据处理

基于现有的WebSocket系统，扩展实时数据处理：

```typescript
// src/pages/3d-config/components/data-manager/real-time-data-manager.component.tsx
interface DataProcessor {
  pointCode: string;
  dataMapping: {
    sourceField: string;
    targetField: string;
    transform?: (value: any) => any;
  }[];
  unitConversion?: {
    from: string;
    to: string;
    factor: number;
  };
  thresholds: {
    normal: [number, number];
    warning: [number, number];
    alert: [number, number];
  };
}

const RealTimeDataManager: FC = () => {
  const [dataProcessors, setDataProcessors] = useState<DataProcessor[]>([]);
  const [realtimeData, setRealtimeData] = useState<Record<string, any>>({});

  // 扩展现有的WebSocket处理
  const enhancedWebSocketHandler = useCallback((socketData: any) => {
    const { key, value, timestamp } = socketData;

    // 查找对应的数据处理器
    const processor = dataProcessors.find(p => key.includes(p.pointCode));
    if (processor) {
      // 数据映射和转换
      const processedValue = processor.dataMapping.reduce((acc, mapping) => {
        if (mapping.transform) {
          acc[mapping.targetField] = mapping.transform(value);
        } else {
          acc[mapping.targetField] = value;
        }
        return acc;
      }, {} as any);

      // 单位转换
      if (processor.unitConversion) {
        processedValue.convertedValue = value * processor.unitConversion.factor;
      }

      // 阈值判断
      const status = getDataStatus(value, processor.thresholds);
      processedValue.status = status;

      // 更新实时数据
      setRealtimeData(prev => ({
        ...prev,
        [processor.pointCode]: {
          ...processedValue,
          timestamp,
          rawValue: value
        }
      }));

      // 触发动画
      triggerDataDrivenAnimation(processor.pointCode, processedValue);
    }
  }, [dataProcessors]);

  // 数据状态判断
  const getDataStatus = (value: number, thresholds: any) => {
    if (value >= thresholds.alert[0] && value <= thresholds.alert[1]) {
      return 'alert';
    } else if (value >= thresholds.warning[0] && value <= thresholds.warning[1]) {
      return 'warning';
    }
    return 'normal';
  };

  // 触发数据驱动的动画
  const triggerDataDrivenAnimation = (pointCode: string, data: any) => {
    const pointElement = document.getElementById(`point-${pointCode}`);
    if (pointElement) {
      // 根据数据状态应用不同的动画
      switch (data.status) {
        case 'alert':
          // 应用报警动画
          pointElement.classList.add('alert-animation');
          break;
        case 'warning':
          // 应用警告动画
          pointElement.classList.add('warning-animation');
          break;
        default:
          // 移除所有动画类
          pointElement.classList.remove('alert-animation', 'warning-animation');
      }
    }
  };

  return (
    <div className="real-time-data-manager">
      <Card title="实时数据监控">
        <Table
          dataSource={Object.entries(realtimeData).map(([key, value]) => ({
            pointCode: key,
            ...value
          }))}
          columns={[
            { title: '测点编码', dataIndex: 'pointCode' },
            { title: '当前值', dataIndex: 'convertedValue' },
            { title: '状态', dataIndex: 'status' },
            { title: '更新时间', dataIndex: 'timestamp' }
          ]}
        />
      </Card>
    </div>
  );
};
```

### 五、配置管理系统

```typescript
// src/pages/3d-config/3d-config.page.tsx
const ThreeDConfigPage: FC = () => {
  const [activeTab, setActiveTab] = useState('scene');
  const [configData, setConfigData] = useState<any>({});
  const [previewMode, setPreviewMode] = useState(false);

  // 配置数据保存
  const saveConfig = async () => {
    try {
      const result = await saveThreeDConfig(configData);
      if (result.success) {
        message.success('配置保存成功');
      }
    } catch (error) {
      message.error('配置保存失败');
    }
  };

  // 配置导出
  const exportConfig = () => {
    const dataStr = JSON.stringify(configData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `3d-config-${Date.now()}.json`;
    link.click();
  };

  // 配置导入
  const importConfig = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        setConfigData(config);
        message.success('配置导入成功');
      } catch (error) {
        message.error('配置文件格式错误');
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="three-d-config-page">
      <div className="config-header">
        <Space>
          <Button type="primary" onClick={saveConfig}>保存配置</Button>
          <Button onClick={exportConfig}>导出配置</Button>
          <Upload
            accept=".json"
            showUploadList={false}
            beforeUpload={(file) => {
              importConfig(file);
              return false;
            }}
          >
            <Button>导入配置</Button>
          </Upload>
          <Switch
            checked={previewMode}
            onChange={setPreviewMode}
            checkedChildren="预览模式"
            unCheckedChildren="编辑模式"
          />
        </Space>
      </div>

      <div className="config-content">
        <div className="config-sidebar">
          <Tabs
            tabPosition="left"
            activeKey={activeTab}
            onChange={setActiveTab}
          >
            <TabPane tab="场景配置" key="scene">
              <SceneConfigPanel
                config={configData.scene}
                onChange={(scene) => setConfigData({...configData, scene})}
                previewMode={previewMode}
              />
            </TabPane>
            <TabPane tab="标点管理" key="points">
              <PointConfigPanel
                config={configData.points}
                onChange={(points) => setConfigData({...configData, points})}
                previewMode={previewMode}
              />
            </TabPane>
            <TabPane tab="业务配置" key="business">
              <BusinessConfigPanel
                config={configData.business}
                onChange={(business) => setConfigData({...configData, business})}
                previewMode={previewMode}
              />
            </TabPane>
            <TabPane tab="动画管理" key="animation">
              <AnimationConfigPanel
                config={configData.animation}
                onChange={(animation) => setConfigData({...configData, animation})}
                previewMode={previewMode}
              />
            </TabPane>
          </Tabs>
        </div>

        <div className="config-preview">
          <ThreeDPreview
            config={configData}
            previewMode={previewMode}
          />
        </div>
      </div>
    </div>
  );
};
```

## 总结

基于您的项目现状和需求文档，我建议的前端实现方案包括：

### 技术架构
1. **基于现有技术栈**：利用React + TypeScript + UmiJS + Ant Design
2. **扩展BIM引擎**：基于现有的`bim-air-plugin`组件进行功能扩展
3. **动画系统**：使用Framer Motion实现复杂动画效果
4. **实时通信**：扩展现有的WebSocket + STOMP系统

### 核心功能模块
1. **三维场景配置**：视角预设、光照系统、材质编辑
2. **标点配置系统**：形状配置、位置设置、数据连接
3. **业务模块管理**：模型业务绑定、路由配置
4. **动画管理系统**：动画库、可视化编辑器、数据绑定
5. **实时数据处理**：数据映射、阈值判断、历史回溯

### 开发建议
1. **模块化开发**：每个功能模块独立开发，便于维护和扩展
2. **配置驱动**：所有配置通过JSON格式存储，支持导入导出
3. **实时预览**：提供配置效果的实时预览功能
4. **权限控制**：集成现有的权限系统，支持按业务模块划分权限

### 实现步骤建议

#### 第一阶段：基础框架搭建
1. 创建三维配置主页面和路由
2. 搭建配置管理的基础架构
3. 实现配置数据的持久化机制

#### 第二阶段：场景配置功能
1. 开发视角预设管理组件
2. 实现光照系统配置界面
3. 开发材质编辑器

#### 第三阶段：标点管理系统
1. 开发标点基础属性配置组件
2. 实现数据连接配置功能
3. 创建标点管理器

#### 第四阶段：动画系统
1. 开发动画库管理组件
2. 实现可视化动画编辑器
3. 开发数据动画绑定系统

#### 第五阶段：业务集成
1. 开发业务模块管理组件
2. 实现模型业务绑定功能
3. 集成实时数据处理系统

#### 第六阶段：功能完善
1. 开发路径规划组件
2. 实现历史数据回溯功能
3. 完善配置预览功能

### 技术要点

#### 1. BIM引擎集成
- 基于现有的`bim-air-plugin`组件进行扩展
- 利用现有的相机控制、模型加载等功能
- 扩展标点渲染和动画播放能力

#### 2. 数据流管理
- 扩展现有的WebSocket数据处理机制
- 实现数据映射和转换功能
- 支持多种数据源的统一管理

#### 3. 动画系统设计
- 使用Framer Motion提供流畅的动画效果
- 支持关键帧动画和数据驱动动画
- 实现动画的可视化编辑和预览

#### 4. 配置管理
- 采用JSON格式存储所有配置信息
- 支持配置的导入导出和版本管理
- 提供配置效果的实时预览

这个方案充分利用了您现有的技术基础，同时满足了需求文档中的所有功能要求。通过模块化的开发方式，可以逐步实现各个功能，降低开发风险，提高代码的可维护性。
