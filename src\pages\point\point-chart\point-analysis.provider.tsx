import { createContext, useContext, ReactNode, useMemo, useCallback } from 'react';
import type { SetState } from 'ahooks/es/useSetState';
import { useSetState } from 'ahooks';
import dayjs from 'dayjs';
import { PointAnalysis, Pointlist } from '@/pages/point/type';
import useQueryParams from '@/hooks/useQueryParams';
import _ from 'lodash';
import { handleGetHistoryData, handleGetPointAnalysisDetail, handleSave } from './server';
import { message } from 'antd';
import { showLoading, hideLoading } from '@/utils/global-loading';

// 类型定义
export type TimeKey = 'startTime' | 'selectBegin' | 'endTime' | 'selectEnd';
export type TemplateType = 'db' | 'new';
export interface HistoryData {
  time: string;
  value: number;
  [key: string]: any;
}

export interface IBasePointDTO extends Pointlist.List {
  historyDatas: HistoryData[];
}

export interface ITemplate {
  ord: number;
  name: string;
  basePointDTOS: IBasePointDTO[];
  type: TemplateType;
  chartType?: string; // 折线图类型
}

export interface IState {
  searchDate: [dayjs.Dayjs, dayjs.Dayjs];
  detail: PointAnalysis.Detail | null;
  templates: ITemplate[];
}

export interface IContext extends IState {
  setState: SetState<IState>;
  getTime: (startKey: TimeKey, endKey: TimeKey) => Record<string, string>;
  getDetail: (init?: boolean) => Promise<void>;
  save: (editParams?: Partial<PointAnalysis.Add>) => Promise<void>;
  saveSync: (templates: ITemplate[]) => Promise<void>;
  templateCount: number;
}

// Context 创建
const PointAnalysisContext = createContext<IContext | undefined>(undefined);
PointAnalysisContext.displayName = 'PointAnalysisContext';

// 初始状态
const initialState: IState = {
  searchDate: [dayjs().subtract(30, 'minutes'), dayjs()],
  detail: null,
  templates: [],
};

// Provider 组件
const Provider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useSetState<IState>(initialState);
  const { id } = useQueryParams() as { id: string | undefined };

  // 计算属性
  const templateCount = useMemo(() => state.templates.length, [state.templates]);

  // 工具函数
  const getTime = useCallback(
    (startKey: TimeKey, endKey: TimeKey) => ({
      [startKey]: state.searchDate[0].format('YYYY-MM-DD HH:mm:ss'),
      [endKey]: state.searchDate[1].format('YYYY-MM-DD HH:mm:ss'),
    }),
    [state.searchDate],
  );

  // 数据处理函数
  const processHistoryData = async (
    templates: ITemplate[],
    timeRange: { startTime: string; endTime: string },
  ) => {
    const basePointList = templates.flatMap((template) => template.basePointDTOS);

    if (_.isEmpty(basePointList)) return templates;

    const params: PointAnalysis.HistoryQuery = {
      selectBegin: timeRange.startTime,
      selectEnd: timeRange.endTime,
      basePointList,
    };
    return new Promise<ITemplate[]>((resolve) => {
      handleGetHistoryData(params, (historyData) => {
        const processedTemplates = templates.map((template) => ({
          ...template,
          type: 'db' as const, // 区分新建和数据库中的数据，便于删除操作
          basePointDTOS: template.basePointDTOS.map((point) => ({
            ...point,
            historyDatas:
              historyData?.find((h) => h.pointCode === point.pointCode)?.historyDatas || [],
          })),
          // 对曾经设置过图表类型进行初始化
          ...(!template?.chartType
            ? {
                chartType: ['遥信', 'state'].includes(template.basePointDTOS[0]?.pointType)
                  ? 'state'
                  : '',
              }
            : {}),
        }));
        resolve(processedTemplates);
      });
    });
  };

  // 业务函数
  const initTemplates = async (data: PointAnalysis.Detail) => {
    const { startTime, endTime, analyseData } = data;
    const jsonAnalyseData = JSON.parse(analyseData) as ITemplate[];

    // 设置时间
    if (startTime && endTime) {
      setState({ searchDate: [dayjs(startTime), dayjs(endTime)] });
    }

    // 判空
    if (_.isEmpty(jsonAnalyseData)) {
      setState({ templates: [] });
      return;
    }

    // 处理历史数据
    const processedTemplates = await processHistoryData(jsonAnalyseData, { startTime, endTime });
    setState({ templates: processedTemplates });
  };

  // 获取详情
  const getDetail = async (init = false) => {
    if (!id) return;

    await handleGetPointAnalysisDetail(id, async (data) => {
      setState({ detail: data });
      // init 是否初始化
      init && (await initTemplates(data));
    });
  };

  // 保存（新建图例，编辑封面）
  const save = async (formParams?: Partial<PointAnalysis.Add>) => {
    if (!formParams && templateCount === 0) {
      message.warning('暂无图例，请先新建图例');
      return;
    }

    const params = {
      id: id as string,
      analyseDataDTOList: state.templates,
      name: state.detail?.name,
      thumbnailId: state.detail?.thumbnailId,
      ...getTime('startTime', 'endTime'),
      ...(formParams || {}),
    };

    showLoading();
    await handleSave(params as PointAnalysis.Update);
    await getDetail(true);
    hideLoading();
  };

  // 保存（设置最大最小限值）
  const saveSync = async (templates: ITemplate[]) => {
    const params = {
      ...state.detail,
      analyseDataDTOList: templates,
    };

    showLoading();
    await handleSave(params as PointAnalysis.Update);
    await getDetail(true);
    hideLoading();
  };

  const contextValue: IContext = {
    ...state,
    setState,
    getTime,
    getDetail,
    save,
    saveSync,
    templateCount,
  };

  return (
    <PointAnalysisContext.Provider value={contextValue}>{children}</PointAnalysisContext.Provider>
  );
};

// Hook
export const usePointAnalysisContext = (): IContext => {
  const context = useContext(PointAnalysisContext);
  if (!context) {
    throw new Error('usePointAnalysisContext must be used within an PointAnalysisProvider');
  }
  return context;
};

export default Provider;
