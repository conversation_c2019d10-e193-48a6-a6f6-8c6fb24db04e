import { Empty } from 'antd';
import styles from './empty-component.less';
interface Props {
  image?: any;
  description?: string;
}
const EmptyComponent: React.FC<Props> = ({ image = Empty.PRESENTED_IMAGE_SIMPLE, description = '暂无数据' }) => {
  return (
    <div className={styles.emptyBox}>
      <Empty image={image} className={styles.empty} description={description} />
    </div>
  );
};

export default EmptyComponent;
