import React, { memo, useEffect, useState, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import { shallowEqual } from 'react-redux';
import { useSelector, useDispatch } from 'umi';
import { message, Table } from 'antd';
import type { TableProps } from 'antd';

import { DetailTableWrapper } from './detail-table.style';
import { useTableScrollerHeight } from '@/hooks';
import { getList } from '../../electricity-calculation.service';
import type { IRootState, IState } from '../../models/electricityCalculation';
import type { IPageInfo } from '@/global';

export const DEFAULT_PAGE_SIZE = 10;

const initalPageInfo = {
  pageNum: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

export type RecordType = any;

interface IProps {
  children?: ReactNode;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const DetailTable: FC<IProps> = (props) => {
  const { setVisible } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<RecordType[]>([]);
  const [pageInfo, setPageInfo] = useState<IPageInfo>(initalPageInfo);

  const { queryInfo } = useSelector<IRootState, Pick<IState, 'queryInfo'>>(
    ({ electricityCalculation: state }) => ({
      queryInfo: state.queryInfo,
    }),
    shallowEqual,
  );

  const dispatch = useDispatch();

  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    getDataList(queryInfo);
  }, [queryInfo]);

  const getDataList = (
    queryParams: any,
    pageParams: Omit<IPageInfo, 'total'> = {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  ) => {
    setLoading(true);
    getList({ ...queryParams, ...pageParams })
      .then((res: any) => {
        const { list, pageNum, pageSize, total } = res.data;
        setDataSource(list);
        setPageInfo({
          pageNum,
          pageSize,
          total: Number(total),
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCalc = (record: RecordType) => {
    setVisible(true);
    dispatch({
      type: 'electricityCalculation/changeRecord',
      payload: record,
    });
  };

  const handlePaginationChange = (pageNum: number, pageSize: number) => {
    getDataList(queryInfo, { pageNum, pageSize });
  };

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: '_',
      align: 'center',
      title: '序号',
      dataIndex: '_',
      width: '20%',
      render: (_, __, index) => index + 1,
    },
    {
      key: 'name',
      align: 'center',
      title: '名称',
      dataIndex: 'name',
      width: '50%',
    },
    {
      key: 'operations',
      align: 'center',
      title: '操作',
      render: (_, record: RecordType) => renderOperations(record),
    },
  ];

  const renderOperations = (record: RecordType) => {
    return <a onClick={() => handleCalc(record)}>计算</a>;
  };

  const tableContainerRef = useRef<HTMLDivElement>(null);
  const tabelScrollerHeight = useTableScrollerHeight(tableContainerRef, [dataSource.length]);

  return (
    <DetailTableWrapper>
      <div className="table-container" ref={tableContainerRef}>
        {contextHolder}
        <Table
          loading={loading}
          rowKey={(record: RecordType) => record.id}
          dataSource={dataSource}
          columns={columns}
          scroll={{ y: tabelScrollerHeight, scrollToFirstRowOnChange: true }}
          pagination={{
            size: 'small',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            total: pageInfo.total,
            pageSizeOptions: [String(DEFAULT_PAGE_SIZE), '20', '30', '40'],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (count) => `共 ${count} 条`,
            onChange: (pageNum, pageSize) => handlePaginationChange(pageNum, pageSize),
          }}
        />
      </div>
    </DetailTableWrapper>
  );
};

export default memo(DetailTable);
