import React, { memo, useEffect, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import { Chart } from '@antv/g2';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { Empty } from 'antd';

import { AreaGraphWrapper } from './area-graph.style';

interface IProps {
  children?: ReactNode;
  title: string;
  waterStatistic: any[];
}

const AreaGraph: FC<IProps> = (props) => {
  const { title, waterStatistic } = props;

  const idRef = useRef<string>(`chart_source_${uuidv4().slice(0, 8)}`);

  useEffect(() => {
    renderGraph();
  }, [waterStatistic]);

  const renderGraph = () => {
    const chart = new Chart({ container: idRef.current });

    // 生成默认的时间轴数据点（3小时间隔）
    const generateDefaultHours = () => {
      const hours = [];
      for (let i = 0; i < 24; i += 3) {
        hours.push(i.toString().padStart(2, '0'));
      }
      return hours;
    };

    // 检查是否有数据
    const hasData = waterStatistic && waterStatistic.length > 0;

    // 准备图表数据
    const flowData = hasData
      ? [
          ...waterStatistic.map((item) => ({
            label: '入库流量',
            hour: item.tm.slice(11, 13),
            value: item.inFlowNum ?? 0,
          })),
          ...waterStatistic.map((item) => ({
            label: '出库流量',
            hour: item.tm.slice(11, 13),
            value: item.outFlowNum ?? 0,
          })),
        ]
      : [
          // 无数据时使用默认时间轴，值设为null
          ...generateDefaultHours().map((hour) => ({
            label: '入库流量',
            hour,
            value: null,
          })),
          ...generateDefaultHours().map((hour) => ({
            label: '出库流量',
            hour,
            value: null,
          })),
        ];

    // 为无数据情况准备水位数据
    const waterLevelData = hasData
      ? [
          ...waterStatistic.map((item) => ({
            label: '上游水位',
            hour: item.tm.slice(11, 13),
            value1: item.upperLevelNum ?? 0,
          })),
          ...waterStatistic.map((item) => ({
            label: '下游水位',
            hour: item.tm.slice(11, 13),
            value1: item.lowerLevelNum ?? 0,
          })),
        ]
      : [
          // 无数据时使用默认时间轴，值设为null
          ...generateDefaultHours().map((hour) => ({
            label: '上游水位',
            hour,
            value1: null,
          })),
          ...generateDefaultHours().map((hour) => ({
            label: '下游水位',
            hour,
            value1: null,
          })),
        ];

    chart.options({
      type: 'view',
      autoFit: true,
      data: flowData,
      legend: {
        color: {
          itemLabelFill: '#fff',
          layout: {
            justifyContent: 'center',
            alignItems: 'center',
          },
        },
      },
      children: [
        {
          type: 'area',
          encode: { x: (d: any) => d.hour, y: 'value', shape: 'area', color: 'label' },
          style: { opacity: 0.2 },
          tooltip: {
            title: (datum: any) => {
              return dayjs().format(`YYYY/MM/DD ${datum.hour}:00:00`);
            },
          },
          axis: {
            y: {
              title: '流量:m³/s',
              titleFill: '#fff',
              labelFormatter: '~s',
              labelFill: '#fff',
              line: true, // 是否显示坐标轴线
              lineStroke: '#6CA1DD',
              lineExtension: [0, 0], // 轴线两侧的延长线
              grid: true, // 是否显示刻度线
              gridStroke: '#eee', // 纵轴网格线颜色设置为淡灰色
              gridLineWidth: 1, // 纵轴网格线宽度
              gridLineDash: [3, 3], // 纵轴网格线虚线样式
            },
            x: {
              labelFill: '#fff', // 设置横坐标文字颜色
              line: true,
              lineStroke: '#6CA1DD',
              // 确保在无数据时显示3小时间隔的刻度
              tickCount: 8,
            },
          },
          labels: hasData
            ? [
                {
                  text: 'value',
                  fill: '#fff',
                  fontSize: 10,
                  textAlign: (_: any, idx: number, arr: any[]) => {
                    if (idx === 0) return 'left';
                    if (idx === arr.length - 1) return 'right';
                    return 'center';
                  },
                },
              ]
            : [],
        },
        {
          type: 'line',
          data: waterLevelData,
          encode: { x: 'hour', y: 'value1', shape: 'hvh', color: 'label' },
          tooltip: {
            title: (datum: any) => {
              return dayjs().format(`YYYY/MM/DD ${datum.hour}:00:00`);
            },
          },
          axis: {
            y: {
              position: 'right',
              title: '水位:m',
              titleFill: '#fff',
              labelFormatter: '~s',
              labelFill: '#fff',
              line: true, // 是否显示坐标轴线
              lineStroke: '#6CA1DD',
              lineExtension: [0, 0], // 轴线两侧的延长线
              // 当无数据时，强制显示刻度
              tickCount: hasData ? undefined : 5,
              // 当无数据时，为坐标轴设置固定的值范围
              min: hasData ? undefined : 0,
              max: hasData ? undefined : Infinity,
            },
          },
          scale: {
            y: {
              independent: true,
              nice: true,
              // 当无数据时，为坐标轴设置固定的值范围
              domain: hasData ? undefined : [0, Infinity],
              reverse: true, // 添加reverse属性使Y轴降序排列
            },
          }, // 设置 y 方向比例尺不同步
        },
      ],
    });

    chart.render();

    // 处理"暂无数据"标识
    const container = document.getElementById(idRef.current);
    if (container) {
      // 移除可能已存在的标识
      const existingLabel = container.querySelector('.no-data-label');
      if (existingLabel) {
        container.removeChild(existingLabel);
      }

      // 如果没有数据，添加"暂无数据"标识
      if (!hasData) {
        const noDataLabel = document.createElement('div');
        noDataLabel.className = 'no-data-label';
        noDataLabel.style.position = 'absolute';
        noDataLabel.style.top = '50%';
        noDataLabel.style.left = '50%';
        noDataLabel.style.transform = 'translate(-50%, -50%)';
        noDataLabel.style.color = '#fff';
        noDataLabel.style.fontSize = '16px';
        noDataLabel.style.fontWeight = 'bold';
        noDataLabel.style.padding = '10px 20px';
        noDataLabel.style.background = 'rgba(0, 0, 0, 0.3)';
        noDataLabel.style.borderRadius = '4px';
        noDataLabel.style.pointerEvents = 'none';
        noDataLabel.textContent = '暂无数据';

        // 确保容器有相对定位
        if (getComputedStyle(container).position === 'static') {
          container.style.position = 'relative';
        }

        container.appendChild(noDataLabel);
      }
    }
  };

  return (
    <AreaGraphWrapper>
      <div className="title">{title}</div>
      <div className="chart" id={idRef.current}></div>
    </AreaGraphWrapper>
  );
};

export default memo(AreaGraph);
