export interface PropOptions {
  treeNodeChange: (val: TreeOptions | undefined) => void; //树节点选中
  ref?: any;
}

export enum ControllerType {
  ADD = '新增',
  EDIT = '编辑',
  DEL = '删除',
}
//树参数
export interface TreeParams {
  parentId?: string; //父节点id
  level?: number; //层级
}
//树配置
export interface TreeOptions {
  id: string;
  name: string;
  parentId?: string;
  attachId: string; //文件夹id
  imgId: string; //文件id
  pointName: string; //测点名称
  pointCode: string; //测点编号
  pointType: string; //测点类型
  level: number; //层级
  isLeaf: boolean; //是否有子级
  parentNode: TreeOptions; //父节点数据
  xcoordinate: number; //标注x坐标
  ycoordinate: number; //标注y坐标
  children?: TreeOptions[]; //子节点
  celsius?: number; //温度
  show?: boolean; //
}
