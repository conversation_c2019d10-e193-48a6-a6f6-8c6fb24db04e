import icon_del from '@/assets/images/pointChart/icon_clearup.png';
import icon_collect from '@/assets/images/pointChart/icon_collect.png';
import icon_collect_active from '@/assets/images/pointChart/icon_collect1.png';
import icon_share from '@/assets/images/pointChart/icon_share.png';
import icon_share_active from '@/assets/images/pointChart/icon_share1.png';
import icon_two from '@/assets/images/pointChart/icon_two.png';
import icon_four from '@/assets/images/pointChart/icon_four.png';
import icon_six from '@/assets/images/pointChart/icon_six.png';
import icon_two_active from '@/assets/images/pointChart/icon_two1.png';
import icon_four_active from '@/assets/images/pointChart/icon_four1.png';
import icon_six_active from '@/assets/images/pointChart/icon_six1.png';

const containerVariants = {
  // 初始状态（隐藏状态）
  hidden: {
    opacity: 0, // 完全透明
  },
  visible: {
    opacity: 1, // 完全不透明
    transition: {
      // 子元素动画的延迟时间
      // 每个子元素会依次延迟 0.1 秒开始动画
      // 创造一个级联/瀑布效果
      staggerChildren: 0.1,
      duration: 0.3,
    },
  },
};

const itemVariants = {
  // 初始状态（隐藏状态）
  hidden: {
    opacity: 0,
    scale: 0.9,
    y: 10,
  },
  // 显示状态
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 15,
      duration: 0.3,
    },
  },
};

export {
  icon_del,
  icon_collect,
  icon_collect_active,
  icon_share,
  icon_share_active,
  icon_two,
  icon_four,
  icon_six,
  icon_two_active,
  icon_four_active,
  icon_six_active,
  containerVariants,
  itemVariants,
};
