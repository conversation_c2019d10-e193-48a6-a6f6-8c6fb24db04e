import { DatePicker, Form, Input, Select, Space, Tooltip } from 'antd';
import styles from './monthly-production-report.page.less';
import { FC, ReactNode, useEffect, useState } from 'react';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { DeleteOutlined, EditOutlined, EyeOutlined, LoginOutlined } from '@ant-design/icons';
import { history, useParams } from 'umi';
import {
  TableOptions,
  params as paramsOptions,
  PageParams,
  stateList,
  PageType,
} from './monthly-production-report';
import {
  ReportStateEnum,
  StateMap,
  delReport,
  listPage,
} from '@/services/intelligentReport/report-management';
import moment from 'moment';
import { DelModal } from '@/components/del-modal/del-modal.component';

const { RangePicker } = DatePicker;
const inputW = { width: '250px' };
let params: PageParams = JSON.parse(JSON.stringify(paramsOptions));
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
const MonthlyProductionReport: FC<any> = (props) => {
  const [tableList, setTableList] = useState<TableOptions[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  //报表页面编码
  const { code } = useParams<{ code: string }>();
  //当前操作数据
  const [curTableData, setCurTableData] = useState<TableOptions>();
  //删除弹框控制
  const [delTemModalShow, setDelTemModalShow] = useState<boolean>(false);
  const [forms] = Form.useForm<PageParams>();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '填报年',
      dataIndex: 'year',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '填报月',
      dataIndex: 'month',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '报送日期',
      dataIndex: 'createDate',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '报送人',
      dataIndex: 'createUserName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'workflowState',
      align: 'center',
      render: (text, record: any) => statusTextHandler(record),
      ellipsis: true,
      width: 100,
    },
    {
      title: '操作',
      render: (text, record: any) => controllerComponet(record),
      align: 'center',
      ellipsis: true,
      width: 200,
    },
  ];

  useEffect(() => {
    if (code) {
      reset();
    }
  }, [code]);
  useEffect(() => {
    return () => {
      params = JSON.parse(JSON.stringify(paramsOptions));
    };
  }, []);
  //状态回显处理
  function statusTextHandler(record: TableOptions): string | undefined {
    const { state, workflowState } = record;
    if (state === ReportStateEnum.pass) {
      return StateMap.get(state);
    } else if (workflowState) {
      return StateMap.get(workflowState);
    } else {
      return StateMap.get(state);
    }
  }
  //获取分页数据
  function getPageData(): void {
    setLoading(true);
    params.type = code;
    listPage<TableOptions>(params)
      .then((res) => {
        if (res.code === '1') {
          setTableList(res.data.list);
          setTotal(Number(res.data.total));
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //删除表格数据方法
  function delTableDataFn(): Promise<any> {
    if (curTableData) {
      return delReport(curTableData.id);
    } else {
      return Promise.resolve({});
    }
  }
  //表格操作栏渲染
  function controllerComponet(record: TableOptions): ReactNode {
    const { workflowState, state, curTask, createUserId } = record;
    const look = (
      <Tooltip title="查看">
        <EyeOutlined onClick={() => toLook(record)} className={styles.icon} />
      </Tooltip>
    );
    const edit = (
      <Tooltip title="编辑">
        <EditOutlined onClick={() => toEdit(record)} className={styles.icon} />
      </Tooltip>
    );
    const del = (
      <Tooltip title="删除">
        <DeleteOutlined onClick={() => openDelModal(record)} className={styles.icon} />
      </Tooltip>
    );
    const examine = (
      <Tooltip title="审核">
        <LoginOutlined onClick={() => toExamine(record)} className={styles.icon} />
      </Tooltip>
    );
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          {look}
          {editStatusHandler(record) ? edit : null}
          {workflowState === ReportStateEnum.commit && curTask && state !== ReportStateEnum.pass
            ? examine
            : null}
          {userId === createUserId && del}
        </Space>
      </div>
    );
  }
  //编辑状态判断
  function editStatusHandler(record: TableOptions): boolean {
    const { workflowState, state, createUserId } = record;
    if (userId === createUserId) {
      if (
        workflowState &&
        (workflowState === ReportStateEnum.save || workflowState === ReportStateEnum.pass)
      ) {
        return true;
      } else if (state === ReportStateEnum.save || state === ReportStateEnum.pass) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
  //上部操作按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button onClick={toAdd}>+新增</type-button>
      </div>
    );
  }
  //跳转新增
  function toAdd(): void {
    history.push('/monthlyProductionReportAdd/' + code);
  }
  //打开删除弹框
  function openDelModal(data: TableOptions): void {
    setCurTableData(data);
    setDelTemModalShow(true);
  }
  //查看
  function toLook(data: TableOptions): void {
    if (data.state === ReportStateEnum.save) {
      history.push('/monthlyProductionReportEdit/' + data.id, {
        type: PageType.LOOK,
      });
    } else {
      history.push('/monthlyProductionReportExamine/' + data.id, {
        type: PageType.LOOK,
      });
    }
  }
  //去编辑
  function toEdit(data: TableOptions): void {
    history.push('/monthlyProductionReportEdit/' + data.id, {
      type: PageType.EDIT,
    });
  }
  //跳转审核
  function toExamine(data: TableOptions): void {
    history.push('/monthlyProductionReportExamine/' + data.id);
  }
  //查询
  function query(): void {
    const { name, startDate, state } = forms.getFieldsValue();
    let date: any = startDate;
    params = {
      ...params,
      name,
      state,
      pageNum: 1,
      ...(date
        ? {
            startDate: moment(date[0].$d).format('YYYY-MM-DD HH:mm:ss'),
            endDate: moment(date[1].$d).format('YYYY-MM-DD 23:59:59'),
          }
        : {}),
    };
    getPageData();
  }
  //重置
  function reset(): void {
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    getPageData();
  }
  //页码改变
  function pageChange(page: number, pageSize: number): void {
    params.pageNum = page;
    params.pageSize = pageSize;
    getPageData();
  }
  //搜索表单模板
  function searchFormComponent(): ReactNode {
    const btnStyle = 'border-radius:2px;box-shadow:none';
    return (
      <Form className="report-form" form={forms} layout="inline">
        <Form.Item<PageParams> label="名称" name="name">
          <Input style={inputW} placeholder="请输入名称..."></Input>
        </Form.Item>
        <Form.Item<PageParams> label="报送日期" name="startDate">
          <RangePicker style={inputW} />
        </Form.Item>
        <Form.Item<PageParams> label="状态" name="state">
          <Select style={inputW} options={stateList} placeholder="请选择..." />
        </Form.Item>
        <Form.Item>
          <type-button onClick={query} styles={btnStyle}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button onClick={reset} styles={btnStyle}>
            重置
          </type-button>
        </Form.Item>
      </Form>
    );
  }
  return (
    <div className={styles['page-container']}>
      <section className={styles['top-search-container']}>
        {controllerComponent()}
        {searchFormComponent()}
      </section>
      <section>
        <TableComponent
          params={{
            pageSize: params.pageSize,
            pageChange,
            total,
            current: params.pageNum,
          }}
          loading={loading}
          columns={columns}
          tableList={tableList}
        ></TableComponent>
      </section>
      {/* 删除弹框 */}
      <DelModal
        onOk={getPageData}
        delFn={delTableDataFn}
        open={delTemModalShow}
        close={() => setDelTemModalShow(false)}
      ></DelModal>
    </div>
  );
};

export default MonthlyProductionReport;
