import { FC, memo, useEffect, useState } from 'react';
import styles from './data-modal.component.less';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { PropOptions, paramsOptions } from './data-modal';
import {
  SocketOptions,
  modalSourceHead,
  subTheme,
  unsubTheme,
} from '../../main-transformer-status';
import { UnitOptions } from '../unit-tab/unit-tab';
import { isJsonString } from '@/utils/utils';
import boxIcon from '@/assets/images/icon_button.png';
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
let params = JSON.parse(JSON.stringify(paramsOptions));
let newData: UnitOptions | undefined = undefined;
export const DataModal: FC<PropOptions> = memo(({ pointData, socketClient, bimDefViewSet }) => {
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableList, setTableList] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  //socket初始化
  const [socketInit, setSokcetInit] = useState<boolean>(false);
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '间隔名称',
      dataIndex: 'gridName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '报警状态',
      dataIndex: 'warningStatus',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电类型',
      dataIndex: 'powerType',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电概率',
      dataIndex: 'probability',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电均值',
      dataIndex: 'powerValue',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电次数',
      dataIndex: 'powerTotal',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电峰值',
      dataIndex: 'powerMax',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '放电相位',
      dataIndex: 'powerPhase',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '通讯地址',
      dataIndex: 'address',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最近更新',
      dataIndex: 'time',
      align: 'center',
      ellipsis: true,
    },
  ];
  useEffect(() => {
    return () => {
      params = JSON.parse(JSON.stringify(paramsOptions));
    };
  }, []);
  //获取测点id
  function getPointIds(): string[] {
    if (pointData) {
      const ids: string[] = [];
      const fields = Object.keys(pointData.powerList);
      fields.forEach((item) => {
        pointData.powerList[item].forEach((citem) => {
          ids.push(citem.value);
        });
      });
      return ids;
    } else {
      return [];
    }
  }
  //发送订阅
  function subMsg(): void {
    const ids = getPointIds();
    if (ids.length > 0) {
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids.join(','),
          userId,
          topicType: modalSourceHead,
        }),
        destination: subTheme,
      };
      // 接受返回消息
      socketInit === false && messageReturn(modalSourceHead + userId);
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
  }
  //取消订阅
  function unsubMsg(): void {
    const ids = getPointIds();
    if (ids.length > 0) {
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids.join(','),
          userId,
          topicType: modalSourceHead,
        }),
        destination: unsubTheme,
      };
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
  }
  //消息返回
  function messageReturn(soucre: string): void {
    if (socketClient) {
      socketClient.subscribe(soucre, function (message: any) {
        const recv = message.body;
        setSokcetInit(true);
        if (isJsonString(recv)) {
          socketDataHandler(JSON.parse(recv));
        }
      });
    }
  }
  //socket消息返回处理
  function socketDataHandler(datas: SocketOptions): void {
    if (newData) {
      const fields = Object.keys(newData.powerList);
      fields.some((item) => {
        return newData?.powerList[item].some((citem) => {
          if (citem.value === datas.key) {
            citem.price = datas.value;
            citem.timeStamp = datas.time_stamp;
            return true;
          }
        });
      });
      pointChangeTable(datas.time_stamp);
    }
  }
  //测点列表数据转换表格数据
  function pointChangeTable(time: string = '—'): void {
    if (newData) {
      const fields = Object.keys(newData.powerList);
      let tableList: any[] = [];
      fields.forEach((item) => {
        let data: any = {};
        newData?.powerList[item].forEach((citem) => {
          data.id = citem.value;
          const name = citem.label.match(/主变([A-Z])/);
          data.gridName = name ? name[0] : '—';

          data[citem.unit] = citem.price || '—';
        });
        data.deviceName = 'CH1';
        data.time = time;
        data.address = newData?.label;
        tableList.push(data);
      });
      setTableList(tableList);
    }
  }
  //页码切换
  function pageChange(page: number, pageSize: number): void {
    params.pageSize = pageSize;
    params.pageNum = page;
  }
  //打开弹框
  function openModal(): void {
    if (pointData) {
      newData = JSON.parse(JSON.stringify(pointData));
      pointChangeTable();
      subMsg();
      setModalShow(true);
    }
  }
  //关闭弹框
  function close(): void {
    unsubMsg();
    params = JSON.parse(JSON.stringify(paramsOptions));
    setModalShow(false);
  }
  return (
    <section className={styles['data-modal-container']}>
      <div
        style={{
          backgroundImage: `url(${boxIcon})`,
        }}
        onClick={openModal}
        className={styles['modal-btn']}
      >
        <span>变压器局放监测</span>
      </div>
      {/* <div onClick={() => bimDefViewSet()} className={styles['modal-btn']}>
        <span>恢复视角</span>
      </div> */}
      <SvgModal
        isFooterBtn={false}
        width="1400px"
        title="变压器局放监测"
        open={modalShow}
        close={close}
      >
        <div className={styles['modal-container']}>
          <TableComponent
            indexW={70}
            isVirtual={true}
            rowKey="id"
            params={{
              pageChange: pageChange,
              total: total,
              pageSize: params.pageSize,
              current: params.pageNum,
            }}
            loading={loading}
            scroll={{ y: 320 }}
            columns={columns}
            tableList={tableList}
          ></TableComponent>
        </div>
      </SvgModal>
    </section>
  );
});
