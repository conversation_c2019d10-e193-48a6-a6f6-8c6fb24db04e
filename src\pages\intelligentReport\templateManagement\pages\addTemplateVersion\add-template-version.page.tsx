import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import styles from './add-template-version.page.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { Input, Tabs, message, TabsProps, Spin, Collapse } from 'antd';
import { DetailOptions, ReleaseOptions, TabItems, workerUrl } from './add-template-version';
import { DetailOptions as TemDetailOptions } from '../templateInfo/template-info';
import { history, useLocation, useParams } from 'umi';
import {
  releaseTemplate,
  templateInfo,
  templateVersionDetail,
} from '@/services/intelligentReport/template-management';
import { versionStateMap } from '../templateInfo/template-info';
import { SpreadTable } from '@/components/spread-table/spread-table.component';
import {
  AuthoritieOptions,
  CellControllerOptions,
  CellPositionOptions,
  DataTypeEnum,
  InfluxesOptions,
  RegionsOptions,
  UseImperativeOptions,
  WorkTableChangeOptions,
  WorkTableNameChangeOptions,
} from '@/components/spread-table/spread-table';
import { UseImperativeOptions as AuthoritieUseImperativeOptions } from './components/authoritie-table/authoritie-table';
import { UseImperativeOptions as InfluxesUseImperativeOptions } from './components/influxes-table/influxes-table';
import { UseImperativeOptions as PlanUseImperativeOptions } from './components/plan-table/plan-table';
import { UseImperativeOptions as HistoryUseImperativeOptions } from './components/history-table/history-table';
import { CollapseProps } from 'antd/lib';
import { PageType } from '@/pages/intelligentReport/monthlyProductionReport/monthly-production-report';
import { AuthoritieTable } from './components/authoritie-table/authoritie-table.component';
import { PlanTable } from './components/plan-table/plan-table.component';
import { HistoryTable } from './components/history-table/history-table.component';
import { TipsModal } from '@/components/tips-modal/tips-modal.component';
import { InfluxesTable } from './components/influxes-table/influxes-table.component';

let designerInit: any = null;
let tableNames: string = '';
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;

const AddTemplateVersionPage = () => {
  //工作表列表
  const [TableItems, setTableItems] = useState<TabsProps['items']>([]);
  //当前操作按钮key
  const [controllerKey, setControllerKey] = useState<string>('');
  //版本详情
  const [detail, setDetail] = useState<DetailOptions>();
  //权限列表
  const [permisList, setPermisList] = useState<AuthoritieOptions[]>([]);
  //测点类型值列表
  const [influxesList, setInfluxesList] = useState<InfluxesOptions[]>([]);
  //计划列表
  const [planList, setPlanList] = useState<RegionsOptions[]>([]);
  //历史列表
  const [historyList, setHistoryList] = useState<RegionsOptions[]>([]);
  //工作薄操作实例
  const [designer, setDesigner] = useState<any>();
  //加载控制
  const [loading, setLoading] = useState<boolean>(true);
  //提交提示弹框控制
  const [commitModalShow, setCommitModalShow] = useState<boolean>(false);
  //版本名称
  const [versionName, setVersionName] = useState<string>('版本1');
  //当前工作表key
  const [tableKey, setTableKey] = useState<string>('');
  //是否可以编辑
  const [isEdit, setIsEdit] = useState<boolean>(false);
  //数据管理折叠面板key
  const [collapseActiveKeys, setCollapseActiveKeys] = useState<string[]>([
    DataTypeEnum.AUTHORITIE,
    DataTypeEnum.INFLUXES,
    DataTypeEnum.PLAN,
    DataTypeEnum.HISTORY,
  ]);
  //信息tabKey
  const [activeKey, setActiveKey] = useState<string>(() => {
    if (TabItems) {
      return TabItems[0].key;
    } else {
      return '';
    }
  });
  //页面是否初始化
  const [isInit, setIsinit] = useState<boolean>(false);
  //路由参数，模板id，版本id
  const { id, versionId } = useParams<{ id: string; versionId: string }>();
  const location = useLocation();
  const { type } = (location.state as any) || { type: 'edit' };
  const spreadRef = useRef<UseImperativeOptions>({} as UseImperativeOptions);
  const authoritieRef = useRef<AuthoritieUseImperativeOptions>(
    {} as AuthoritieUseImperativeOptions,
  );
  const influxesRef = useRef<InfluxesUseImperativeOptions>({} as InfluxesUseImperativeOptions);
  const planRef = useRef<PlanUseImperativeOptions>({} as PlanUseImperativeOptions);
  const historyRef = useRef<HistoryUseImperativeOptions>({} as HistoryUseImperativeOptions);
  //输入框聚焦数据
  const focusedInputRef = useRef<CellControllerOptions>({} as CellControllerOptions);
  //当前工作表展示权限列表
  const curPermisList = useMemo(() => {
    return permisList.filter((item) => item.tableId === tableKey);
  }, [tableKey, permisList]);
  //当前测点类型值表展示列表
  const curInfluxesList = useMemo(() => {
    return influxesList.filter((item) => item.tableId === tableKey);
  }, [tableKey, influxesList]);
  //当前计划展示列表
  const curPlanList = useMemo(() => {
    return planList.filter((item) => item.tableId === tableKey);
  }, [tableKey, planList]);
  //当前历史展示列表
  const curHistoryList = useMemo(() => {
    return historyList.filter((item) => item.tableId === tableKey);
  }, [tableKey, historyList]);
  //当前工作表得计划，历史，测点,测点类型列表数据
  const curTableList: CellControllerOptions[] = useMemo(() => {
    const plan: CellControllerOptions[] = curPlanList;
    const history: CellControllerOptions[] = curHistoryList;
    const influxes: CellControllerOptions[] = curInfluxesList;
    const list = plan.concat(history).concat(influxes);
    return list;
  }, [curPlanList, curHistoryList, curInfluxesList]);
  //右侧表格折叠面板配置
  const tableTabitems: CollapseProps['items'] = useMemo(
    () => [
      {
        key: DataTypeEnum.AUTHORITIE,
        collapsible: 'icon',
        label: tableHeaderComponent(
          '权限允许编辑区域',
          '插入选区',
          authoritieRef.current.addPermisRange,
        ),
        children: (
          <AuthoritieTable
            inputFocusFn={inputFocusHandler}
            allTableList={permisList}
            ref={authoritieRef}
            tableListChange={setPermisList}
            spreadRef={spreadRef}
            isEdit={isEdit}
            tableList={curPermisList}
          ></AuthoritieTable>
        ),
      },
      {
        key: DataTypeEnum.INFLUXES,
        collapsible: 'icon',
        forceRender: true,
        label: tableHeaderComponent(
          '测点类型值绑定',
          '插入选区',
          influxesRef.current.addPointRange,
        ),
        children: (
          <InfluxesTable
            collapseChange={setCollapseActiveKeys}
            infoTabChange={setActiveKey}
            otherTableList={curTableList}
            inputFocusFn={inputFocusHandler}
            allTableList={influxesList}
            ref={influxesRef}
            tableListChange={setInfluxesList}
            spreadRef={spreadRef}
            isEdit={isEdit}
            tableList={curInfluxesList}
          ></InfluxesTable>
        ),
      },
      {
        key: DataTypeEnum.PLAN,
        collapsible: 'icon',
        label: tableHeaderComponent('计划数据绑定', '插入选区', planRef.current.addPlanRange),
        children: (
          <PlanTable
            otherTableList={curTableList}
            inputFocusFn={inputFocusHandler}
            allTableList={planList}
            ref={planRef}
            tableListChange={setPlanList}
            spreadRef={spreadRef}
            isEdit={isEdit}
            tableList={curPlanList}
          ></PlanTable>
        ),
      },
      {
        key: DataTypeEnum.HISTORY,
        collapsible: 'icon',
        label: tableHeaderComponent('历史数据绑定', '插入选区', historyRef.current.addHistoryRange),
        children: (
          <HistoryTable
            otherTableList={curTableList}
            inputFocusFn={inputFocusHandler}
            allTableList={historyList}
            ref={historyRef}
            tableListChange={setHistoryList}
            spreadRef={spreadRef}
            isEdit={isEdit}
            tableList={curHistoryList}
          ></HistoryTable>
        ),
      },
    ],
    [tableKey, isEdit, permisList, planList, historyList, influxesList],
  );
  //表格自定义菜单
  const tableCellMenu = useMemo(() => {
    return [
      {
        text: '允许编辑',
        commandName: 'allowEdit',
        iconClass: 'ribbon-button-gridlines',
        visibleContext: 'ClickViewport',
        execute: authoritieRef.current.tablePermisEvent,
      },
      {
        text: '测点类型绑定',
        commandName: 'influxesBind',
        iconClass: 'custom-influxes',
        visibleContext: 'ClickViewport',
        execute: influxesRef.current.tablePointEvent,
      },
      {
        text: '计划绑定',
        commandName: 'planBind',
        iconClass: 'custom-plan',
        visibleContext: 'ClickViewport',
        execute: planRef.current.tablePlanEvent,
      },
      {
        text: '历史绑定',
        commandName: 'historyBind',
        iconClass: 'custom-history',
        visibleContext: 'ClickViewport',
        execute: historyRef.current.tableHistoryEvent,
      },
    ];
  }, [isInit]);

  useEffect(() => {
    if (versionId !== 'add') {
      getDetail(versionId);
      if (type === 'edit') {
        setIsEdit(true);
      }
    } else {
      setLoading(false);
      setIsEdit(true);
    }
  }, [versionId]);
  useEffect(() => {
    setIsinit(true);
    return () => {
      tableNames = '';
    };
  }, []);
  useEffect(() => {
    if (detail && designer) {
      createWorkTable(detail);
    }
  }, [detail, designer]);
  //获取模板详情
  function getTemplateDetail() {
    templateInfo(id)
      .then((res) => {
        if (res.code === '1') {
          const data: TemDetailOptions = res.data;
          const ids = data.templateVersionList[data.templateVersionList.length - 1].id;
          history.replace(`/addTemplateVersion/${id}/${ids}`, {
            type: PageType.EDIT,
          });
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //表格表头模板
  function tableHeaderComponent(name: string, btnName: string, event: () => void): ReactNode {
    return (
      <p className={styles['permiss-title-container']}>
        <span className={styles['permiss-title']}>{name}</span>
        {isEdit && <type-button onClick={event}>{btnName}</type-button>}
      </p>
    );
  }
  //表格初始化创建
  function univerCreate(designer: any): void {
    const table = getWorkTableData();
    if (table) {
      setTableItems([
        {
          key: table.name(),
          label: table.name(),
        },
      ]);
      setTableKey(table.name());
      setDesigner(designer);
      designerInit = designer;
    }
  }
  //获取详情后导入详情模板数据
  function createWorkTable(data: DetailOptions): void {
    spreadRef.current.importJSON(data.templateData);
    if (controllerKey === '') {
      spreadRef.current.verticalPositionTop();
    }
    const tables = spreadRef.current.getAllWorkTable() || [];
    const tableItems = tables.map((item) => {
      return {
        key: item.name(),
        label: item.name(),
      };
    });
    setTableItems(tableItems);
    tableItems[0] && onTableTabsChange(tableItems[0].key);
    cellOtherStyleHandler({ authorities: data.authorities, status: 0 });
    setLoading(false);
  }
  //获取版本详情
  function getDetail(id: string): void {
    setLoading(true);
    templateVersionDetail(id).then((res) => {
      if (res.code === '1') {
        setDetail(res.data);
        setVersionName(res.data.versionName);
        setPermisList(res.data.authorities || []);
        setInfluxesList(res.data.influxes || []);
        if (res.data.regions) {
          setHistoryList(res.data.regions.filter((item) => item.type === '1'));
          setPlanList(res.data.regions.filter((item) => item.type === '0'));
        }
      } else {
        message.error(res.error || res.message);
      }
    });
  }
  //删除工作表事件
  function removeWorkTable(key: string): void {
    //删除工作表的tabs
    setTableItems((prev) => {
      const list = prev?.filter((item) => item.key !== key);
      return list;
    });
    //删除工作表的权限数据
    setPermisList((prev) => {
      return prev?.filter((item) => item.tableId !== key);
    });
    //删除测点数据
    setInfluxesList((prev) => {
      return prev?.filter((item) => item.tableId !== key);
    });
    //删除工作表的计划数据
    setPlanList((prev) => {
      return prev?.filter((item) => item.tableId !== key);
    });
    //删除工作表的历史数据
    setHistoryList((prev) => {
      return prev?.filter((item) => item.tableId !== key);
    });
  }
  //数据输入框聚焦处理
  function inputFocusHandler(data: CellControllerOptions, type: DataTypeEnum): void {
    focusedInputRef.current = { ...data, tableType: type };
  }
  //单元格点击处理
  function cellClickHandler(data: CellPositionOptions): void {
    if (focusedInputRef.current.tableType) {
      cellPositionInputValue(data);
    }
  }
  //单元格点击给右侧输入框赋值处理
  function cellPositionInputValue(data: CellPositionOptions): void {
    switch (focusedInputRef.current.tableType) {
      case DataTypeEnum.AUTHORITIE:
        setPermisList((prev) => updateListData<AuthoritieOptions>(prev, data));
        break;
      case DataTypeEnum.PLAN:
        setPlanList((prev) => updateListData<RegionsOptions>(prev, data));
        break;
      case DataTypeEnum.HISTORY:
        setHistoryList((prev) => updateListData<RegionsOptions>(prev, data));
        break;
      case DataTypeEnum.INFLUXES:
        setInfluxesList((prev) => updateListData<InfluxesOptions>(prev, data));
        break;
    }
  }
  //根据id修改列表对应数据
  function updateListData<T>(list: any[], data: CellPositionOptions): T[] {
    return list.map((item) => {
      if (item.id === focusedInputRef.current.id && item.isEdit) {
        item.endPosition = data.position;
        item.startPosition = data.position;
      }
      return item;
    });
  }
  //工作表名称事件
  function workTableNameChange(data: WorkTableNameChangeOptions): void {
    const { oldValue, newValue } = data;
    setTableItems((prev) => {
      if (prev) {
        const list = prev.map((item) => {
          if (item.key === oldValue) {
            //修改名称
            item.key = newValue;
            item.label = newValue;
            setTableKey(newValue);
          }
          return item;
        });
        return list;
      }
      return prev;
    });
    //修改工作表的权限数据表格key
    setPermisList((prev) => {
      if (prev) {
        const list = prev.map((item) => {
          if (item.tableId === oldValue) {
            item.tableId = newValue;
            item.tableName = newValue;
          }
          return item;
        });
        return list;
      }
      return prev;
    });
    setPlanList((prev) => {
      if (prev) {
        const list = prev.map((item) => {
          if (item.tableId === oldValue) {
            item.tableId = newValue;
            item.tableName = newValue;
          }
          return item;
        });
        return list;
      }
      return prev;
    });
    setHistoryList((prev) => {
      if (prev) {
        const list = prev.map((item) => {
          if (item.tableId === oldValue) {
            item.tableId = newValue;
            item.tableName = newValue;
          }
          return item;
        });
        return list;
      }
      return prev;
    });
    setInfluxesList((prev) => {
      if (prev) {
        const list = prev.map((item) => {
          if (item.tableId === oldValue) {
            item.tableId = newValue;
            item.tableName = newValue;
          }
          return item;
        });
        return list;
      }
      return prev;
    });
  }
  //工作表切换事件
  function workTableChange(data: WorkTableChangeOptions): void {
    const { newSheet, oldSheet } = data;
    if (newSheet) {
      const key = newSheet.name();
      setTableItems((prev) => {
        if (prev) {
          //判断是否属于新增工作表
          const isAdd = !prev.some((item) => item.key === key);
          if (isAdd) {
            const list = prev.concat({ key: key, label: key });
            return list;
          }
        }
        return prev;
      });
      setTableKey(key);
    } else {
      //newSheet不存在，表示oldSheet被删除
      const key = oldSheet.name();
      const spread = designerInit.getWorkbook();
      //获取oldSheet索引,切换到删除后对应的tab
      const index = spread.getSheetIndex(oldSheet.name());
      if (index < spread.sheets.length - 1) {
        setTableKey(spread.sheets[index + 1].name());
      } else {
        setTableKey(spread.sheets[index - 1].name());
      }
      removeWorkTable(key);
    }
  }
  //获取工作薄数据
  function getWorkData(): any {
    const snapshot = spreadRef.current.getWorkData();
    return snapshot;
  }
  //获取当前工作表数据
  function getWorkTableData(): any {
    const sheetSnapshot = spreadRef.current.getWorkTableData();
    return sheetSnapshot;
  }
  //清除/恢复表格多余的样式（用于标记权限，测点这些单元格）
  function cellOtherStyleHandler({
    authorities = [],
    status,
  }: {
    authorities: AuthoritieOptions[];
    status: 0 | 1;
  }): void {
    authorities && authoritieRef.current.cellBackBatchHandler(authorities, status);
  }
  //获取提交数据
  function getRequestParams(
    {
      authorities,
      regions,
      influxes,
    }: {
      authorities: AuthoritieOptions[];
      influxes: InfluxesOptions[];
      regions: RegionsOptions[];
    },
    versionState: number,
  ): ReleaseOptions {
    cellOtherStyleHandler({ authorities, status: 1 });
    const templateData = getWorkData();
    return {
      ...(versionId !== 'add' ? { id: versionId } : {}),
      templateId: id,
      templateData: JSON.stringify(templateData),
      authorities: authorities,
      influxes,
      versionState,
      versionName,
      regions,
    };
  }
  //检查表格数据是否有未暂存数据
  function checkTableEditStatus(): { status: 1 | 0; tableName: string } {
    const authoritie: CellControllerOptions[] = permisList;
    const plan: CellControllerOptions[] = planList;
    const history: CellControllerOptions[] = historyList;
    const influxes: CellControllerOptions[] = influxesList;
    const list = plan.concat(history).concat(authoritie).concat(influxes);
    let tableName = '';
    list.some((item) => {
      if (item.isEdit) {
        tableName = item.tableName;
      }
    });
    return { status: tableName !== '' ? 0 : 1, tableName };
  }
  //暂存
  function save(): void {
    setControllerKey('save');
    if (versionName !== '') {
      setLoading(true);
      const { status, tableName } = checkTableEditStatus();
      tableNames = tableName;
      if (status === 1 || commitModalShow) {
        //使用WEB Worker，防止处理数据过大，堵塞UI loading渲染
        const worker = new Worker(workerUrl);
        worker.postMessage({ influxesList, permisList, planList, historyList });
        worker.onmessage = (e) => {
          const data = getRequestParams(e.data, 0);
          releaseTemplate(data)
            .then((res) => {
              if (res.code === '1') {
                message.success('暂存完成！');
                if (versionId === 'add') {
                  getTemplateDetail();
                } else {
                  getDetail(versionId);
                }
              } else {
                cellOtherStyleHandler({
                  authorities: data.authorities,
                  status: 0,
                });
                message.warning(res.message || '暂存失败');
                setLoading(false);
              }
              setCommitModalShow(false);
            })
            .catch((err) => {
              cellOtherStyleHandler({
                authorities: data.authorities,
                status: 0,
              });
              setLoading(false);
            });
        };
      } else {
        setLoading(false);
        setCommitModalShow(true);
      }
    } else {
      message.warning('请输入版本名称！');
    }
  }
  //发布
  function release(isBack: boolean): void {
    setControllerKey('release');
    if (versionName !== '') {
      setLoading(true);
      const { status, tableName } = checkTableEditStatus();
      tableNames = tableName;
      if (status === 1 || commitModalShow) {
        //使用WEB Worker，防止处理数据过大，堵塞UI loading渲染
        const worker = new Worker(workerUrl);
        worker.postMessage({ influxesList, permisList, planList, historyList });
        worker.onmessage = (e) => {
          const data = getRequestParams(e.data, 1);
          releaseTemplate(data)
            .then((res) => {
              if (res.code === '1') {
                message.success('发布完成！');
                if (isBack) {
                  history.goBack();
                } else if (versionId === 'add') {
                  getTemplateDetail();
                } else {
                  getDetail(versionId);
                }
              } else {
                cellOtherStyleHandler({
                  authorities: data.authorities,
                  status: 0,
                });
                message.warning(res.message || '发布失败');
                setLoading(false);
              }
              setCommitModalShow(false);
            })
            .catch((err) => {
              cellOtherStyleHandler({
                authorities: data.authorities,
                status: 0,
              });
              setLoading(false);
            });
          worker.terminate(); //关闭worker
        };
      } else {
        setLoading(false);
        setCommitModalShow(true);
      }
    } else {
      message.warning('请输入版本名称！');
    }
  }
  //提示弹框确定执行方法
  function tipsModalSureFn(): void {
    const fn = eval(controllerKey);
    fn();
  }
  //开始编辑
  function edit(): void {
    setIsEdit(true);
  }
  //下载模板
  function downTemplate(): void {
    cellOtherStyleHandler({ authorities: permisList, status: 1 });
    setLoading(true);
    spreadRef.current
      .exportFile(versionName)
      .then((res) => {
        cellOtherStyleHandler({ authorities: permisList, status: 0 });
        message.success('导出成功');
        setLoading(false);
      })
      .catch((err) => {
        cellOtherStyleHandler({ authorities: permisList, status: 0 });
        message.warning('导出失败');
        setLoading(false);
      });
  }
  //导入文件处理
  function fileImportHandler(status: 1 | 0): void {
    if (status === 1) {
      setPermisList([]);
      const tables = spreadRef.current.getAllWorkTable() || [];
      const tableItems = tables.map((item) => {
        return {
          key: item.name(),
          label: item.name(),
        };
      });
      setTableItems(tableItems);
      tableItems[0] && onTableTabsChange(tableItems[0].key);
    }
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    const saveBtnStyle = 'border-radius:0px';
    const downBtnStyle = 'border-top-right-radius:0px;border-bottom-right-radius:0px';
    const releaseBtnStyle = 'border-top-left-radius:0px;border-bottom-left-radius:0px';
    const isSave = (detail && detail.versionState === '0') || detail == undefined;
    const isUser = (detail && detail.createUserId === userId) || userId == 1;
    const downBtn = (
      <type-button
        onClick={downTemplate}
        loading={loading}
        styles={isUser || versionId === 'add' ? downBtnStyle : ''}
      >
        下载模板
      </type-button>
    );
    const uploadBtn = (
      <type-button onClick={spreadRef.current.importFile} loading={loading} styles={saveBtnStyle}>
        导入模板
      </type-button>
    );
    const releaseBtn = (
      <type-button loading={loading} onClick={() => release(true)} styles={releaseBtnStyle}>
        发布
      </type-button>
    );
    const saveReleaseBtn = (
      <type-button loading={loading} onClick={() => release(false)} styles={saveBtnStyle}>
        暂存
      </type-button>
    );
    const saveBtn = (
      <type-button loading={loading} onClick={save} styles={saveBtnStyle}>
        暂存
      </type-button>
    );
    const editBtn = (
      <type-button loading={loading} onClick={edit} styles={releaseBtnStyle}>
        编辑
      </type-button>
    );

    return (
      <div className={styles['controller-container']}>
        {downBtn}
        {isEdit ? (
          <>
            {uploadBtn}
            {isSave ? saveBtn : saveReleaseBtn}
            {releaseBtn}
          </>
        ) : (
          isUser && editBtn
        )}
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  //信息tab改变
  function onTabsChange(key: string) {
    setActiveKey(key);
  }
  //工作表tab改变
  function onTableTabsChange(key: string): void {
    setTableKey(key);
    spreadRef.current.setActiveSheet(key);
  }
  //版本名称改变
  function versionNameChange(e: any): void {
    setVersionName(e.target.value);
  }
  //基本信息模板
  function baseInfoComponent(): ReactNode {
    return (
      <>
        <div className={styles['info-item']}>
          <span>版本名称</span>
          {isEdit ? (
            <Input
              style={{ margin: '4px' }}
              placeholder="请输入..."
              onChange={versionNameChange}
              value={versionName}
            ></Input>
          ) : (
            <span>{versionName}</span>
          )}
        </div>
        <div className={styles['info-item']}>
          <span>版本状态</span>
          <span>{versionStateMap.get(detail?.versionState || '')}</span>
        </div>
        {versionId !== 'add' && (
          <div className={styles['info-item']}>
            <span>修改日期</span>
            <span>{detail?.createDate}</span>
          </div>
        )}
        {versionId !== 'add' && (
          <div className={styles['info-item']}>
            <span>创建人</span>
            <span>{detail?.createUserName}</span>
          </div>
        )}
      </>
    );
  }
  return (
    <div className={`${styles['report-add-page']} add-template-version-page`}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          <span>{versionId === 'add' ? '新增版本' : versionName + '信息'}</span>
        </div>
        {controllerComponent()}
      </section>
      <Spin wrapperClassName={styles.spin} spinning={loading}>
        <div className={styles.content}>
          {isInit ? (
            <SpreadTable
              cellClick={cellClickHandler}
              cellMenu={tableCellMenu}
              ref={spreadRef}
              disabled={isEdit}
              workTableChange={workTableChange}
              workTableNameChange={workTableNameChange}
              createInit={univerCreate}
              fileImportEnd={fileImportHandler}
            ></SpreadTable>
          ) : (
            <div className={styles.mask}></div>
          )}
          <section className={styles.info}>
            <Tabs activeKey={activeKey} items={TabItems} onChange={onTabsChange} />
            {activeKey === '1' && baseInfoComponent()}
            {/* 使用display控制,防止初始化页面后，访问不到ref */}
            <div
              className={
                activeKey === '2'
                  ? styles['work-table-tabs-container']
                  : styles['work-table-tabs-hidden']
              }
            >
              {/* 工作表tabs */}
              <Tabs
                className="work-table-tabs"
                activeKey={tableKey}
                items={TableItems}
                onChange={onTableTabsChange}
              />
              <Collapse
                onChange={setCollapseActiveKeys}
                activeKey={collapseActiveKeys}
                ghost
                items={tableTabitems}
              />
            </div>
          </section>
        </div>
      </Spin>
      <TipsModal
        onOk={tipsModalSureFn}
        open={commitModalShow}
        close={() => setCommitModalShow(false)}
        loading={loading}
        tipsText={'右侧表格 ' + tableNames + ' 有未保存的数据，是否继续提交？'}
      ></TipsModal>
    </div>
  );
};

export default AddTemplateVersionPage;
