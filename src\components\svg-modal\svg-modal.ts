import { ReactNode } from 'react';

export interface PropOptions {
  width?: string; //px
  height?: string; //px
  open: boolean;
  title: string;
  children?: ReactNode;
  close?: () => void; //关闭事件
  onOk?: () => void; //提交事件
  confirmloading?: boolean; //按钮加载控制
  maskClosable?: boolean; //蒙层关闭是否开启
  isFooterBtn?: boolean; //是否需要默认页脚区域
  confirmText?: string; //关闭文字
  cancelText?: string; //确定文字
}
