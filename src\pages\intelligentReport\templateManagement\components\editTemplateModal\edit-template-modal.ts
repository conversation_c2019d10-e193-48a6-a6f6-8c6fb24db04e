import { DictOptions } from '@/services/intelligentReport/template-management';
import { TableOptions } from '../../template-management';
import { ProductOptions } from '@/services/system';

export interface PropOptions {
  open: boolean;
  close: () => void;
  onOk: (data: FormEditOptions) => void;
  editData?: TableOptions;
  menuData?: ProductOptions;
}
export interface FormEditOptions {
  code: string;
  name: string;
  type: string;
  remark: string;
  id: string;
}
