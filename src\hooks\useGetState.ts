import { useCallback, useEffect, useState } from 'react';

export function useSyncCallback(callback: (val?: any) => any) {
  const [proxyState, setProxyState] = useState({ current: false });
  const [param, setParam] = useState();
  const Func = useCallback(
    (val?: any) => {
      setParam(val);
      setProxyState({ current: true });
    },
    [proxyState],
  );

  useEffect(() => {
    //重置proxyState，下次使用
    if (proxyState.current === true) {
      setProxyState({ current: false });
    }
  }, [proxyState]);

  useEffect(() => {
    if (proxyState.current) {
      callback(param);
    }
  });

  return Func;
}
