import React, { memo, useCallback, useMemo, useRef } from 'react';
import type { ReactNode, FC, ElementRef } from 'react';

import { QueryFormWrapper } from './query-form.style';
import BaseForm from '@/components/base-form/base-form.component';
import { EElementType } from '@/components/base-form/base-form.types';
import type { IBaseFormConfig } from '@/components/base-form/base-form.types';
interface IProps {
  children?: ReactNode;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const QueryForm: FC<IProps> = (props) => {
  const { setVisible } = props;

  const baseFormRef = useRef<ElementRef<typeof BaseForm>>(null);

  const handleCalc = useCallback(() => {
    setVisible(true);
  }, []);

  const baseFormConfig = useMemo<IBaseFormConfig>(
    () => ({
      layout: 'inline',
      elements: [
        {
          key: 'btn_calc_all',
          type: EElementType.BUTTON,
          label: '全量计算',
          onClick: handleCalc,
          noStyle: false,
          formItemProps: {
            style: { marginLeft: 'auto', marginRight: '0', width: 'auto' }, // 右靠齐
          },
        },
      ],
    }),
    [handleCalc],
  );

  return (
    <QueryFormWrapper>
      <BaseForm ref={baseFormRef} config={baseFormConfig} />
    </QueryFormWrapper>
  );
};

export default memo(QueryForm);
