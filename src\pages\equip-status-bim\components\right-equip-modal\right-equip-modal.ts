export interface PropOptions {
  open: boolean;
  close: () => void; //关闭方法
}

export interface TreeOptions {
  name: string;
  id: string;
}
export interface DataOptions {
  label: string;
  value: number;
}
//柱状图坐标轴样式对象
export const chartAxisStyle = {
  yAxis: {
    grid: {
      line: {
        style: (item: any, index: number) => {
          if (index === 0) {
            //隐藏Y轴0刻度线
            return {
              stroke: 'transparent',
            };
          } else {
            return {
              stroke: '#6CA1DD',
              lineDash: [4, 2],
            };
          }
        },
      },
    },
    label: {
      style: {
        fill: '#D4EBFD',
        fontSize: 12,
      },
    },
    title: {
      text: '温度（℃）',
      position: 'end',
      autoRotate: false,
      rotation: 45,
      spacing: -40,
      style: {
        y: 10,
        fill: '#fff',
        fontSize: 11,
      },
    },
  },
  xAxis: {
    line: {
      style: {
        stroke: '#6CA1DD',
      },
    },
    tickLine: {
      style: {
        stroke: 'transparent',
      },
    },
    label: {
      style: {
        fill: '#D4EBFD',
        fontSize: 12,
      },
    },
  },
};
//测试数据
export const TEST_LIST: TreeOptions[] = [
  {
    name: '点1',
    id: '1',
  },
  {
    name: '点2',
    id: '2',
  },
  {
    name: '点3',
    id: '3',
  },
  {
    name: '点4',
    id: '4',
  },
  {
    name: '点5',
    id: '5',
  },
  {
    name: '点6',
    id: '6',
  },
  {
    name: '点7',
    id: '7',
  },
  {
    name: '点8',
    id: '8',
  },
  {
    name: '点9',
    id: '9',
  },
  {
    name: '点10',
    id: '10',
  },
  {
    name: '点11',
    id: '11',
  },
  {
    name: '点12',
    id: '12',
  },
];
export const TEST_LIST2: DataOptions[] = [
  {
    label: '11/01 13:00',
    value: 1,
  },
  {
    label: '11/01 14:00',
    value: 2,
  },
  {
    label: '11/01 15:00',
    value: 3,
  },
  {
    label: '11/01 16:00',
    value: 4,
  },
  {
    label: '11/01 17:00',
    value: 5,
  },
  {
    label: '11/01 18:00',
    value: 6,
  },
  {
    label: '11/01 19:00',
    value: 7,
  },
  {
    label: '11/01 20:00',
    value: 8,
  },
  {
    label: '11/01 21:00',
    value: 9,
  },
  {
    label: '11/01 22:00',
    value: 10,
  },
];
