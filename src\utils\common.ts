import { request, urlPrefix } from '@/utils/request';
import { cloneDeep, uniqBy } from 'lodash';
import moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
//测试接口
export const sleep = <T = any>(delay = 500, backData: T) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(backData);
    }, delay);
  });
};

/**
 * 把2024-01-17T02:05:18.494Z格式为日期和时间的方法
 */
export function formatDate(dateString: string) {
  var date = new Date(dateString);
  var year = date.getFullYear();
  var month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
  var day = date.getDate().toString().padStart(2, '0');
  var hours = date.getHours().toString().padStart(2, '0');
  var minutes = date.getMinutes().toString().padStart(2, '0');
  var seconds = date.getSeconds().toString().padStart(2, '0');
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}
/**
 * 获取缩略图
 */
export function getDownloadAttachment(attachId: any): Promise<any> {
  return request.get(`/Attachment/downloadAttachment/thumbnail/${attachId}`);
}

/**
 * 生成唯一id值
 */

export const getUniqueId = () => {
  return uuidv4();
};

/**
 * 设置带有过期时间的本地存储
 * @param {string} key 存储的键
 * @param {any} value 存储的值
 * @param {number} exp 过期时间（以毫秒为单位）
 */
export const setLocalStorageWithExpiry = (key: string, value: any, exp: number) => {
  const now = new Date();
  // `item` 对象包含数据值和过期时间
  const item = {
    value: value,
    expiry: now.getTime() + exp,
  };
  localStorage.setItem(key, JSON.stringify(item));
};

/**
 * 获取本地存储的值，如果过期则返回 null
 * @param {string} key 存储的键
 * @returns {any | null} 存储的值或者 null（如果过期）
 */

export const getLocalStorageWithExpiry = (key: string) => {
  const itemStr = localStorage.getItem(key);
  // 如果不存在，则直接返回 null
  if (!itemStr) {
    return null;
  }
  const item = JSON.parse(itemStr);
  const now = new Date();
  // 检查过期时间
  if (now.getTime() > item.expiry) {
    // 如果已过期，删除存储并返回 null
    localStorage.removeItem(key);
    return null;
  }
  return item.value;
};

/**
 * 遍历树结构并将子集数据插入树节点
 * @export
 * @param {any[]} treeData 树结构数据
 * @param {any[]} insertNodes 待插入儿子对象
 * @param {(string | number)} pId 插入节点key值
 * @param {string} [keyName='id'] 插入节点key名称
 * @param {string} [childName='children'] 插入节点子节点名称
 * @returns {any[]}
 */
export function handleTreeDataMethods(
  treeData: any[],
  insertNodes: {},
  pId: string | number,
  keyName: string = 'id',
  childName: string = 'children',
): any[] {
  const data = cloneDeep(treeData);
  const insertTreeNode = (array: any[]) => {
    array.forEach((item) => {
      if (item[keyName] === pId) {
        const pItem = item;
        pItem[childName] = [...pItem[childName], { ...insertNodes }];
      }
      if (item[childName] instanceof Array && item[childName].length) {
        insertTreeNode(item[childName]);
      }
    });
  };

  insertTreeNode(data);
  return data;
}

/**
 * 遍历树结构并将子集数据插入树节点
 * @export
 * @param {any[]} treeData 树结构数据
 * @param {any[]} insertNodes 待插入的数据数组
 * @param {(string | number)} pId 插入节点key值
 * @param {string} [keyName='id'] 插入节点key名称
 * @param {string} [childName='children'] 插入节点子节点名称
 * @returns {any[]}
 */
export function dfsInsertTreeNode(
  treeData: any[],
  insertNodes: any[],
  pId: string | number,
  keyName: string = 'id',
  childName: string = 'children',
): any[] {
  const data = cloneDeep(treeData);
  const insertTreeNode = (array: any[]) => {
    array.forEach((item) => {
      if (item[keyName] === pId) {
        const pItem = item;
        pItem[childName] = insertNodes;
      }
      if (item[childName] instanceof Array && item[childName].length) {
        insertTreeNode(item[childName]);
      }
    });
  };

  insertTreeNode(data);
  return data;
}

/**
 * 千字节转为文件大小
 * @export
 * @param {number} kbytes 千字节数
 * @returns
 */
export function kbytesToSize(kbytes: number) {
  if (kbytes === 0) return '0 KB';
  const m = 1024;
  const sizes = ['KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(kbytes) / Math.log(m));
  // eslint-disable-next-line no-restricted-properties
  return (kbytes / Math.pow(m, i)).toFixed(1) + sizes[i];
}

/**
 * 字节转为文件大小
 * @export
 * @param {number} bytes 字节数
 * @returns
 */
export function bytesToSize(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  // eslint-disable-next-line no-restricted-properties
  return (bytes / Math.pow(k, i)).toFixed(1) + sizes[i];
}

/**
 * 获取文件后缀名
 * @export
 * @param {string} filename 文件名
 * @param {string} type 获取类型，1：文件名、默认：文件后缀名
 * @returns
 */
export function getFileExtendingName(filename: string, type: number = 0) {
  let name = '';
  switch (type) {
    case 1:
      name = filename.replace(/\.\w+$/, '');
      break;
    default:
      name = filename.replace(/.+\./, '');
      break;
  }

  return name;
}

/**
 * 获取工程规模单位
 * @export
 * @param {string} name
 */
export function getProjectScaleUnit(name: string) {
  if (typeof name !== 'string') return name;
  const reg = /^(\d+)([^\d]+|\w+)/;
  return name.match(reg);
}

/**
 * 递归查找树节点指定节点
 * @export
 * @param {any[]} dataSource 源数据
 * @param {string} [key='id'] 匹配的键
 * @param {string} findKey 查找的键值
 * @param {string} [childKey='children'] 子集名称
 * @returns
 */
export function findTreeNode(
  dataSource: any[],
  key: string = 'id',
  findKey: string | number,
  childKey: string = 'children',
) {
  let node: any = null;
  const dfs = (array: any[]) => {
    array.forEach((item) => {
      if (item[key] !== findKey) {
        if (item[childKey] instanceof Array && item[childKey].length) {
          dfs(item[childKey]);
        }
      } else {
        node = item;
      }
    });
  };

  if (dataSource instanceof Array) {
    dfs(dataSource);
  } else {
    throw Error('憨批传参');
  }
  return node;
}

/**
 * 根据传参返回星期数
 * @export
 * @param {number} n 传入星期数的枚举值，如：0、1、2、3、4、5、6
 * @returns
 */
export function dayOfWeek(n: number) {
  if (typeof n === 'number') {
    return `星期${'日一二三四五六'[n % 7]}`;
  }
  throw Error('憨批传参');
}

/**
 * 对调数组两个元素的index,并返回一个全新数组对象
 * @export
 * @param {any[]} array 操作的数组
 * @param {numer} index 操作的元素index
 * @param {string} type 操作类型up：上移，down：下移
 * @returns
 */
export function swapArray(array: any[], index: number, type: 'up' | 'down') {
  if (!(array instanceof Array) || typeof index !== 'number') {
    throw Error('憨批传参');
  }
  const data = cloneDeep(array);

  if ((index === 0 && type === 'up') || (index === data.length - 1 && type === 'down')) return data;

  const swapIndex = type === 'up' ? index - 1 : index + 1;

  [data[index], data[swapIndex]] = [data[swapIndex], data[index]];
  return data;
}

/**
 * dfs递归获取树结构树中所有节点key的Value,返回string[]
 * @export
 * @param {any[]} source 结构树数据
 * @param {string} [key='id'] 遍历的key值,缺省为‘id’
 * @param {string} [childName='children'] 遍历的子节点key值,缺省为‘children’
 * @returns {string[]} 字符串形式key值集合
 */
export function getTreeStructureKeys(
  source: any[],
  key: string = 'id',
  childName: string = 'children',
) {
  const result: any[] = [];

  const dfs = (array: any[]) => {
    array.forEach((element) => {
      if (element[key]) result.push(String(element[key]));
      if (element[childName] instanceof Array && element[childName].length) {
        dfs(element[childName]);
      }
    });
  };

  dfs(source);

  return result;
}

/**
 * 广度优先查找指定深度数结构节点值集合
 * @export
 * @param {any[]} source 树结构数据
 * @param {string} key 取出属性key值，默认为id
 * @param {string} childKey 子节点属性key值，默认为children
 * @returns
 */
export function bfsTreeData(source: any[], key: string = 'id', childKey: string = 'children') {
  if (!(source instanceof Array)) {
    throw Error('憨批传参');
  }
  const result: any = [];
  const stack = cloneDeep(source);

  while (stack.length > 0) {
    [...stack].forEach((element) => {
      if (element.modelUpload && element.loadModel) {
        result.push(element[key]);
      }
      stack.shift();
      if (element[childKey] instanceof Array && element[childKey].length) {
        stack.push(...element[childKey]);
      }
    });
  }

  return result;
}

/**
 * 查找树节点的父级集合
 * @export
 * @param {any[]} dataSource 树结构数据
 * @param {string} [childName='children']  子节点属性key值，默认为children
 * @param {string} [nodeElName='id'] 取出属性key值，默认为id
 * @returns
 */
export function findFather(
  dataSource: any[],
  childName: string = 'children',
  nodeElName: string = 'id',
) {
  const stack: (string | number)[] = [];
  const dfs = (array: any[]) => {
    array.forEach((item) => {
      if (item[childName] instanceof Array && item[childName].length) {
        stack.push(item[nodeElName]);
        dfs(item[childName]);
      }
    });
  };
  dfs(dataSource);
  return stack;
}

/**
 * 遍历一棵树中所有叶子节点
 * @export
 * @param {any[]} dataSource 树结构数据
 * @param {string} [key='id'] 取出属性key值，默认为id
 * @param {string} [childKey='children'] 子节点属性key值，默认为children
 * @param {string} [isKey=true] 返回节点对象集合还是仅key值，默认仅key值
 * @returns {stringp[]}
 */
export function findLeafNode(
  dataSource: any[],
  key: string = 'id',
  childKey: string = 'children',
  isKey = true,
) {
  const stack: any[] = [];
  const dfs = (array: any[]) => {
    array.forEach((item) => {
      if (item[childKey] instanceof Array && item[childKey].length) {
        dfs(item[childKey]);
      } else {
        stack.push(isKey ? item[key] : item);
      }
    });
  };
  dfs(dataSource);

  return stack;
}

/**
 * 扁平化树结构数据
 * @export
 * @param {any[]}  source 树结构数据
 * @param {string} [childName='children'] 子节点属性key值，默认为children
 * @param {string} [nodeElName='id'] 取出属性key值，默认为id
 * @returns {allNodes: any[], allIds: string[]}
 */
export function flatArrayStructure(
  source: any[],
  childName: string = 'children',
  nodeElName: string = 'id',
) {
  const allNodes: any[] = []; // 所有去除childrens节点对象集合

  const dfs = (array: any[]) => {
    array.forEach((el) => {
      const cloneEl = cloneDeep(el);
      delete cloneEl[childName];
      allNodes.push(cloneEl);
      if (el[childName] instanceof Array && el[childName].length) {
        dfs(el[childName]);
      }
    });
  };

  dfs(source);
  const uniqNodes = uniqBy(allNodes, nodeElName);
  const uniqIds = uniqNodes.map((el) => el[nodeElName]);

  return { allNodes: uniqNodes, allIds: uniqIds };
}

/**
 * 广度优先查找指定深度数结构节点值集合
 * @export
 * @param {any[]} source 树结构数据
 * @param {string} key 取出属性key值，默认为id
 * @param {string} childKey 子节点属性key值，默认为children
 * @param {number} deep 遍历深度，默认为3
 * @returns
 */
export function bfsTreeDeep(
  source: any[],
  key: string = 'id',
  childKey: string = 'children',
  deep: number = 3,
) {
  if (!(source instanceof Array) || typeof deep !== 'number') {
    throw Error('憨批传参');
  }
  const result: any = [];
  const stack = cloneDeep(source);
  let count = 0;

  while (stack.length > 0 && count < deep) {
    [...stack].forEach((element) => {
      stack.shift();
      result.push(element[key]);
      if (element[childKey] instanceof Array && element[childKey].length) {
        stack.push(...element[childKey]);
      }
    });
    count += 1;
  }

  return result;
}

/**
 * 将图片对象转换为base64格式
 * @export
 * @param {blob} img 图片blob对象
 * @param {*} callback
 */
export function getImgBase64(img: Blob, callback: any) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

/** 树结构数据增、删、改 */
export function operationTreeStructured(
  type: 'add' | 'edit' | 'delete',
  source: any[],
  opreateId: number | string,
  targetNode: any,
) {
  const walker = (array: any[]) => {
    array.forEach((node, index) => {
      if (node.id === opreateId) {
        switch (type) {
          case 'add':
            node.children.push(targetNode);
            break;
          case 'edit':
            // eslint-disable-next-line no-param-reassign
            node.responsiblerName = targetNode.responsiblerName;
            // eslint-disable-next-line no-param-reassign
            node.responsiblerUserId = targetNode.responsiblerUserId;
            break;
          case 'delete':
            array.splice(index, 1);
            break;
          default:
            break;
        }
      }
      if (node.children instanceof Array && node.children.length) {
        walker(node.children);
      }
    });
  };

  walker(source);

  return source;
}

/**
 * 甘特图数据组装
 * @export
 * @param {any[]} source
 * @param {string | number} parentId
 * @returns
 */
export function parseGanttData(source: any[]) {
  if (!(source instanceof Array)) {
    throw Error('憨批传参');
  }

  const ganttData: any[] = [];
  const waklerData = (data: any[], parentId: string) => {
    data.forEach((item) => {
      const task: any = {
        id: item.id,
        text: item.name,
        start_date: moment().format('YYYY-MM-DD'),
        duration: 0,
      };

      if (parentId) task.parent = parentId;

      if (item.planStartDate) {
        const start = moment(item.planStartDate);
        task.start_date = start.format('YYYY-MM-DD');
        if (item.planCompleteDate) {
          const end = moment(item.planCompleteDate);
          task.duration = end.diff(start, 'days') + 1;
        }
      }

      ganttData.push(task);

      if (item.childrens instanceof Array && item.childrens.length) {
        waklerData(item.childrens, item.id);
      }
    });
  };

  waklerData(source, '');

  return ganttData;
}

export function matchFileType(contentType: string | null): string {
  if (!contentType) return 'iconunknown';
  const type = contentType.replace(/[.]/gi, '').trim().toLowerCase();
  if (['txt', 'pdf', 'dwg', 'exe', 'svg', 'pkg'].includes(type)) {
    return `icon${type}`;
  }
  if (['rar', 'zip'].includes(type)) {
    return 'iconzip';
  }
  if (['doc', 'docx'].includes(type)) {
    return 'iconword1';
  }
  if (['xls', 'xlsx'].includes(type)) {
    return 'iconexcel1';
  }
  if (['ppt', 'pptx'].includes(type)) {
    return 'iconppt1';
  }
  if (['mp4', 'avi', 'mov', 'rmvb', 'mkv'].includes(type)) {
    return 'iconvideo';
  }
  if (['png', 'jpg', 'gif', 'bmp'].includes(type)) {
    return 'iconPICTURE';
  }
  return 'iconunknown';
}

/** 重组结构树数据 */
export function reorganizeTree(source: any[]) {
  const result = [];
  const jsonData: any = {};
  source.forEach((item) => {
    jsonData[item.id] = item;
  });
  for (const key in jsonData) {
    if (key) {
      const { parentId } = jsonData[key];
      if (parentId === 0) {
        result.push(jsonData[key]);
      } else if (jsonData[parentId]) {
        jsonData[key].parentTitle = jsonData[parentId].name;
        if (!jsonData[parentId].children) {
          jsonData[parentId].children = [];
        }
        jsonData[parentId].children.push(jsonData[key]);
      }
    }
  }
  return result;
}

/**
 * 下载附件
 * @export
 * @param {(string | number)} id 附件attachmentContentId
 */
export function downloadAttachment(id: string | number) {
  window.open(`${urlPrefix}/Attachment/downloadAttachment/${id}`);
}

/**
 * 获取file对象
 * @export
 * @param {(string | number)} id 附件attachmentContentId
 * @returns
 */
export async function getFile(id: string | number) {
  return fetch(`${urlPrefix}/Attachment/downloadAttachment/${id}`).then((res) => res.blob());
}

/**
 * 浏览器返回
 */
export async function handleBack(isSession?: boolean) {
  if (isSession) {
    sessionStorage.setItem('Refresh', Math.random() + '1');
  }
  window.history.back();
}

const getIpNum = (ipAddress: any) => {
  /*获取IP数*/
  let ip = ipAddress.split('.');
  let a = parseInt(ip[0]);
  let b = parseInt(ip[1]);
  let c = parseInt(ip[2]);
  let d = parseInt(ip[3]);
  let ipNum = a * 256 * 256 * 256 + b * 256 * 256 + c * 256 + d;
  return ipNum;
};
const isInner = (userIp: any, begin: any, end: any) => {
  return userIp >= begin && userIp <= end;
};

export const IsLAN = (ipAddress: any) => {
  ipAddress.toLowerCase();
  if (ipAddress == 'localhost') return true;

  let ipNum = getIpNum(ipAddress);

  let isInnerIp = false; //默认给定IP不是内网IP

  let aBegin = getIpNum('10.0.0.0');

  let aEnd = getIpNum('**************');

  let bBegin = getIpNum('**********');

  let bEnd = getIpNum('***************');

  let cBegin = getIpNum('***********');

  let cEnd = getIpNum('***************');

  let dBegin = getIpNum('*********');

  let dEnd = getIpNum('***************');

  isInnerIp =
    isInner(ipNum, aBegin, aEnd) ||
    isInner(ipNum, bBegin, bEnd) ||
    isInner(ipNum, cBegin, cEnd) ||
    isInner(ipNum, dBegin, dEnd);

  return isInnerIp;
};

/**
 * 是否为有效的文件id
 */
export const isValidAttachId = (attachId: string | null | undefined) => {
  if (!attachId || attachId === '0') return false;
  return true;
};

/**
 * 组装文件list预览信息
 */
export const assembleFileList = (
  fileListOrFile: any,
  majorKey: string = 'attachId',
  id: any = '',
) => {
  if (Array.isArray(fileListOrFile)) {
    return fileListOrFile
      .filter((item) => isValidAttachId(item[majorKey]))
      .map((item) => {
        return {
          name: item.name || '无文件名文件~',
          id: id,
          fileId: item[majorKey],
          url: `${urlPrefix}/Attachment/downloadAttachment/${item[majorKey]}`,
        };
      });
  }
  return [
    {
      name: fileListOrFile?.name || '无文件名文件~',
      id: id,
      fileId: fileListOrFile?.[majorKey],
      url: `${urlPrefix}/Attachment/downloadAttachment/${fileListOrFile?.[majorKey]}`,
    },
  ].filter((item) => isValidAttachId(item.fileId));
};

/**
 * 提交接口时，处理文件list
 */
export const getAttachIdAsFileList = (fileList: any[], majorKey: string = 'id') => {
  return (fileList || [])
    .filter((item) => isValidAttachId(item[majorKey]))
    .map((item) => item[majorKey]);
};

/**
 * 提交接口时，处理文件list
 */
export const getIdAsFileList = (fileList: any[], majorKey: string = 'fieldId') => {
  return (fileList || []).filter((item) => isValidAttachId(item[majorKey]));
};

/**
 * 提交处理图片
 */
export const getAttachIdAsImg = (fileList: any[], majorKey: string = 'fileId') => {
  return (fileList || []).filter((item) => isValidAttachId(item[majorKey]));
};

/**
 * 组装图片信息
 *
 */ export const assembleImage = (attachtId: any, isArr: boolean = false) => {
  if (isValidAttachId(attachtId)) {
    if (isArr) {
      const list = attachtId.split(',');
      return list.map((item: string) => {
        return {
          id: item,
          fileId: item,
          url: `${urlPrefix}/Attachment/downloadAttachment/${item}`,
        };
      });
    } else {
      return [
        {
          id: attachtId,
          fileId: attachtId,
          url: `${urlPrefix}/Attachment/downloadAttachment/${attachtId}`,
        },
      ];
    }
  } else {
    return [];
  }
};

//下载图片返回能直接给Image组件的url
export const getImg = (id: any) => {
  return `${urlPrefix}/Attachment/downloadAttachment/${id}`;
};

//把获取到的图片转换成本地url可以给IMg预览
export const getLocalImg = async (id: any) => {
  try {
    const response = await fetch(getImg(id));
    const blob = await response.blob();
    const objectUrl = URL.createObjectURL(blob);
    return objectUrl; // 返回本地 URL
  } catch (error) {
    console.error('Error getting local image:', error);
    return undefined; // 处理错误情况，返回 null 或者其他适当的值
  }
};

//遍历树节点只取出Id拼成一个数组
export function findAllIds(nodes: any[]): any[] {
  let ids: any = [];
  // 遍历数组中的每个节点
  nodes.forEach((node) => {
    // 如果节点有id属性，则添加到数组中
    if (node.id) {
      ids.push(node.id);
    }
    // 如果节点有子节点数组，递归地调用findAllIds并合并结果
    if (node.children && node.children.length > 0) {
      ids = ids.concat(findAllIds(node.children));
    }
  });
  return ids;
}
//组装显示图片的参数
export function getImgParams(attachmentId: string) {
  if (attachmentId === '0' || !attachmentId) return [];
  const imglist = attachmentId.split(',');
  const res = imglist.map((item: any) => ({
    url: `${urlPrefix}/Attachment/downloadAttachment/${item}`,
    fileId: item,
    id: '',
  }));
  return res;
}
//上传分解图片拼成字符串
export function getImgIds(fileList: any) {
  const ids = fileList.map((item: any) => item.fileId).join(',');
  return ids;
}

type TreeNode<T> = T & { children?: TreeNode<T>[] };

export const deepFn = <T>(
  arr: TreeNode<T>[] = [],
  callback: (item: TreeNode<T>) => void = () => {},
) => {
  arr.forEach((item: TreeNode<T>) => {
    if (item.children?.length) {
      deepFn(item.children, callback);
    }
    callback(item);
  });
};

// 驼峰转下划线函数
export const camelToUnderscore = (str: string) => {
  return str.replace(/([A-Z])/g, '_$1')?.toLowerCase();
};
