import { DragModal } from '@/components/drag-modal/drag-modal.component';
import styles from './duty-record.component.less';
import { ProcessButtonsStyle } from '@/constants/process-buttons.style';
import { Button, Form, DatePicker, Input, Select, Table } from 'antd';
import React, { useState, memo } from 'react';
import { THIRD_PARTY_TYPE_LIST } from '@/pages/duty-manage/utils/contant';

const data = [
  {
    id: 1,
    time: '2021-01-01',
    content:
      '汉武帝刘彻（前156年—前87年），西汉第七位皇帝（前141年—前87年在位），中国历史上杰出的政治家、军事家、战略家、文学家。 [249-250]汉景帝刘启与王皇后之子',
    attachmentType: '正常记事',
    source: '手动录入',
  },
  {
    id: 2,
    time: '2021-01-01',
    content: '刘彻初封胶东王，七岁被立为皇太子，十六岁继承皇位，在位五十四年，功业甚多',
    attachmentType: '操作票',
    source: '采集',
  },
];
export default memo(function DutyRecordComponent() {
  const [open, setOpen] = useState(false); // 新增弹窗
  const [pageInfo, setPageInfo] = useState({ pageNum: 1, pageSize: 10 });
  const [total, setTotal] = useState(data.length);
  const [form] = Form.useForm();

  // 获取列表
  const getList = async (obj = {}) => {
    console.log(obj);
  };

  // 关闭弹窗
  const handleCancel = () => {
    setOpen(false);
  };

  // 新增
  const handleOk = () => {
    setOpen(false);
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (_text: any, _record: any, index: number) => index + 1,
      width: 80,
    },
    {
      title: '发生时间',
      dataIndex: 'time',
      key: 'time',
      width: 120,
    },
    {
      title: '记事内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
    },
    {
      title: '记事类型',
      dataIndex: 'attachmentType',
      key: 'attachmentType',
      width: 120,
    },
    {
      title: '内容来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
    },
  ];

  return (
    <React.Fragment>
      <div className={styles['duty-record-component']}>
        <div className={styles['header']}>
          <ProcessButtonsStyle>
            <Button type="primary" onClick={() => setOpen(true)}>
              新增
            </Button>
            <Button type="primary">删除</Button>
          </ProcessButtonsStyle>
        </div>
        <Table<(typeof data)[0]>
          rowSelection={{
            type: 'checkbox',
            onChange: (selectedRowKeys, selectedRows) => {
              console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
            },
          }}
          rowKey={(record) => record.id}
          columns={columns}
          dataSource={data}
          pagination={{
            size: 'small',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            total: Number(total),
            pageSizeOptions: [10, 20, 50, 100],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (pi, ps) => {
              setPageInfo({ pageNum: pi, pageSize: ps });
              getList({ pageNum: pi, pageSize: ps });
            },
          }}
        />
      </div>
      <DragModal
        okText="确定"
        cancelText="取消"
        title="新增值班记录"
        open={open}
        onCancel={handleCancel}
        onOk={handleOk}
        width={500}
        // destroyOnClose // 关闭时销毁 Modal 里的子元素
        forceRender // 强制渲染 Modal
        // footer={false}
      >
        <Form form={form}>
          <Form.Item label="发生时间" name="time">
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item label="记事内容" name="content">
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item label="记事类型" name="attachmentId">
            <Select options={THIRD_PARTY_TYPE_LIST} />
          </Form.Item>
        </Form>
      </DragModal>
    </React.Fragment>
  );
});
