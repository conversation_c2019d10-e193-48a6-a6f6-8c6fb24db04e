import { useEffect, useState } from 'react';
import { request } from '@/utils/request';

export default function useTreeByConfigure() {
  const [treeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTreeData();
  }, []);

  // 获取标段树
  const getTreeData = async () => {
    setLoading(true);
    const { flag, code, data } = await getOrganizationTree({ id: 0, flag: 1 });
    if (flag && code === '1') {
      setTreeData(data);
    }
    setLoading(false);
  };
  return {
    treeData,
    loading,
  };
}

/**
 * 获取组织机构树
 * @export
 * @param {number} parentId 父级组织机构Id
 * @param {string} name 组织机构名称
 * @param {string} code 组织机构编码
 * @returns {Promise<Global.Response>}
 */
export async function getOrganizationTree(params: object): Promise<any> {
  return request.get(`/sys/Organization/tree`, { params });
}
