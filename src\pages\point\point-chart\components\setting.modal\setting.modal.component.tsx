import { useState, useEffect } from 'react';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { Form, Input, Button, Space } from 'antd';
import { createRoot } from 'react-dom/client';

interface IProps {
  callback: (params: Record<string, string>) => Promise<void>;
  closeModalCallback?: () => void;
  initialValues?: Record<string, any>;
}

const SettingModalContent = (props: IProps) => {
  const [form] = Form.useForm();
  const [showPointName, setShowPointName] = useState('');

  useEffect(() => {
    if (props.initialValues) {
      form.setFieldsValue({ ...props.initialValues });
      setShowPointName(props.initialValues.pointName || '');
    }
  }, [props.initialValues]);

  // 关闭
  const handleCancel = () => {
    props?.closeModalCallback?.();
  };

  // 保存
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await props?.callback(values);
      handleCancel();
    } catch (error) {
      // 表单验证失败
      console.error('表单验证失败:', error);
    }
  };

  return (
    <DragModal
      title="测点限值设置"
      open={true}
      onCancel={handleCancel}
      destroyOnClose
      width={450}
      mask={true}
      centered
      forceRender
      footer={false}
    >
      <Form form={form}>
        {showPointName && (
          <Form.Item name="pointName" label="测点名称">
            <Input readOnly />
          </Form.Item>
        )}
        <Form.Item
          label="测点限值（大于）"
          name="greaterThan"
          rules={[{ required: true, message: '请输入大于限值' }]}
        >
          <Input placeholder="请输入大于限值" />
        </Form.Item>
        <Form.Item
          label="测点限值（小于）"
          name="lessThan"
          rules={[{ required: true, message: '请输入小于限值' }]}
        >
          <Input placeholder="请输入小于限值" />
        </Form.Item>
        <Form.Item
          label=""
          labelCol={{ span: 0 }}
          wrapperCol={{ span: 24 }}
          style={{ textAlign: 'right' }}
        >
          <Space size={10} align="center">
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </DragModal>
  );
};

export const SettingModal = {
  show: (options: {
    callback: (params: Record<string, string>) => Promise<void>;
    closeCallback?: () => void;
    initialValues?: Record<string, any>;
  }) => {
    const div = document.createElement('div');
    document.body.appendChild(div);
    const root = createRoot(div);

    const closeModal = () => {
      root.unmount();
      div.remove();
      options?.closeCallback?.();
    };

    root.render(<SettingModalContent {...options} closeModalCallback={closeModal} />);
  },
};
