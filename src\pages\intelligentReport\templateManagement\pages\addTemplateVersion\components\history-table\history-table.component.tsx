import {
  CellControllerOptions,
  DataTypeEnum,
  RegionsOptions,
} from '@/components/spread-table/spread-table';
import { ColumnsOptions } from '@/components/table-component/table';
import { TableComponent } from '@/components/table-component/table-component';
import { Dropdown, Input, Select, Space, Tooltip, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import {
  FC,
  ReactNode,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { PropOptions, SelectOptions, UseImperativeOptions, planCycleList } from './history-table';
import { CheckOutlined, DeleteOutlined, DownOutlined } from '@ant-design/icons';
import styles from '../../add-template-version.page.less';
import style from './history-table.component.less';
import { validateExcelPosition } from '@/utils/utils';
import { temPlanAttribute } from '@/services/intelligentReport/template-management';

const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const HistoryTable: FC<PropOptions> = memo(
  forwardRef(
    (
      { tableList, inputFocusFn, allTableList, tableListChange, isEdit, spreadRef, otherTableList },
      ref,
    ) => {
      const otherTableLRef = useRef<CellControllerOptions[]>([]);
      //表格字段配置
      const columns: ColumnsType<ColumnsOptions> = [
        {
          title: '位置',
          dataIndex: 'startPosition',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => tableInput(record, 'startPosition', 'input'),
        },
        {
          title: '周期',
          dataIndex: 'planCycle',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) =>
            tableInput(record, 'planCycle', 'select', planCycleList),
        },
        {
          title: '操作',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => controllerComponent(record),
        },
      ];
      useImperativeHandle<any, UseImperativeOptions>(ref, () => ({
        addHistoryRange,
        tableHistoryEvent,
      }));
      useEffect(() => {
        otherTableLRef.current = otherTableList;
      }, [otherTableList]);
      //Input聚焦处理
      function onFoucsHandler(data: RegionsOptions): void {
        if (data.isEdit) {
          inputFocusFn(data, DataTypeEnum.HISTORY);
        }
      }
      //测点表格输入框改变
      function tableInputChange({
        value,
        field,
        data,
        type,
      }: {
        value: string;
        field: string;
        data: RegionsOptions;
        type: 'input' | 'select';
      }): void {
        const list = allTableList.map((item) => {
          if (item.id === data.id) {
            item[field] = type === 'input' ? value.toUpperCase() : value;
          }
          return item;
        });
        tableListChange(list);
      }
      //表格输入框
      function tableInput(
        data: RegionsOptions,
        field: string,
        type: 'input' | 'select',
        options?: SelectOptions[],
      ): ReactNode {
        const curData = options?.find((item) => item.key === data[field]);
        if (data.isEdit) {
          return type === 'input' ? (
            <Input
              onFocus={() => onFoucsHandler(data)}
              onChange={(e) =>
                tableInputChange({
                  value: e.target.value,
                  field,
                  data,
                  type,
                })
              }
              value={data[field]}
            />
          ) : (
            <Dropdown
              menu={{
                items: options,
                onClick: ({ key }) =>
                  tableInputChange({
                    value: key,
                    field,
                    data,
                    type,
                  }),
              }}
            >
              <span title={curData?.label} className={style['dropdown-menu-item']}>
                {curData ? curData.label : '请选择'}
                <DownOutlined className={style['dropdown-menu-item-arrow']} />
              </span>
            </Dropdown>
          );
        } else {
          return curData ? curData.label : data[field];
        }
      }
      //表格操作栏渲染
      function controllerComponent(data: RegionsOptions): ReactNode {
        const del = (
          <Tooltip title="删除">
            <DeleteOutlined onClick={() => delTableItem(data)} className={styles.icon} />
          </Tooltip>
        );
        const sure = (
          <Tooltip title="确定">
            <CheckOutlined onClick={() => sureTableData(data)} className={styles.icon} />
          </Tooltip>
        );
        return (
          <div className={styles['table-controller-container']}>
            {isEdit && (
              <Space size="middle">
                {data.isEdit && sure}
                {del}
              </Space>
            )}
          </div>
        );
      }
      //获取当前工作表数据
      function getWorkTableData(): any {
        const sheetSnapshot = spreadRef.current.getWorkTableData();
        return sheetSnapshot;
      }
      //新增选区
      function addHistoryRange(): void {
        const table = getWorkTableData();
        const data: RegionsOptions = {
          id: new Date().getTime().toString(),
          tableId: table.name(),
          tableName: table.name(),
          startPosition: '', //开始行
          endPosition: '', //开始列
          rowCount: 1, //行数
          colCount: 1, //列数
          editUserId: userId, //可操作用户id
          isEdit: true,
          planCycle: planCycleList[0].key,
          attributeId: '',
          type: '1', //历史数据
        };
        tableListChange((prev) => {
          return prev.concat(data);
        });
      }
      //表格历史数据绑定事件
      function tableHistoryEvent(): void {
        const data = spreadRef.current.getActiveCell();
        if (data) {
          const table = getWorkTableData();
          const isMerged = spreadRef.current.cellIsMerged(table, data.selection);
          const { rowCount, colCount, col, row } = data.selection;
          if (isMerged || (rowCount === 1 && colCount === 1)) {
            const plan: RegionsOptions = {
              id: new Date().getTime().toString(),
              tableId: table.name(),
              tableName: table.name(),
              startPosition: data.start, //开始
              endPosition: data.end, //结束
              rowCount: row, //行索引
              colCount: col, //列索引
              editUserId: userId, //操作用户id
              isEdit: true,
              planCycle: planCycleList[0].key,
              attributeId: '',
              type: '1', //历史数据
            };
            tableListChange((prev) => {
              if (verifyList(plan)) {
                return prev.concat(plan);
              } else {
                message.warning('该单元格已经有绑定的数据了！');
                return prev;
              }
            });
          } else {
            message.warning('只能绑定一个单元格');
          }
        }
      }
      //确定提交表格数据
      function sureTableData(data: RegionsOptions): void {
        const { startPosition } = data;
        const row = validateExcelPosition(startPosition);
        if (!row.valid) {
          message.warning('请输入有效的Excel位置，例如 "A1"、"B2"');
        } else if (row.column) {
          const verify = verifyList(data);
          if (verify === false) {
            message.warning('该单元格已经有绑定的数据了！');
          } else {
            const { col, row } = spreadRef.current.getCellRange(
              `${startPosition}:${startPosition}`,
            );
            const list = allTableList.map((item) => {
              if (item.id === data.id) {
                item.isEdit = false;
                item.endPosition = startPosition;
                item.colCount = col; //列索引
                item.rowCount = row; //行索引
              }
              return item;
            });
            tableListChange(list);
          }
        }
      }
      //校验绑定是否重复
      function verifyList(data: RegionsOptions): boolean {
        //去掉已经存在列表中得比较对象
        const newList = otherTableLRef.current.filter((item) => item.id !== data.id);
        return !newList.some((item) => {
          if (item.startPosition === data.startPosition) {
            return true;
          } else {
            return false;
          }
        });
      }
      //删除表格子项
      function delTableItem(data: RegionsOptions): void {
        const list = allTableList.filter((item) => item.id !== data.id);
        tableListChange(list);
      }
      return (
        <>
          <TableComponent isIndex={false} columns={columns} tableList={tableList}></TableComponent>
        </>
      );
    },
  ),
);
