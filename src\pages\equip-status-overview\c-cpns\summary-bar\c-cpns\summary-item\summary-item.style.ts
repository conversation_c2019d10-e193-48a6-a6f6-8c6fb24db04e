import styled from 'styled-components';

export const SummaryItemWrapper = styled.div`
  height: 100%;
  width: 16%;
  border-radius: 16px;
  background: rgba(40, 130, 232, 0.2);
  display: flex;

  .left {
    width: 35%;
    display: flex;
    justify-content: center;
    align-items: center;

    & > img {
      height: 25%;
      width: fit-content;
    }
  }

  .right {
    flex: 1;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;

    & > .title {
      font-size: 18px;
      font-weight: bold;
    }

    & > .text {
      margin-top: 5%;

      & > span.num {
        font-size: 20px;
        color: #0ae4ff;
        font-weight: bold;
      }

      & > span.unit {
        font-size: 14px;
      }
    }
  }
`;
