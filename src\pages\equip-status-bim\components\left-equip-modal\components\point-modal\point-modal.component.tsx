import { FC, memo, useEffect, useState } from 'react';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { PropOptions, paramsOptions, PointTypes } from './point-modal';
import { Form, Input, Select } from 'antd';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsOptions } from '@/components/table-component/table';
import { ColumnsType } from 'antd/es/table';
import styles from './point-modal.component.less';
import { getPointList } from '@/pages/point/point.service';

export let params = JSON.parse(JSON.stringify(paramsOptions));
export const PointModal: FC<PropOptions> = memo(({ onOk, open, close }) => {
  //测点列表
  const [pointList, setPointList] = useState<any[]>([]);
  //表格数据总数
  const [total, setTotal] = useState<number>(0);
  //加载控制
  const [loading, setLoading] = useState<boolean>(false);
  //已选择测点
  const [selectList, setSelectList] = useState<any[]>([]);
  const [forms] = Form.useForm();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '名称',
      dataIndex: 'pointName',
      align: 'center',
      ellipsis: true,
      width: 480,
    },
    {
      title: '编码',
      dataIndex: 'pointCode',
      align: 'center',
      ellipsis: true,
      width: 250,
    },
    {
      title: '类型',
      dataIndex: 'pointType',
      align: 'center',
      ellipsis: true,
      width: 70,
    },
  ];
  //表格选择配置
  const rowSelection = {
    selectedRowKeys: selectList.map((item) => item.pointCode),
    onChange: onSelectChange,
  };
  useEffect(() => {
    if (open && pointList.length === 0) {
      params = JSON.parse(JSON.stringify(paramsOptions));
      query();
    }
  }, [open]);
  //表格行点击
  function onTableRowClick(data: any): void {
    const node = selectList.find((item) => item.pointCode === data.pointCode);
    if (node) {
      setSelectList(selectList.filter((item) => item.pointCode !== data.pointCode));
    } else {
      setSelectList(selectList.concat(data));
    }
  }
  //表格选中处理
  function onSelectChange(newSelectedRowKeys: React.Key[], selectedRows: any[]): void {
    setSelectList(selectedRows);
  }
  //关闭处理
  function closeHandler(): void {
    close();
  }
  //确定选择
  function sureHandler(): void {
    onOk(selectList);
  }
  //数据查询
  function query(param?: any): void {
    setLoading(true);
    setSelectList([]);
    if (param) {
      params = { ...params, ...param };
    }
    getPointList(params)
      .then((res) => {
        if (res.code === '1') {
          if (res.data.list) {
            setPointList(res.data.list);
          }
          setTotal(parseInt(res.data.total));
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //页码切换
  function pageChange(page: number, pageSize: number): void {
    query({
      pageSize: pageSize,
      pageNum: page,
    });
  }
  //条件查询
  function paramsQuery(): void {
    const param = forms.getFieldsValue();
    params = JSON.parse(JSON.stringify(paramsOptions));
    query(param);
  }
  //已选中测点重置
  function reset(): void {
    setSelectList([]);
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    query();
  }
  return (
    <SvgModal
      width="1000px"
      height="530px"
      close={closeHandler}
      isFooterBtn={false}
      open={open}
      title="对比测点"
    >
      <Form
        form={forms}
        layout="inline"
        initialValues={{ layout: 'inline' }}
        style={{ maxWidth: 'none', paddingLeft: '23px' }}
      >
        <Form.Item label="名称" name="pointName">
          <Input style={{ width: '170px' }} placeholder="请输入..." />
        </Form.Item>
        <Form.Item label="编码" name="pointCode">
          <Input style={{ width: '170px' }} placeholder="请输入..." />
        </Form.Item>
        <Form.Item label="类型" name="pointType">
          <Select options={PointTypes} style={{ width: '170px' }} placeholder="请选择..." />
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={paramsQuery}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={reset}>
            重置测点
          </type-button>
        </Form.Item>
      </Form>
      <TableComponent
        isVirtual={true}
        onTableRowClick={onTableRowClick}
        rowKey="pointCode"
        rowSelection={rowSelection}
        params={{
          pageChange: pageChange,
          total: total,
          pageSize: params.pageSize,
          current: params.pageNum,
        }}
        loading={loading}
        scroll={{ y: 220 }}
        columns={columns}
        tableList={pointList}
      ></TableComponent>
      <div className={styles.footer}>
        <type-button onClick={closeHandler}>取消</type-button>
        <type-button onClick={sureHandler}>确定</type-button>
      </div>
    </SvgModal>
  );
});
