import React, { useCallback, useRef, useState } from 'react';
import { message, Modal, Spin } from 'antd';
import styles from './point-analysis.page.less';
import LegendListModal from '@/components/modal-point-list/mpl.component';
import { motion } from 'framer-motion';
import { containerVariants, itemVariants } from './config';
import classNames from 'classnames';
import _ from 'lodash';
import { Pointlist } from '@/pages/point/type';
import AnalysisTemplate from './components/analysis-template/analysis.template.component';
import { PointAnalysis } from '@/pages/point/type';
import { useMount } from 'ahooks';
import DetailHeader from './components/detail-top/detail.header.component';
import { handleSave, handleGetHistoryData } from './server';
import { exportHistoryData } from '@/pages/point/point.service';
import useQueryParams from '@/hooks/useQueryParams';
import DetailTop from '@/components/detail-top/detail.top.component';
import { showLoading, hideLoading } from '@/utils/global-loading';
import { usePointAnalysisContext } from '@/pages/point/point-chart/point-analysis.provider';
import { useDispatch } from 'umi';

export interface IBasePointDTO extends Pointlist.List {
  historyDatas: any[];
  pointExceedsLimits?: number;
  pointlessThanLimits?: number;
}
export interface ITemplates {
  ord: number;
  name: string;
  basePointDTOS: IBasePointDTO[];
  exceedsLimits?: number;
  lessThanLimits?: number;
  type: 'db' | 'new';
}
export default function DetailMeasurePointAnalysis() {
  // 路由传参
  const { id } = useQueryParams() as {
    id: string | undefined;
  };
  const [layout, setLayout] = useState<1 | 2 | 3>(1); // 三种布局：单列、双列、三列
  const [selectedTemplates, setSelectedTemplates] = useState<number>(-1); // 选中的图表
  const modalRef = useRef<React.ElementRef<typeof LegendListModal>>(null); // 测点列表弹窗 ref
  const [loading, setLoading] = useState<boolean>(false); // 控制模板显示（改变布局时让charts重新渲染动画）
  const { getTime, getDetail, detail, templates, setState, templateCount } =
    usePointAnalysisContext();
  const dispatch = useDispatch();

  // 获取详情
  useMount(async () => {
    await getDetail(true);
    await getPointType();
  });

  // 获取测点类型（字典）
  const getPointType = async () => {
    dispatch({
      type: 'point/fetchDevices',
    });
  };

  // 导出
  const handleExport = async (point: ITemplates) => {
    if (!detail) {
      message.warning('暂无测点图例数据');
      return;
    }
    const params = {
      basePointList: [...point.basePointDTOS],
      selectBegin: detail?.startTime,
      selectEnd: detail?.endTime,
    };
    showLoading();
    await exportHistoryData(params);
    hideLoading();
  };

  // 新建图例
  const createLegend = async (rows: Pointlist.List[], type: string) => {
    const params = {
      basePointList: rows,
      ...getTime('selectBegin', 'selectEnd'), // 时间参数
    } as PointAnalysis.HistoryQuery;
    const result = await handleGetHistoryData(params);
    /**
     * rows 勾选的测点
     * 遍历勾选的测点，对应匹配对应的历史数据
     */
    const basePointDTOS = rows.map((item) => ({
      ...item,
      historyDatas: result?.find((__) => __.pointCode === item.pointCode)?.historyDatas || [], // 历史数据
    }));
    const newTem = [
      ...templates,
      {
        ord: new Date().getTime(), // 排序
        name: `${rows[0]?.pointName}测点数据分析`, // 名称
        type: 'new' as const, // 区分新建和数据库，便于删除操作
        basePointDTOS,
        chartType: type, // 遥信阶梯折线图
      },
    ];
    setState({ templates: newTem });
  };

  // 图例中选择测点
  const editLegends = async (rows: Pointlist.List[], type: string) => {
    // 匹配当前图例
    const nowLegend = templates.find((item) =>
      _.isEqual(item.ord, selectedTemplates),
    )?.basePointDTOS;
    // 计算出 rows 和 nowLegend 并集
    // 过滤 undefined（因为分页只会请求第一页数据，如果是曾经的数据，获取不到）
    const allRows = _.unionBy(rows, nowLegend || [], 'pointCode')?.filter(
      (item) => !_.isUndefined(item),
    );
    const params = {
      basePointList: allRows,
      ...getTime('selectBegin', 'selectEnd'), // 时间参数
    } as PointAnalysis.HistoryQuery;
    const result = await handleGetHistoryData(params);
    const basePointDTOS = allRows.map((item) => ({
      ...item,
      historyDatas: result?.find((__) => __.pointCode === item.pointCode)?.historyDatas || [], // 历史数据
    }));
    const newTem = templates.map((tem) => {
      if (_.isEqual(tem.ord, selectedTemplates)) {
        return {
          ...tem,
          basePointDTOS: basePointDTOS,
          chartType: type, // 遥信阶梯折线图
        };
      }
      return tem;
    });
    setState({ templates: newTem });
    setSelectedTemplates(-1); // 点击确定后清空选中
  };
  // 测点列表多选 - 确定
  const handleChoosePoint = useCallback(
    async (rows: Pointlist.List[], type: string) => {
      if (_.isEqual(selectedTemplates, -1)) {
        await createLegend(rows, type);
      } else {
        await editLegends(rows, type);
      }
    },
    [selectedTemplates, templates, getTime],
  );

  // 布局切换
  const handleLayoutChange = (newLayout: 1 | 2 | 3) => {
    if (templateCount === 0) {
      message.warning('暂无图例，请先新建图例');
      return;
    }
    setLoading(true); // 先隐藏模板
    setLayout(newLayout);
    setTimeout(() => {
      setLoading(false); // 短暂延时后重新显示，重绘 g2charts
    }, 150);
  };

  // 删除图表
  const handleRemoveChart = useCallback(
    (item: ITemplates) => {
      // 如果是新增的图表，则直接删除
      if (_.isEqual(item.type, 'new')) {
        const newTem = templates.filter((_) => _.ord !== item.ord);
        setState({ templates: newTem });
      } else {
        Modal.confirm({
          title: '提示',
          content: '确定要删除该图表吗？',
          onOk: async () => {
            // 如果是数据库中已保存的图表，则需要重新保存
            const afterDelete = templates.filter((_) => _.ord !== item.ord);
            const params = {
              id: id as string,
              name: detail?.name,
              analyseDataDTOList: afterDelete,
              thumbnailId: detail?.thumbnailId,
              ...getTime('startTime', 'endTime'),
            };
            setLoading(true);
            // 保存
            await handleSave(params as PointAnalysis.Update);
            getDetail(true);
            setLoading(false);
          },
        });
      }
    },
    [templates, detail, getTime],
  );

  // 删除对比测点
  const handleRemovePoint = useCallback(
    (point: ITemplates['basePointDTOS'][0], itemTemplate: ITemplates) => {
      const newTem = templates.map((template) => {
        return template.ord === itemTemplate.ord
          ? {
              ...template,
              basePointDTOS: template.basePointDTOS?.filter((_) => _.pointCode !== point.pointCode),
            }
          : template;
      });
      setState({ templates: newTem });
    },
    [templates],
  );

  /**
   * 选中图表
   * @param item 图表
   */
  const handleSelect = useCallback((item: ITemplates) => {
    setSelectedTemplates(item.ord); // 记录当前选中的图表模板
    modalRef.current?.openModal('edit', item.basePointDTOS); // 回显勾选
  }, []);

  return (
    <>
      <section className={styles.container}>
        <div className={styles['header-fixed']}>
          <DetailTop title={`测点图例分析 - ${detail?.name}`} />
          <DetailHeader
            addLegend={() => {
              modalRef.current?.openModal('create');
              setSelectedTemplates(-1);
            }}
            handleLayoutChange={(newLayout: 1 | 2 | 3) => handleLayoutChange(newLayout)}
            layout={layout}
          />
        </div>
        <motion.div
          className={classNames(styles['template-container'], {
            [styles['layout-1']]: layout === 1,
            [styles['layout-2']]: layout === 2,
            [styles['layout-3']]: layout === 3,
          })}
          variants={containerVariants} // 绑定动画
          animate="visible" // 动画状态
          initial="hidden" // 初始状态
        >
          {loading ? (
            <Spin
              size="large"
              style={{
                position: 'fixed',
                top: '50%',
                left: 'calc(50% + 114px)', // 侧栏 228PX
                transform: 'translate(-50%, -50%)',
              }}
              spinning={loading}
            />
          ) : (
            templates.map((item) => (
              <motion.div
                key={item.ord}
                variants={itemVariants} // 绑定动画
                animate="visible"
                initial="hidden"
                layout
              >
                <AnalysisTemplate
                  handleRemoveChart={() => handleRemoveChart(item)} // 删除图表
                  handleSelect={() => handleSelect(item)} // 新增测点
                  handleRemovePoint={(point) => handleRemovePoint(point, item)} // 删除对比测点
                  handleExport={() => handleExport(item)} // 导出
                  layout={layout}
                  points={item}
                  chartType={item?.chartType || ''}
                />
              </motion.div>
            ))
          )}
        </motion.div>
      </section>
      <LegendListModal
        ref={modalRef}
        callback={(rows: Pointlist.List[], type) => handleChoosePoint(rows, type)} // 选择测点回调
      />
    </>
  );
}
