{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "importHelpers": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "paths": {"@/*": ["src/*"], "@@/*": ["./src/.umi/*"]}, "plugins": [{"name": "@styled/typescript-styled-plugin", "tags": ["styled", "css", "sty"]}], "allowSyntheticDefaultImports": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts", "src/public-path.js", "src/global.d.ts", "src/pages/safety-inspection-problem/safety-inspection-problem-add/securityCheck-plan-add/c-cpns/forword-Form/forword-Form.style.ts"], "exclude": ["node_modules", "lib", "es", "dist", "typings", "**/__test__", "test", "docs", "tests", "src/.umi/*"]}