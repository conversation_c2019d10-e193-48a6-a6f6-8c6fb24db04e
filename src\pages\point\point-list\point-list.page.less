.page {
  overflow: hidden;
  &-topbar {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    padding: 12px 16px 0 16px;
    align-items: center;
    align-content: flex-start;
    gap: 16px;
    flex-wrap: wrap;
  }

  &-content {
    padding: 16px 16px 0 16px;
    :global {
      .ant-table-column-sort {
        background-color: transparent !important;
      }
    }
  }
}

.link-btn {
  color: #8fd4ff;
}

//按钮样式
.formButton {
  margin-bottom: 10px;
  margin-left: 10px;
  display: flex;
}

.formContent {
  display: flex;
}

.formButton_left {
  margin-right: 20px;
}
.page-content {
  height: calc(100vh - 115px);
}
