import styled from 'styled-components';

export const BaseFormWrapper = styled.div`
  form.ant-form {
    & > .ant-form-item {
      width: 15%;
      margin-right: 0;

      /* Input */
      input.ant-input {
        border-color: #1577a5;
      }

      /* InputNumber */
      .ant-input-number-group-wrapper {
        width: 100%;

        & > .ant-input-number-wrapper {
          & > .ant-input-number {
            border-color: #1577a5;
          }

          & > .ant-input-number-group-addon {
            color: #bbb;
            border-color: #1577a5;
          }
        }
      }

      /* Textarea */
      textarea.ant-input {
        border-color: #1577a5;
      }

      /* Selector */
      .ant-select-selector {
        border-radius: 5px !important;

        & > .ant-select-selection-item {
          color: #fff;
        }
      }

      // multiple/tags with disabeld
      .ant-select-disabled.ant-select-multiple
        .ant-select-selection-overflow
        .ant-select-selection-item {
        color: #fff;
      }

      // 下拉图标
      .ant-select-arrow {
        color: #fff;
      }

      // 清除图标
      span.ant-select-clear {
        background: #091f4c !important;
        color: #475779 !important;
      }

      /* Radio */
      span.ant-radio {
        top: 0;

        &:not(.ant-radio-checked) > .ant-radio-inner {
          background-color: transparent;
        }

        &.ant-radio-disabled .ant-radio-inner::after {
          background-color: #b8b8b8;
        }

        & + span {
          color: #fff;
        }
      }

      /* Switch */
      button.ant-switch {
        border: none !important;
        outline: 2px solid #1577a5;

        &.ant-switch-checked {
          outline-color: #1677ff;
        }
      }

      /* DatePicker */
      .ant-picker {
        width: 100%;

        .ant-picker-input > input {
          color: #fff;

          // placeholder color
          &::placeholder {
            color: #bbb !important;
          }
        }

        // svg logo
        .ant-picker-range-separator svg,
        .ant-picker-suffix svg {
          fill: #bbb;
        }

        // 清除图标
        span.ant-picker-clear {
          background: #091f4c !important;
          color: #475779 !important;
        }
      }

      /* Upload */
      .ant-upload-icon {
        // paper clip icon
        & > span.anticon.anticon-paper-clip {
          color: #8fd4ff;
        }

        // 文件名（预览）
        & + a {
          color: #8fd4ff;
        }
      }

      // 上传列表
      .ant-upload-wrapper {
        .ant-upload-list {
          // 上传入口
          .ant-upload.ant-upload-select {
            // 非hover
            &:not(:hover) {
              border-color: #1578a5;
            }

            &.ant-upload-disabled {
              border-color: #1578a5;

              & > span.ant-upload-disabled {
                color: #bbb;
              }
            }
          }

          // 上传项
          .ant-upload-list-item {
            border-color: #1578a5;

            // delete icon
            span.ant-upload-list-item-actions {
              span.anticon.anticon-delete {
                color: #ffffff72;
              }
            }
          }
        }
      }
    }
  }
`;
