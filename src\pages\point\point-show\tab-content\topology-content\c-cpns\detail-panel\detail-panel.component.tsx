import React, { memo, useEffect, useMemo, useState } from 'react';
import type { ReactNode, FC } from 'react';
import { Badge, Descriptions, Table } from 'antd';
import type { DescriptionsProps, TableProps } from 'antd';

import { DetailPanelWrapper } from './detail-panel.style';
import { getPointList } from '../../topology-content.service';

export function traverse(dictCode: string, nodes: any[]) {
  for (const node of nodes) {
    if (node.dictCode === dictCode) return node;
    if (node.children?.length) {
      const result: any = traverse(dictCode, node.children);
      if (result) return result;
    }
  }
  return null;
}

type RecordType = any;

interface IProps {
  children?: ReactNode;
  nodes: any[];
  edges: any[];
  visible: boolean;
  selectedBusinessId: string;
}

const DetailPanel: FC<IProps> = (props) => {
  const { visible, selectedBusinessId, nodes, edges } = props;

  const [baseInfoData, setBaseInfoData] = useState<any>(null);

  useEffect(() => {
    if (!visible || !selectedBusinessId) return;

    getPointList({
      pageNum: 1,
      pageSize: 10,
      pointCode: selectedBusinessId,
    }).then((res) => {
      setBaseInfoData({
        ...res.data?.list?.[0],
      });
    });
  }, [visible, selectedBusinessId]);

  const baseInfo: DescriptionsProps['items'] = [
    {
      key: '1',
      label: '节点类型',
      span: 3,
      children: '测点',
    },
    {
      key: '2',
      label: '测点名称',
      span: 3,
      children: baseInfoData?.pointName || '-',
    },
    {
      key: '3',
      label: '编码',
      span: 3,
      children: baseInfoData?.pointCode || '-',
    },
    {
      key: '4',
      label: '类型',
      span: 3,
      children: baseInfoData?.pointType || '-',
    },
    {
      key: '5',
      label: '设备关联状态',
      span: 3,
      children: (
        <Badge
          style={{ color: '#fff' }}
          status={baseInfoData?.switchingValue === '1' ? 'success' : 'error'}
          text={baseInfoData?.switchingValue === '1' ? '已关联' : '未关联'}
        />
      ),
    },
    {
      key: '6',
      label: '是否更新',
      span: 3,
      children: (
        <Badge
          style={{ color: '#fff' }}
          status={baseInfoData?.memo === '1' ? 'success' : 'error'}
          text={baseInfoData?.memo === '1' ? '已同步' : '未同步'}
        />
      ),
    },
  ].map<NonNullable<DescriptionsProps['items']>[number]>((item, index) => ({
    ...item,
    style: { color: '#fff' },
    labelStyle: { color: '#8fd4ff' },
  }));

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'deviceName',
      align: 'center',
      width: '45%',
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      key: 'deviceType',
      align: 'center',
      width: '30%',
      title: '设备类型',
      dataIndex: 'deviceType',
    },
    {
      key: 'distance',
      align: 'center',
      width: '30%',
      title: '是否关联',
      dataIndex: 'distance',
      render: () => '是', // 不显示距离
    },
  ];

  const dataSource = useMemo(() => {
    if (!selectedBusinessId) return [];

    const currentNode = nodes.find((node) => node.data.id === selectedBusinessId);
    const restNodes = nodes.filter((node) => node !== currentNode);

    return restNodes
      .map((node) => ({
        deviceName: node.data.text,
        deviceType: node.data.type,
        distance: edges.find(
          (edge: any) =>
            [edge.startId, edge.endId].includes(currentNode.id) &&
            [edge.startId, edge.endId].includes(node.id),
        )?.distance,
      }))
      .filter((item) => item.distance)
      .map((item) => ({ ...item, distance: item.distance + 'm' }));
  }, [nodes, edges, selectedBusinessId]);

  return (
    <DetailPanelWrapper $visible={visible}>
      <div className="title-bar">基本信息</div>
      <Descriptions items={baseInfo} bordered size="small" />
      <>
        <div className="title-bar">关联信息</div>
        <Table
          rowKey="id"
          size="small"
          columns={columns}
          dataSource={dataSource}
          pagination={false}
        />
      </>
    </DetailPanelWrapper>
  );
};

export default memo(DetailPanel);
