import React, { useEffect, useRef } from 'react';
import { Line } from '@antv/g2plot';
import dayjs from 'dayjs';
import styles from './g2.component.less';

// ___________________________________________________ 用于测试现场真实数据
// import testJson from './test.json';
// import { v4 as uuidv4 } from 'uuid';
// const historyData = testJson?.data
//   ?.map((point) => {
//     return (
//       point.historyDatas?.map((__) => ({
//         time: __.time,
//         value: __.value,
//         type: point.pointName,
//         pointCode: point.pointCode,
//         tableId: uuidv4(),
//       })) || []
//     );
//   })
//   .flat();
// console.log('historyData', historyData);
// ___________________________________________________

interface IData {
  time: string;
  value: number;
  type: string;
  [key: string]: any;
}
interface IProps {
  data: IData[];
  options?: any;
  chartType: string;
}
const defaultOptions = {
  xField: 'time', // 横坐标字段
  yField: 'value', // 纵坐标字段
  seriesField: 'type', // 系列字段
  appendPadding: [0, 0, 0, 0], // 间距
  autoFit: true, // 自动适应
  interactions: [
    { type: 'active-region' }, // 高亮显示数据点
  ],
  lineStyle: {
    lineWidth: 1, // 线宽
  },
  smooth: true, // 平滑
  meta: {
    date: {
      range: [0.02, 1], // 时间范围
    },
  },
  animation: {
    appear: {
      animation: 'path-in', // 动画
      duration: 3000, // 动画持续
    },
  },
  slider: {
    start: 0, // 起始位置
    end: 1, // 结束位置
    formatter: () => '', // 隐藏缩略轴上的文字
  },
  xAxis: {
    // type: 'time',
    // mask: 'YYYY-MM-DD HH:mm:ss', // 添加这行配置来指定时间格式
    nice: true, // 美观
    label: {
      // autoRotate: false, // 自动旋转
      // rotate: Math.PI / 6, // 旋转角度
      offset: 20, // 轴线和文字的距离
      style: {
        fill: '#D4EBFD', // 文字颜色
        fontSize: 12, // 文字大小
      },
      // formatter: (val) => {
      //   return dayjs(val).format('HH:mm:ss'); // 动态显示时间[3](@ref)
      // },
    },
    line: {
      style: {
        stroke: '#6CA1DD', // 轴线颜色
        opacity: 0.3, // 透明度
      },
    },
    tickLine: {
      style: {
        stroke: 'transparent', // 设置刻度线颜色（隐藏）
      },
    },
  },
  yAxis: {
    label: {
      // autoRotate: false, // 关闭自动旋转
      // formatter: (v) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`), // 格式化
      style: {
        fill: '#D4EBFD', // 文字颜色
        fontSize: 12, // 文字大小
        fontWeight: 'bold', // 文字粗细
      },
    },
    grid: {
      line: {
        style: {
          lineWidth: 1, // 设置虚线宽度
          stroke: 'rgba(108, 161, 221, 0.3)', // 虚线颜色
          lineDash: [2, 2], // 虚线的密集度
        },
      },
      // alternateColor: 'red' // 分割线之间的颜色
    },
  },
  legend: {
    itemName: {
      style: {
        fill: '#fff',
      },
      // formatter: (name) => name,
    },
    flipPage: true, // 分页
    maxRow: 1, // 两行分页
    pageNavigator: {
      marker: {
        style: {
          fill: '#8FD4FF', // 右边箭头颜色
          inactiveFill: '#8FD4FF', // 作伴箭头颜色
        },
      },
      text: {
        style: {
          fill: '#FFFFFF', // 设置文字颜色为白色
        },
      },
    },
    // 画圆
    marker: {
      symbol: (x: number, y: number, r: number) => {
        return [
          ['M', x - 9, y - 1], // 起始点
          ['L', x + 9, y - 1], // 右上角
          ['L', x + 9, y + 1], // 右下角
          ['L', x - 9, y + 1], // 左下角
          ['Z'], // 关闭路径
        ];
      },
      style: {
        fill: 'currentColor', // 使用当前颜色填充
      },
    },
  },
  tooltip: {
    enterable: true, // 是否可进入Tooltip 内部
    // follow: true, // 跟随鼠标
    // shared: true, // 控制是否共享显示多个图形的信息
    offset: 10, // 距离
    marker: { lineWidth: 1, r: 3 }, // 点的样式
    // showCrosshairs: true, // 启用十字准线
    crosshairs: {
      line: {
        style: {
          stroke: '#1E89F4', // 鼠标移入，竖线的颜色
        },
      },
    },
  },
};
const GTwoLine = ({ data, options, chartType }: IProps) => {
  const container = useRef(null);
  /**
   * @description 数据预处理
   * 由于测点的 time(X轴) 均不相同，需要进行一个数据排序，不然多个折线图会出现紊乱
   */
  const processedData = React.useMemo(() => {
    return Object.values(data)
      ?.map((item) => ({
        ...item,
        time: dayjs(item.time).valueOf(), // 转换为时间戳（毫秒）
      }))
      ?.sort((a, b) => a.time - b.time) // 按时间升序排序
      ?.map((item) => ({
        ...item,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss'),
      }));
  }, [data]);

  // 计算数据的最大值和最小值
  const { maxValue, minValue } = React.useMemo(() => {
    const values = data.map((item) => item.value);
    return {
      maxValue: Math.max(...values),
      minValue: Math.min(...values),
    };
  }, [data]);

  // 初始化图表
  useEffect(() => {
    if (!container.current) return;

    // 准备注释配置
    const annotations = [];
    if (maxValue !== undefined && minValue !== undefined) {
      annotations.push(
        {
          type: 'line',
          start: ['min', maxValue],
          end: ['max', maxValue],
          style: {
            stroke: '#ff4d4f',
            lineWidth: 2,
            lineDash: [4, 4],
          },
        },
        {
          type: 'text',
          position: ['max', maxValue],
          content: `最大值: ${maxValue}`,
          style: {
            fill: '#ff4d4f',
            fontSize: 12,
            fontWeight: 'bold',
            textAlign: 'right',
          },
          offsetX: -10,
          offsetY: -10,
        },
        {
          type: 'line',
          start: ['min', minValue],
          end: ['max', minValue],
          style: {
            stroke: '#52c41a',
            lineWidth: 2,
            lineDash: [4, 4],
          },
        },
        {
          type: 'text',
          position: ['max', minValue],
          content: `最小值: ${minValue}`,
          style: {
            fill: '#52c41a',
            fontSize: 12,
            fontWeight: 'bold',
            textAlign: 'right',
          },
          offsetX: -10,
          offsetY: 10,
        },
      );
    }

    const config = {
      ...defaultOptions,
      ...options,
      data: processedData,
      yAxis: {
        ...defaultOptions.yAxis,
        minLimit: 0,
        maxLimit: Number((maxValue * 1.1).toFixed(2)),
      },
      // 折线图的平滑度，遥信使用梯形折线图
      stepType: chartType === 'state' ? 'vh' : 'smooth',
      annotations,
    };

    const chart = new Line(container.current, config);
    chart.render();

    // 清理函数
    return () => {
      if (chart) chart.destroy();
    };
  }, [processedData, maxValue, minValue]);

  return <div ref={container} className={styles['chart-container']}></div>;
};

export default GTwoLine;
