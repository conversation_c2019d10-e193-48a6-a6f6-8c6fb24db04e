# NWH数字工程研发中心WEB前端编码风格指南

### version 1.0

## 一、文件命名指南

### 总体命名规则

**坚持**所有符号使用一致的命名规则。

**坚持**所有文件名均使用**小写**命名。

**坚持**遵循同一个模式来描述符号的特性和类型，推荐的模式为`feature.type.filetype`。

>**为什么？**
>
> 1、命名约定提供了一致的方式来查找内容，让我们一眼就能锁定。 项目的一致性是至关重要的。团队内的一致性也很重要。
> 2、命名约定帮助我们更快得找到不在手头的代码，更容易理解它。
> 3、目录名和文件名应该清楚的传递它们的意图。 例如，`app/heroes/hero-list.component.ts`包含了一个用来管理英雄列表的组件。
> 4、npm均为**小写**命名，并使用短横线分割单词，更加的统一和美观且符合现在主流命名习惯。
> 5、windows文件系统大小写不敏感，而linux文件系统则大小写敏感，为避免生产环境编译发布出现不可预料问题，文件命名使用小写更合适。

### 使用点和横杠来分隔文件名

**坚持**遵循先描述文件特性，再描述它的类型的模式，对所有组件使用一致的类型命名规则。推荐的模式为`feature.type.filetype`。

**坚持** 在描述性名字中，用横杠来分隔单词，即：烤串 (kebab) 命名法。

> 例如：`user-custom.layout.tsx`，表示一个自定义用户的react布局组件；`user-list.component.vue`，表示一个用户列表的vue组件，`drag-modal.component.tsx`是一个拖拽弹出框的react组件、`progress-bar.component.vue`是一个Vue进度条组件

**坚持**使用点来分隔描述性名字和类型，类型名字不要用简写。

**坚持**使用惯用的后缀来描述类型，包括`*.service`、`*.component`、`*.module`、`*.page`、`*.layout`、`*.spec`。 必要时可以创建更多类型名，但必须注意，不要创建太多。

> **比如：**
> 
>  - service 请求服务
>  - component 组件
>  - page 页面
>  - module 模块
>  - layout 布局
>  - spec 测试

>**为什么？**
>
> 1、类型名字提供一致的方式来快速的识别文件中有什么。
> 2、利用编辑器或者 IDE 的模糊搜索功能，可以很容易地找到特定文件。
> 3、像`.service`这样的没有简写过的类型名字，描述清楚，毫不含糊。 像`.srv`, `.svc`, 和 `.serv`这样的简写可能令人困惑。
> 4、为自动化任务提供模式匹配。

### 文件结构指导原则

**坚持**把所有源代码都放到名为`src`的目录里。

**坚持**如果组件/页面具有多个伴隨文件 (`.tsx`、`.vue`、`.less`和`.spec`)，就为它创建一个文件夹。

>**为什么？**
>
> 1、在早期阶段能够帮助保持应用的结构小巧且易于维护，这样当应用增长时就容易进化了。
> 2、组件通常有三个文件 (`*.tsx`、 `*.less`、 `*.vue` 和 `*.spec.ts`)，它们很容易把一个目录弄乱。

## 二、编程约定

### 总体原则

### 单一职责

**坚持**每个文件只定义一样东西（例如服务或组件）。

**考虑**把文件大小限制在 400 行代码以内。

>**为什么？**
>
> 1、单组件文件非常容易阅读、维护，并能防止在版本控制系统里与团队冲突。
> 2、单组件文件可以防止一些隐蔽的程序缺陷，当把多个组件合写在同一个文件中时，可能造成共享变量、创建意外的闭包，或者与依赖之间产生意外耦合等情况。
> 3、单独的组件通常是该文件默认的导出，可以用路由器实现按需加载。
> 4、最关键的是，可以增强代码可重用性和阅读性，减少出错的可能性。

### 简单函数

**坚持**定义简单函数

**考虑**限制在 75 行之内。

>**为什么？**
>
> 1、简单函数更易于测试，特别是当它们只做一件事，只为一个目的服务时。
> 2、简单函数促进代码重用。
> 3、简单函数更易于阅读。
> 4、简单函数更易于维护。
> 5、简单函数可避免易在大函数中产生的隐蔽性错误，例如与外界共享变量、创建意外的闭包或与依赖之间产生意外耦合等

### 2.1 类

**坚持**使用大写驼峰命名法来命名类。

>  例如：`class UserComponent`优于`class userComponent`

>**为什么？**
>
> 1、遵循类命名传统约定，js构造函数就使用大驼峰命名法。
> 2、类可以被实例化和构造实例。根据约定，用大写驼峰命名法来标识可构造的东西。

### 2.2 变量

**坚持**用`const`声明变量，除非它们的值在应用的生命周期内会发生变化。

**考虑**少用`let`声明变量。

**坚持**小驼峰变量名 (`heroRoutes`) 比传统的大写蛇形命名法 (`HERO_ROUTES`) 更容易阅读和理解。

>**为什么？**
>
> 1、告诉读者这个值是不可变的。
> 2、TypeScript 会要求在声明时立即初始化，并阻止再次赋值，以确保达成我们的意图。
> 3、把常量命名为大写蛇形命名法的传统源于现代 IDE 出现之前， 以便阅读时可以快速发现那些`const`定义。 TypeScript 本身就能够防止意外赋值。
> 4、容许现存的`const`常量沿用大写蛇形命名法。传统的大写蛇形命名法仍然很流行、很普遍，特别是在第三方模块中。 修改它们没多大价值，还会有破坏现有代码和文档的风险。

### 2.3 接口

**坚持**使用大写驼峰命名法来命名接口。

**考虑**不要在接口名字前面加`I`前缀。

**考虑**用类代替接口。

>**为什么？**
>
> 1、[TypeScript 指导原则](https://github.com/Microsoft/TypeScript/wiki/Coding-guidelines)不建议使用 “I” 前缀。
> 2、类可以作为接口使用（只是用`implements`代替`extends`而已）。

### 2.4 属性和方法

**坚持**使用小写驼峰命名法来命名属性和方法。

> 例如：handleOnClick

**避免**为私有属性和方法添加下划线前缀。

**考虑**方法命名使用约定好的动词前缀，例如：`get`、`set`、 `add`、`remove`, `create`、`destroy`、`start`、`stop`, `insert`、`delete`, `update`、`modify`等等。

>**为什么？**
>
> 1、遵循传统属性和方法的命名约定。
> 2、JavaScript 不支持真正的私有属性和方法。TypeScript 工具让识别私有或公有属性和方法变得很简单。

### 2.5 导入语句中的空行

**坚持**在第三方导入和应用导入之间留一个空行。

**考虑**按模块名字的字母顺排列导入行。

**考虑**在解构表达式中按字母顺序排列导入的东西。

>**为什么？**
>
> 1、空行可以让阅读和定位本地导入更加容易。
> 2、按字母顺序排列可以让阅读和定位本地导入更加容易。

### 2.6 避免为导入和导出属性、方法、类等指定别名

**避免**除非有重要目的，否则不要为输入和输出指定别名

> 例如：`import { zip as myzip } from 'lodash`，或导出时使用`export default class
BmpLayoutComponent`导入时`import BmpLayout from '@/layout/bmp.layout'`进行了重命名。

>**为什么？**
>
> 同一个属性、类、方法有两个名字（一个对内一个对外）很容易导致混淆。

### 2.7 成员顺序

**坚持**把属性成员放在前面，方法成员放在后面。

**坚持**先放公共成员，再放私有成员，并按照字母顺序排列。

>**为什么？**
>
> 把类的成员按照统一的顺序排列，易于阅读，能立即识别出组件的哪个成员服务于何种目的。

### 2.8 实现生命周期钩子接口

**坚持**实现生命周期钩子接口，不要写了生命周期钩子函数在组件里，却不实现。

### 2.9 样式

**避免**使用通用选择器 `*`。

**避免**使用元素 `id` 选择器。

**避免**使用无具体语义的名字定义的标签选择器。

**坚持**样式书写统一使用展开格式。

```css

.jdc {

display: block;

width: 50px;

}

```

**坚持**样式书写顺序按照：`布局方式、位置` > `尺寸` > `文本相关` > `视觉效果`

  
| 布局方式、位置 | `display`/`position`/`float`/`top`/`right`/`bottom`/`left`/`overflow`/`clear`/`z-index |

| -------------- | ------------------------------------------------------------ |

| 尺寸 | `width`/`height`/`margin`/`padding`/`border` |

| 文本相关 | `font（font-family、font-size、font-style、font-weight、font-varient）`/`line-height`/`text-align`/`vertical-align`/`text-wrap`/`text-transform`/`text-indent`/`text-decoration`/`letter-spacing`/`word-spacing`/`white-space`/`text-overflow`/`word-wrap` |

| 视觉效果 | `background`/`color`/`transition`/`list-style`/`cursor`/`border-radius`/`box-shadow`/`text-shadow`/`transform` |

```css
.xxx {
position: absolute; // 布局

width: 100px; // 尺寸

height: 100px;

font-size: 12px; // 文本

line-height: 100px;

text-align: center;

background-color: #f00; // 视觉效果

color: #fff;

}
```

### 2.10 注释

**坚持**为以下情况添加注释：

- 公共组件使用说明

- 各组件中重要函数或者类说明

- 复杂的业务逻辑处理说明

- 特殊情况的代码处理说明,对于代码中特殊用途的变量、存在临界值、函数中使用的hack、使用了某种算法或思路等需要进行注释描述

- 单行注释使用`//`

- 多重 if 判断语

**坚持**添加多行注释，推荐格式为：

```
/**

* 说明

* @关键字

*/
```
**常用注释关键字：**(只列出一部分，并不是全部)

  

| 注释名 | 语法 | 含义 | 示例 |

| -------- | ----------------------------------------- | -------------------- | -------------------------------------------- |

| @param | @param 参数名 {参数类型} 描述信息 | 描述参数的信息 | @param name {String} 传入名称 |

| @return | @return {返回类型} 描述信息 | 描述返回值的信息 | @return {Boolean} true:可执行;false:不可执行 |

| <AUTHOR> <AUTHOR> [附属信息：如邮箱、日期] | 描述此函数作者的信息 | <AUTHOR> 2015/07/21 |

| @version | @version XX.XX.XX | 描述此函数的版本号 | @version 1.0.3 |

| @example | @example 示例代码 | 演示函数的使用 | @example setTitle('测试') |

参考文档： [#JSDoc](https://jsdoc.app/about-getting-started.html)

## 三、完善的编辑器配置

**坚持**在项目中，团队使用统一的`.vscode`配置文件。

**坚持**在项目依赖中安装`eslint`、`tslint`、`stylelint`、`prettier`等代码检查和格式化工具/依赖

## 四、Commit Log 指南

### Subject 标题
标题不超过50个字符，结尾不需要标点符号；应该使用关键字 + 祈使句来描述，比如：fix: data display error。

关键字如下：

| 关键字   | 描述                  |
|----------|---------------------|
| feat     | 增加新功能            |
| fix      | 修复错误              |
| docs     | 修改文档              |
| style    | 修改样式              |
| refactor | 代码重构              |
| test     | 增加测试模块          |
| chore    | 更新核心模块、配置文件 |
| update   | 更新框架或依赖        |

### Content 内容
如果修改较小内容可不填

## package.json语义化版本规则

1.0 或 1.0.x 或 ~1.0.4：只接受补丁级版本更新

1 或 1.x 或 ^1.0.4：只接受小版本号和补丁版本更新

* 或 x：接受所有类型更新

## 五、yarn常用命令汇总

### yarn intall
yarn install用于安装一个项目的所有依赖。 这个命令最常见的使用场景是在你刚Check out一份项目代码之后，或者在你需要使用其他开发者新增加的项目依赖的时候。
执行不带任何命令的yarn，等同于执行yarn install，并透传所有参数。

### yarn add
一个包是一个包含代码的文件夹和一个描述包内容的package.json文件。 如果你想使用其他包，首先要将其加入依赖列表。 也就是执行yarn add package-name命令，来为项目安装所需的包。如果习惯使用 npm， 你可能希望使用 --save 或 --save-dev， 这些已经被 yarn add 和 yarn add --dev 所取代。 更多信息，请参阅 [yarn add](https://yarnpkg.com/zh-Hans/docs/cli/add) 文档。

你可以用以下方法指定版本号：
+ yarn add package-name 会安装 latest 最新版本。
+ yarn add package-name@1.2.3 会从 registry 里安装这个包的指定版本。
+ yarn add package-name@tag 会安装某个 “tag” 标识的版本（比如 beta、next 或者 latest）。

### yarn outdated
列出当前项目包的所有依赖项的版本信息，包括当前已安装的版本、最符合语义版本定义（semver）的版本和最新的可用版本。

### yarn upgrade
upgrade命令会根据在 package.json 文件中所指定的版本范围将依赖更新到其最新版本，也会重新生成yarn.lock 文件。

```
yarn upgrade-interactive [--latest]
```
upgrade-interactive 与 upgrade 命令采用相同的参数和功能。 在执行升级操作之前，此命令将显示已过期的包列表，并允许用户选择相应的想要升级的包。
>--latest : 此标志告知 yarn 忽略 package.json 中指定的版本范围，改用资源库中标为 latest 的版本，谨慎使用。

### yarn check
验证当前项目 package.json 里的依赖版本和 yarn 的 lock 文件是否匹配。

```
yarn check [--integrity]
```
验证当前项目 package.json 里包内容的版本和 hash 值是否与 yarn 的 lock 文件一致。 这有助于验证包依赖没有更改。

### yarn cache

```
yarn cache list [--pattern]
```
Yarn 将每个包存储在你的文件系统-用户目录-全局缓存中。yarn cache list 将列出已缓存的每个包。
yarn cache list --pattern <pattern> 将列出匹配指定模式的已缓存的包。

```
yarn cache dir
```
运行 yarn cache dir 会打印出当前的 yarn 全局缓存在哪里。

```
yarn cache clean [<module_name...>]
```
运行此命令将清除全局缓存。 将在下次运行 yarn 或 yarn install 时重新填充。 此外，您可以指定一个或多个想要清除的包。

## 六、npm常用命令汇总

### 一、查询

查看所有全局安装包
```
npm list -g --depth 0
```

查看本地所有依赖包版本信息
```
npm outdate
```

### 安装/卸载

全局安装依赖包
```
npm install -g <package-name>
```

本地安装生产环境依赖包
```
npm install --save <package-name>[@latest]/[@next]
```

本地安装开发环境依赖包
```
npm install --save-dev <package-name>[@latest]/[@next]
```

卸载全局包
```
npm uninstall -g <package-name> 
```

卸载本地包
```
npm uninstall <package-name>
```
### 更新

更新所有本地依赖包
```
npm update
```

更新所有全局依赖包
```
npm update -g
```

更新全局安装依赖包
```
npm update -g <package-name>
```

清除npm缓存
```
npm cache clean --force
```

验证npm缓存
```
npm cache verify
```