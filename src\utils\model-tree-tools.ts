import { v4 as uuidv4 } from 'uuid';
import { deepForEachFn, deepMapTreeFn } from './data-processing-tools';
// 处理项目树，遍历并筛选出挂了模型的节点
export function pruneProjectTree(nodes: any[]) {
  // modelUpload是否挂载模型，loadModel是否自动加载
  return nodes.reduce((acc: any[], node: { modelUpload: boolean; loadModel: boolean; children: any }) => {
    // 如果当前节点的flag为true，则保留该节点及其所有上级节点
    if (node.modelUpload && node.loadModel) {
      acc.push({
        ...node,
        children: pruneProjectTree(node.children || []),
      });
    } else {
      // 否则，检查其子节点中是否有满足条件的节点
      const filteredChildren = pruneProjectTree(node.children || []);
      if (filteredChildren.length > 0) {
        acc.push({
          ...node,
          children: filteredChildren,
        });
      }
    }
    return acc;
  }, []);
}
export function pruneProjectTree2(nodes: any[]) {
  // modelUpload是否挂载模型
  return nodes.reduce((acc: any[], node: { modelUpload: boolean; children: any }) => {
    // 如果当前节点的flag为true，则保留该节点及其所有上级节点
    if (node.modelUpload) {
      acc.push({
        ...node,
        key: node.children,
        children: pruneProjectTree2(node.children || []),
      });
    } else {
      // 否则，检查其子节点中是否有满足条件的节点
      const filteredChildren = pruneProjectTree2(node.children || []);
      if (filteredChildren.length > 0) {
        acc.push({
          ...node,
          key: node.children,
          children: filteredChildren,
        });
      }
    }
    return acc;
  }, []);
}

// 为项目树叶子节点添加bimModelId属性并瘦身，projectTree是项目树，modelObjs是用项目id查询的得到的模型基本信息
export const addModelId = (projectTree: any[], modelObjs: any[]) => {
  return deepMapTreeFn(projectTree, (node) => {
    let obj = {};
    let flag = false;
    modelObjs?.forEach((item) => {
      if (item.nodeId === node.id) {
        obj = {
          code: node.code,
          name: node.name,
          id: node.id,
          parentId: node.parentId,
          loadModel: node.loadModel,
          children: node.children,
          modelId: item.bimModelId,
        };
        flag = true;
      }
    });
    if (flag) {
      return obj;
    } else
      return {
        code: node.code,
        name: node.name,
        id: node.id,
        parentId: node.parentId,
        loadModel: node.loadModel,
        children: node.children,
      };
  });
};

// 处理构件树（瘦身），添加uuid并删去多余的属性
export const dfsRefactorModelTree = (treeNodeObjects: any[]) => {
  return treeNodeObjects.map((item) => {
    const currentId: any = uuidv4();
    const isChildren = item.childNodes instanceof Array && item.childNodes.length;
    const treeNode: any = {
      ...item,
      id: currentId,
      key: currentId,
      title: item.name,
      modelId: item.modelId,
      isLeaf: !isChildren,
      obj: null,
      parent: null,
      viewer: null,
      children: isChildren ? dfsRefactorModelTree(item.childNodes) : [],
    };
    delete treeNode._segmentObject;
    delete treeNode.obj;
    delete treeNode.parent;
    delete treeNode.viewer;
    delete treeNode.childNodes;
    return { ...treeNode };
  });
};

// 获取模型树某些节点下绑了模型的叶子节点
export const getLeafNodesById = (modelShip: string[], tree: any, target?: string) => {
  const treeArr: any[] = []; //根据构件节点id获取对应的结构子树数组
  modelShip.forEach((componentId: any) => {
    deepForEachFn(
      [tree],
      (node) => {
        if (node.componentId === componentId) {
          treeArr.push(node.segmentObject.treeNodeObject);
        }
      },
      'childNodes',
    );
  });
  const leafNodes: string[] = []; //根据结构子树获取绑定了模型的叶子节点或其属性
  treeArr.forEach((tree) => {
    deepForEachFn(
      [tree],
      (node) => {
        if (node.isComponent && node.componentId) {
          if (target) {
            leafNodes.push(node[target]);
          } else {
            leafNodes.push(node);
          }
        }
      },
      'childNodes',
    );
  });
  return leafNodes;
};

// 根据勾选的节点过滤树
export function filterTree(tree: any[], checkedKeys: any[]) {
  // 定义一个内部函数来递归地过滤节点
  function filterNode(node: { id: any; children: any[] }) {
    // 如果节点被勾选或者它的任意子节点需要被保留，则应该保留这个节点
    const nodeChecked = checkedKeys.includes(node.id);
    const children: any[] = node.children ? node.children.map(filterNode).filter((child: any) => child !== null) : [];
    const shouldKeep = nodeChecked || children.length > 0;

    // 如果当前节点需要被保留，则创建一个新的节点对象，包括过滤后的子节点
    if (shouldKeep) {
      return { ...node, children };
    } else {
      return null;
    }
  }

  // 对树的每个根节点应用过滤函数，并移除那些返回null的节点
  return tree.map((item) => filterNode(item)).filter((node: any) => node !== null);
}

// 根据优先级处理 {材质，模型ids}[] 的对象数组
export function processMaterialData(objects: any[], priorityMap: { [x: string]: number }, arrName: string, levelName: string) {
  // 创建一个结果数组，用于存放处理后的对象
  const result = [...objects];
  // 创建一个集合用于存储最高优先级出现过的值
  const highestPriority = new Set();
  // 按优先级从高到低遍历对象数组
  Object.keys(priorityMap)
    .sort((a, b) => priorityMap[a] - priorityMap[b])
    .forEach((level) => {
      // 遍历当前类型的所有元素
      result.forEach((item) => {
        if (item[levelName] === level) {
          // 对于每个 id，如果它没有出现在最高优先级集合中，则添加
          item[arrName] = item[arrName].filter((id: string) => !highestPriority.has(id));
          // 将当前对象的 id加入最高优先级集合
          item[arrName].forEach((id: string) => highestPriority.add(id));
        }
      });
    });
  return result;
}
