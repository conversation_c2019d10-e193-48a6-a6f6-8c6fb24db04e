import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import type { ReactNode, FC, ElementRef } from 'react';
import { useDispatch } from 'umi';

import { QueryFormWrapper } from './query-form.style';
import BaseForm from '@/components/base-form/base-form.component';
import { EElementType } from '@/components/base-form/base-form.types';
import type { IBaseFormConfig } from '@/components/base-form/base-form.types';
import type { IRootState, IState } from '../../models/EquipRelationGraph';

interface IProps {
  children?: ReactNode;
}

const QueryForm: FC<IProps> = (props) => {
  const dispatch = useDispatch();

  const baseFormRef = useRef<ElementRef<typeof BaseForm>>(null);

  const handleQuery = useCallback(() => {
    const queryInfo = baseFormRef.current?.baseFormInstance.getFieldsValue();
    const [startTime, endTime] = queryInfo.dates ?? [];
    const newQueryInfo = { ...queryInfo, startTime, endTime };
    delete newQueryInfo.dates;
    dispatch({ type: 'EquipRelationGraph/changeQueryInfo', payload: newQueryInfo });
  }, []);

  const handleReset = useCallback(() => {
    baseFormRef.current?.baseFormInstance.resetFields();
    dispatch({ type: 'EquipRelationGraph/changeQueryInfo', payload: {} });
  }, []);

  const baseFormConfig = useMemo<IBaseFormConfig>(
    () => ({
      layout: 'inline',
      elements: [
        {
          key: 'input_monitor_type',
          type: EElementType.INPUT,
          label: '监测仪器名称',
          field: 'monitorType',
          formItemProps: { style: { marginRight: '2%', width: '25%' } },
        },
        { key: 'btn_query', type: EElementType.BUTTON, label: '查询', onClick: handleQuery },
        { key: 'btn_reset', type: EElementType.BUTTON, label: '重置', onClick: handleReset },
      ],
    }),
    [handleQuery, handleReset],
  );

  return (
    <QueryFormWrapper>
      <BaseForm ref={baseFormRef} config={baseFormConfig} />
    </QueryFormWrapper>
  );
};

export default memo(QueryForm);
