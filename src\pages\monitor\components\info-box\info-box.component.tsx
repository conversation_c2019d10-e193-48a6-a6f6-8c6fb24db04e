import EmptyComponent from '@/components/empty-component/empty-component';
import { Descriptions, Empty } from 'antd';
import styles from './info-box.component.less';
import classnames from 'classnames';
interface Props {
  data: { key: string; label: string; value: string }[];
  sliceNum?: number;
}
const InfoBox: React.FC<Props> = ({ data, sliceNum = 2 }) => {
  return (
    <>
      {data?.length > 0 ? (
        <div className={styles.outer}>
          <div className={styles.inner}>
            <Descriptions column={1} className={styles.descriptionsPanel}>
              {data?.slice(0, sliceNum)?.map((ele: any) => {
                return (
                  <Descriptions.Item key={ele.key} label={ele.label}>
                    {ele.value}
                  </Descriptions.Item>
                );
              })}
            </Descriptions>
          </div>
          {data?.slice(sliceNum)?.length > 0 ? (
            <div className={styles.inner}>
              <Descriptions column={1} className={classnames(styles.descriptionsPanel, styles.descriptionsPanel2)}>
                {data?.slice(sliceNum).map((ele: any) => {
                  return (
                    <Descriptions.Item key={ele.key} label={ele.label}>
                      {ele.value}
                    </Descriptions.Item>
                  );
                })}
              </Descriptions>
            </div>
          ) : (
            ''
          )}
        </div>
      ) : (
        <EmptyComponent />
      )}
    </>
  );
};

export default InfoBox;
