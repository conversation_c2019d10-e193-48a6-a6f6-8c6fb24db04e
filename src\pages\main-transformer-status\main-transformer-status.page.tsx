import { useEffect, useRef, useState } from 'react';
import { DataIndicators } from './components/data-indicators/data-indicators.component';
import { DataModal } from './components/data-modal/data-modal.component';
import { UnitTab } from './components/unit-tab/unit-tab.component';
import { WarningInfo } from './components/warning-info/warning-info.component';
import styles from './main-transformer-status.page.less';
import { UnitOptions } from './components/unit-tab/unit-tab';
import { CenterBim } from './components/center-bim/center-bim.component';
import { socketConnect } from '@/utils/socket';
import { url } from '../equip-status-bim/equip-status-bim.page';
import { Client } from '@stomp/stompjs';
import { SocketOptions, pointSourceHead, subTheme, unsubTheme } from './main-transformer-status';
import { useSyncCallback } from '@/hooks/useGetState';
import { isJsonString } from '@/utils/utils';
import { PointOptions } from './components/unit-tab/unit-tab';
import { PointOptions as BimPoint } from '../equip-status-bim/equip-status-bim';
import { LeftEquipModal as HistoryDataModal } from '../equip-status-bim/components/left-equip-modal/left-equip-modal.component';
import { pointDataList } from '@/services/equip-status-bim';
import { rsaEncrypt } from '@/utils/jsencrypt.util';

export let otherSocketClient: Client | undefined;
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
let newData: UnitOptions | undefined = undefined;
const MainTransformerStatusPage = () => {
  //当前机组key
  const [tabKey, setTabKey] = useState<string>('');
  //当前机组数据
  const [transformerData, setTransformerData] = useState<UnitOptions>();
  //socket实例
  const [socketClient, setSocketClient] = useState<Client>();
  //socket初始化
  const [socketInit, setSokcetInit] = useState<boolean>(false);
  const bimRef = useRef<any>(null);
  const subMsgSync = useSyncCallback(subMsg);
  //当前点击测点数据
  const [pointData, setPointData] = useState<BimPoint[]>([]);
  //弹框控制
  const [modalController, setModalController] = useState<boolean>(false);
  //点击标题
  const [title, setTitle] = useState<string>('');
  //单位
  const [unit, setUnit] = useState<string>('');
  useEffect(() => {
    socketConnectHandler();
    return () => {
      //关闭socket连接
      otherSocketClient && otherSocketClient.deactivate();
      newData = undefined;
    };
  }, []);
  //socket连接处理
  function socketConnectHandler(): void {
    // 建立 WebSocket 连接并添加事件处理器
    socketConnect(url)
      .then((client) => {
        otherSocketClient = client;
        setSocketClient(client);
        subMsgSync();
      })
      .catch((err) => {
        console.log('err', err);
      });
  }
  //发送订阅
  function subMsg(): void {
    const ids = getPointIds();
    if (ids.length > 0) {
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids.join(','),
          userId,
          topicType: pointSourceHead,
        }),
        destination: subTheme,
        headers: {
          Authorization: rsaEncrypt(subTheme).toString(),
        },
      };
      // 接受返回消息
      socketInit === false && messageReturn(pointSourceHead + userId);
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
  }
  //取消订阅
  function unsubMsg(): void {
    const ids = getPointIds();
    if (ids.length > 0) {
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids.join(','),
          userId,
          topicType: pointSourceHead,
        }),
        destination: unsubTheme,
        headers: {
          Authorization: rsaEncrypt(unsubTheme).toString(),
        },
      };
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
  }
  //消息返回
  function messageReturn(soucre: string): void {
    if (socketClient) {
      socketClient.subscribe(soucre, function (message: any) {
        const recv = message.body;
        setSokcetInit(true);
        if (isJsonString(recv)) {
          socketDataHandler(JSON.parse(recv));
        }
      });
    }
  }
  //socket返回数据处理
  function socketDataHandler(data: SocketOptions): void {
    if (transformerData && newData) {
      const fields = Object.keys(transformerData.pointList);
      const listData = newData;
      fields.some((item) => {
        return listData.pointList[item].some((citem: PointOptions) => {
          if (citem.value === data.key) {
            citem.price = data.value;
            return true;
          }
        });
      });
      listData.indicators.some((item: PointOptions) => {
        if (item.value === data.key) {
          item.price = data.value;
          return true;
        }
      });
      setTransformerData(JSON.parse(JSON.stringify(listData)));
    }
  }
  //每次机组初始化时获取最新的历史数据,防止socket获取不到最新数据
  function getHistoryPointData(data: UnitOptions): void {
    const keyList = getPointIds(data);

    pointDataList({ keyList }).then((res) => {
      if (res.code === '1' && res.data && newData) {
        const fields = Object.keys(data.pointList);
        const newDatas = newData;
        fields.forEach((item) => {
          newDatas.pointList[item].some((citem: PointOptions) => {
            citem.price = res.data[Number(citem.value)];
          });
        });
        newDatas.indicators.forEach((item: PointOptions) => {
          item.price = res.data[Number(item.value)];
        });
        setTransformerData(JSON.parse(JSON.stringify(newDatas)));
      } else {
        setTransformerData(JSON.parse(JSON.stringify(data)));
      }
      subMsgSync();
    });
  }
  //获取测点id
  function getPointIds(data?: UnitOptions): string[] {
    const curData = data ? data : transformerData;
    if (curData) {
      const ids: string[] = [];
      const fields = Object.keys(curData.pointList);
      fields.forEach((item) => {
        curData.pointList[item].forEach((citem) => {
          ids.push(citem.value);
        });
      });
      curData.indicators.forEach((item) => {
        if (item.description) {
          ids.push(item.value);
        }
      });
      return ids;
    } else {
      return [];
    }
  }
  //变压器tab切换
  function tabChange(data: UnitOptions): void {
    if (data.value !== transformerData?.value) {
      newData = JSON.parse(JSON.stringify(data));
      setTabKey(data.value);
      unsubMsg();
      getHistoryPointData(data);
      bimDefViewSet(data);
    }
  }
  //bim默认视图设置
  function bimDefViewSet(curData?: UnitOptions): void {
    bimRef.current.setBimDefaultView(curData ? curData : transformerData);
    bimRef.current.setLineController(true);
  }
  //关闭弹框
  function closeModal(): void {
    setModalController(false);
  }
  //打开统计弹框
  function openModal(data: BimPoint[], title: string = '', unit: string): void {
    setPointData(data);
    setTitle(title);
    setUnit(unit);
    setModalController(true);
  }
  return (
    <div className={styles['mina-trans-page']}>
      {/* BIM模型展示 */}
      <CenterBim openModal={openModal} ref={bimRef} pointData={transformerData}></CenterBim>
      {/* 变压器局放监测按钮弹框 */}
      <DataModal
        socketClient={socketClient}
        pointData={transformerData}
        bimDefViewSet={bimDefViewSet}
      ></DataModal>
      {/* 机组切换tab */}
      <UnitTab onChange={tabChange} tabKey={tabKey}></UnitTab>
      {/* 警告等级信息 */}
      <WarningInfo></WarningInfo>
      {/* 信息指标 */}
      <DataIndicators pointData={transformerData}></DataIndicators>
      {/* 历史记录弹框信息 */}
      <HistoryDataModal
        socketClient={socketClient}
        unit={unit}
        title={title}
        pointData={pointData}
        close={closeModal}
        open={modalController}
      />
    </div>
  );
};

export default MainTransformerStatusPage;
