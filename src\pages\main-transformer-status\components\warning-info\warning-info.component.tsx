import { memo } from 'react';
import styles from './warning-info.component.less';
import { WAR_LIST } from './warning-info';
import { WarningOutlined } from '@ant-design/icons';

export const WarningInfo = memo(() => {
  return (
    <section className={styles['warning-info']}>
      <div className={styles['warning-info-tab']}>
        <p>
          <WarningOutlined />
          <span>警报</span>
        </p>
        <p>
          {WAR_LIST.map((item) => (
            <span
              className={styles['warning-info-tab-item']}
              data-key={item.value}
              key={item.value}
              style={{ backgroundColor: item.color }}
            >
              {item.label}
            </span>
          ))}
        </p>
      </div>
    </section>
  );
});
