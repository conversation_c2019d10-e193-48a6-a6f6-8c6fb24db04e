import { request, jsonHeader } from '@/utils/request';
import { Pointlist, PointAnalysis, PointUnitDictionary } from './type';

// 字典 - 获取测点单位值
export function findDictTree(dictCode: string): Promise<Global.Response<PointUnitDictionary[]>> {
  return request.get('/Dict/findDictTree', {
    params: { dictCode },
  });
}
// 获取设备类型
export const getPonitsType = (): Promise<Global.Response<PointUnitDictionary[]>> => {
  return request.get(`/Dict/findDictTree?dictCode=point_type`);
};

// 测点菜单 - 测点列表页 - 获取测点列表
export async function getPointList(
  params: Partial<Pointlist.Query> = {},
): Promise<Global.Response<Pointlist.Common>> {
  return request.post(`/BasePoint/listPage`, { data: params });
}

// 获取遥测历史数据
export interface IGetHistoryPointList {
  keyList: string[];
  selectBegin: string;
  selectEnd: string;
}
export async function getHistoryPointList(
  params: IGetHistoryPointList,
): Promise<Global.Response<Record<string, { time: string; value: number }>>> {
  return request.post(`/nr/data/getHistoryGroupByKey`, {
    data: JSON.stringify(params),
    headers: jsonHeader,
  });
}

// 获取遥信历史数据
export interface IGetOriginPointList {
  key: string;
  selectBegin: string;
  selectEnd: string;
}
export async function getOriginPointList(
  params: IGetOriginPointList,
): Promise<Global.Response<Record<string, { time: string; value: number }>>> {
  return request.get(`/nr/data/getOrigin`, { params });
}

// 测点菜单 - 测点展示 - 获取测点差异化列表
export async function getPointDiff(params: Record<string, any>): Promise<Global.Response> {
  return request.get(`/BasePointDiff/listPage`, { params });
}

// 测点菜单 - 测点列表页 - 手动更新
export async function updatePointInfo(): Promise<Global.Response> {
  return request.get(`/BasePoint/sync-point`);
}

//  测点菜单 - 测点列表页 - 导出
export const exportPointList = (params: Record<string, any>): Promise<Global.Response<Blob>> => {
  return request.get('/BasePoint/export', {
    responseType: 'blob',
    params,
  });
};

// 测点菜单 - 测点列表页 - 点击是否更新
export const isUpdatePoint = (id: string): Promise<Global.Response<string>> => {
  return request.post(`/BasePointDataVerify/verify-code`, { data: { pointCode: id } });
};

// 获取测点列表详情
export const getPointDetails = (id: string): Promise<Global.Response<string>> => {
  return request.get(`/BasePoint/${id}`);
};

// 设置单位
interface IUpdatePointDetails {
  id: string;
  unitData: string;
}
// 未关联设备 - 新增
export const addPointListUnit = (
  data: IUpdatePointDetails,
): Promise<Global.Response<Global.Code>> => {
  return request.post(`/BasePointRelationInfo/`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
};
// 关联设备 - 修改
export const setPointListUnit = (
  data: IUpdatePointDetails,
): Promise<Global.Response<Global.Code>> => {
  return request.post(`/BasePointRelationInfo/${data.id}`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
};
// 关联设备列表
export const getPointRelationInfo = (params: {
  pointCode: string;
}): Promise<Global.Response<Pointlist.PointUnitConfig>> => {
  return request.get(`/BasePointRelationInfo/listPage`, { params });
};
// 测点列表 - 关联设备 - 获取设备列表
export function getDeviceList(
  params: Partial<Pointlist.Query> = {},
): Promise<Global.Response<Pointlist.Common>> {
  return request.post(`/DeviceResume/listPage`, { data: params });
}

// 测点列表 - 关联设备 - 给测点关联设备
export async function addDevice(
  params: Partial<Pointlist.Query> = {},
): Promise<Global.Response<Pointlist.Common>> {
  return request.post(`/BasePointDevice/`, { data: JSON.stringify(params), headers: jsonHeader });
}

// 测点列表 - 取消关联 - 取消设备关联
export async function deleteDevice(
  params: Partial<Pointlist.Query> = {},
): Promise<Global.Response<Pointlist.Common>> {
  return request.delete(`/BasePointDevice/removeDevice`, { data: params });
}

// 测点列表 - 测点详情 - 获取当前测点值
export const getPointValue = (params: {
  pointCode: string;
}): Promise<Global.Response<Pointlist.PointValue[]>> => {
  return request.post(`/nr/data/getRealTimeKey`, { data: params });
};

// 测点列表 - 查询测点明细
interface IGetPointDetail {
  compareId: string;
  pointType: 'state' | 'analog';
  pageSize?: number;
  pageNum?: number;
}
export const queryMeasurementPointDetails = (
  params: IGetPointDetail,
): Promise<Global.Response<Pointlist.HistoricalDetails>> => {
  return request.get('/BasePointDiffDatabase/listPage', {
    params,
  });
};
// 获取图例中心列表
export async function getPointAnalysisList(
  params: Partial<PointAnalysis.Query>,
): Promise<Global.Response<PointAnalysis.Common>> {
  return request.get(`/BasePointAnalyseOption/listPage`, { params });
}

// 获取我的图例
export async function getMyLegendList(
  params: Partial<PointAnalysis.Query>,
): Promise<Global.Response<PointAnalysis.Common>> {
  return request.get(`/BasePointAnalyseOption/my/Schem`, { params });
}
// 获取收藏和分享
export async function getShareAndFavoriteList(
  params: Partial<PointAnalysis.Query>,
): Promise<Global.Response<PointAnalysis.Common>> {
  return request.get(`/BasePointAnalyseOption/fetchUserFavoritesShares`, { params });
}

// 新增测点分析
export async function addPointAnalysisList(
  data: PointAnalysis.Add,
): Promise<Global.Response<Global.Code>> {
  return request.post(`/BasePointAnalyseOption`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
// 修改测点分析
export async function updatePointAnalysisList(
  data: PointAnalysis.Update,
): Promise<Global.Response<Global.Code>> {
  return request.put(`/BasePointAnalyseOption`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
// 删除测点分析
export async function deletePointAnalysisList(id: string): Promise<Global.Response<Global.Code>> {
  return request.delete(`/BasePointAnalyseOption/${id}`);
}

// 获取详情
export async function getPointAnalysisDetail(
  id: string,
): Promise<Global.Response<PointAnalysis.Detail>> {
  return request.get(`/BasePointAnalyseOption/${id}`);
}

// 添加或取消收藏
export async function addOrCancelFavorite(
  data: PointAnalysis.Collect,
): Promise<Global.Response<Global.Code>> {
  return request.post(`/BasePointAnalyseConcern/saveFavorites`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}

// 分享
export async function share(data: PointAnalysis.Share): Promise<Global.Response<Global.Code>> {
  return request.post(`/BasePointAnalyseConcern/saveShare`, {
    data: JSON.stringify({
      ...data,
      myShare: 1,
    }),
    headers: jsonHeader,
  });
}
// 取消分享
export async function cancelShare(
  data: PointAnalysis.CancelShare,
): Promise<Global.Response<Global.Code>> {
  return request.post(`/BasePointAnalyseConcern/Unshare`, {
    data: JSON.stringify({
      ...data,
      myShare: 0,
    }),
    headers: jsonHeader,
  });
}
// 取消别人的分享
export async function cancelShareWithMe(
  data: PointAnalysis.CancelShareWithMe,
): Promise<Global.Response<Global.Code>> {
  return request.post(`/BasePointAnalyseConcern/unShareWithMe`, {
    data: JSON.stringify({
      ...data,
      shareWithMe: 0,
    }),
    headers: jsonHeader,
  });
}

// 获取测点列表历史数据 - 封装（测点分析专用）
export async function getPointListHistoryData(
  data: PointAnalysis.HistoryQuery,
): Promise<Global.Response<PointAnalysis.History[]>> {
  return request.post(`/BasePointAnalyseOption/getHistoryGroupByKey`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}

/**
 * 用于插入测试数据
 */
interface IInsertFlux {
  datatype: 'state' | 'analog';
  key: string; // 编码
  name: string; // 名称
  quality: string; // 质量
  realTimeStr: string; // 时间
  value: number; // 值
}
export async function insertFlux(data: IInsertFlux) {
  return request.post('/nr/data/insertFlux', { data: JSON.stringify(data), headers: jsonHeader });
}

// 历史数据导出
export async function exportHistoryData(data: {
  selectBegin: string;
  selectEnd: string;
  basePointList: any[];
}): Promise<Global.Response<Blob>> {
  return request.post('/BasePointAnalyseOption/export', {
    data: JSON.stringify(data),
    responseType: 'blob',
    headers: jsonHeader,
  });
}

// 获取测点历史数据
export async function getPointHistoryData(params: {
  key: string;
  max?: number;
  min?: number;
  selectBegin?: string;
  selectEnd?: string;
}): Promise<Global.Response<PointAnalysis.History[]>> {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });

  return request.post(`/nr/data/getDataWithRange`, {
    data: formData,
  });
}
