import { FC, MouseEvent, ReactNode, memo, useEffect, useRef, useState } from 'react';
import styles from './right-equip-modal.component.less';
import { TEST_LIST, type TreeOptions, type PropOptions, TEST_LIST2, chartAxisStyle, DataOptions } from './right-equip-modal';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { Column } from '@antv/g2plot';
import { throttle } from '@/utils/utils';
//限制节点点击滚动时触发chartScrollHandler方法得效果。
let scrollTf: boolean = false;
let scrollTimeout: NodeJS.Timeout | null = null;
export const RightEquipModal: FC<PropOptions> = memo(({ open, close }) => {
  const [treeNode, setTreeNode] = useState<string>('1');
  const treeRef = useRef<any>(null);
  useEffect(() => {
    if (treeRef.current) {
      treeRef.current.treeData = TEST_LIST;
      treeRef.current.nodeSelect = treeNodeChange;
    }
    chartInit();
    return () => {
      scrollTf = false;
      scrollTimeout && clearTimeout(scrollTimeout);
    };
  }, []);
  //弹框关闭
  function closeHandler(): void {
    close();
  }
  //节点选中事件
  function treeNodeChange(val: TreeOptions): void {
    scrollTf = true;
    setTreeNode(val.id);
    const element = document.getElementsByClassName(`modal-container-chart-item-${val.id}`);
    if (element) {
      scrollTimeout && clearTimeout(scrollTimeout);
      //滚动到元素区域
      element[0].scrollIntoView({ behavior: 'smooth' });
      //滚动结束1000ms后，放开限制
      scrollTimeout = setTimeout(() => {
        scrollTf = false;
      }, 1000);
    }
  }
  //chart渲染模板
  function chartComponent(): ReactNode {
    return TEST_LIST.map((item) => (
      <div key={item.id} className={`${styles['modal-container-chart-item']} modal-container-chart-item-${item.id}`}>
        <p className={styles['modal-container-chart-title']}>
          <span>{item.name}实时温度监测</span>
        </p>
        <div id={`modal-container-chart-item-main-${item.id}`} className={styles['modal-container-chart-item-main']}></div>
      </div>
    ));
  }
  //统计图初始化
  function chartInit(): void {
    TEST_LIST.forEach((item) => {
      const plot = new Column(`modal-container-chart-item-main-${item.id}`, {
        height: 280,
        data: TEST_LIST2,
        padding: [40, 10, 30, 40],
        xField: 'label',
        yField: 'value',
        columnStyle: {
          //柱子边框描边
          stroke: '#11B8F8',
          //柱子背景色
          fill: 'l(90) 0:rgba(17,179,243,0.7) 1:rgba(17,179,243,0.05)',
        },
        xAxis: chartAxisStyle.xAxis,
        yAxis: chartAxisStyle.yAxis,
        tooltip: {
          enterable: true,
          formatter: (datum: any) => {
            const data: DataOptions = datum;
            return { name: '温度', value: data.value + '℃' };
          },
        },
        minColumnWidth: 20,
        maxColumnWidth: 20,
      });
      plot.render();
    });
  }
  //统计图区域滚动处理
  function chartScrollHandler(e: any): void {
    if (scrollTf === false) {
      //区域滚动条垂直位置
      const scrollPosition: number = e.target.scrollTop;
      TEST_LIST.forEach((item, index) => {
        const dom: any = document.getElementsByClassName(`modal-container-chart-item-${item.id}`)[0];
        if (dom) {
          const itemTop = dom.offsetTop;
          const itemHeight = dom.offsetHeight;
          //判断是否在该区域内，如果是，则将该节点选中
          if (scrollPosition >= itemTop && scrollPosition < itemTop + itemHeight - 90) {
            setTreeNode(item.id);
          }
        }
      });
    }
  }
  return (
    <SvgModal width="1000px" close={closeHandler} isFooterBtn={false} open={open} title="在线温度监测">
      <div className={styles['modal-container']}>
        {/* 节点列表 */}
        <div className={styles['modal-container-tree']}>
          <checked-tree value={treeNode} ref={treeRef} />
        </div>
        {/* 统计图列表 */}
        <div onScroll={throttle(chartScrollHandler, 500)} className={styles['modal-container-chart']}>
          {chartComponent()}
        </div>
      </div>
    </SvgModal>
  );
});
