import { FC, forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { PropOptions } from './line-chart';
import styles from './line-chart.component.less';
import { Empty } from 'antd';
import { Line } from '@antv/g2plot';
import { chartAxisStyle } from '@/pages/equip-status-bim/components/right-equip-modal/right-equip-modal';
import {
  DataOptions,
  SortOptions,
  dataGroudSort,
} from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
//排序后的数据
let sortList: SortOptions[] = [];
//计时器
let timer: NodeJS.Timeout | undefined;
//实时执行开关
let realTimeSwitch: boolean = true;
export const LineChart: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        stepType,
        sliderColor = '#fff',
        color,
        padding = [60, 10, 70, 40],
        legend = true, //是否需要图例
        smooth = true, //曲线是否平滑过渡
        realTimeFn, // 实时请求方法
        sliderFn, // 缩率图拖动方法
        isRealTime = false, // 是否开启实时请求
        realTime = 1000, // 实时请求间隔
        chartData, // 统计图数据
        loading = false, // 加载状态
        unit = '', // 统计图单位
        unitY = 50,
      },
      ref,
    ) => {
      const [chart, setChart] = useState<Line>(); // 统计图实例
      const chartRef = useRef<any>(null);
      useImperativeHandle(ref, () => ({
        clear,
        stopRealTime,
      }));
      useEffect(() => {
        return clear;
      }, []);
      useEffect(() => {
        if (isRealTime === false) {
          stopRealTime();
        }
      }, [isRealTime]);
      useEffect(() => {
        if (chartData.length > 0) {
          chartInit();
        } else if (chart) {
          clear();
        }
      }, [chartData]);
      // 清除
      function clear(): void {
        sortList = [];
        stopRealTime();
        if (chart) {
          chart.destroy();
          setChart(undefined);
        }
      }
      // 停止实时请求
      function stopRealTime(): void {
        if (timer) {
          clearInterval(timer);
          timer = undefined;
          realTimeSwitch = true;
        }
      }
      // 实时数据处理
      function realTimeDataHandler(): void {
        timer = setInterval(async () => {
          if (realTimeSwitch) {
            if (realTimeFn) {
              realTimeSwitch = false;
              await realTimeFn();
              realTimeSwitch = true;
            }
          }
        }, realTime);
      }
      // 初始化统计图
      function chartInit(): void {
        // 数据排序
        sortList = dataGroudSort(chartData, {});
        if (chart) {
          chart.changeData(chartData);
        } else {
          const line = new Line(chartRef.current, {
            padding,
            data: chartData,
            xField: 'label', // x 轴字段
            yField: 'value', // y 轴字段
            seriesField: 'type', // 系列字段
            color,
            stepType,
            xAxis: {
              ...chartAxisStyle.xAxis,
              range: [0, 1], // 强制 x 轴范围从 0 到数据的最大值
            },
            yAxis: {
              tickCount: 5,
              // max: 50,//最大值
              // min: 30,//最小值
              grid: chartAxisStyle.yAxis.grid,
              label: chartAxisStyle.yAxis.label,
              title: {
                ...chartAxisStyle.yAxis.title,
                text: unit,
                style: {
                  ...chartAxisStyle.yAxis.title.style,
                  y: unitY,
                },
              },
            },
            legend: legend
              ? {
                  position: 'top-right',
                  itemHeight: 30, // 设置每个 item 的高度
                  maxHeight: 50, // 设置图例最大高度，避免溢出
                  itemName: {
                    style: {
                      inactiveFill: '#753',
                      fill: '#fff',
                      fontSize: 12,
                    },
                  },
                }
              : false,
            tooltip: {
              enterable: true,
              domStyles: {
                'g2-tooltip-title': {
                  fontSize: '14px',
                  color: '#CCE8FF',
                },
              },
              formatter: (datum: any) => {
                const data: DataOptions = datum;
                let value = data.value + ' ' + unit;
                const node = sortList.find((item) => item.label === data.label);
                if (node) {
                  const topData = node.top3.find((item) => item.type === data.type);
                  const minData = node.min.type === data.type ? node.min : undefined;
                  if (topData) {
                    value = `<span style="color:${topData.color}">${
                      data.value + ' ' + unit
                    }</span>`;
                  } else if (minData) {
                    value = `<span style="color:${minData.color}">${
                      data.value + ' ' + unit
                    }</span>`;
                  }
                  return { name: data.type, value };
                } else {
                  return { name: data.type, value };
                }
              },
              showCrosshairs: true,
              crosshairs: {
                line: {
                  style: {
                    stroke: '#1E89F4', // 设置竖线的颜色
                  },
                },
              },
            },
            smooth,
            // scrollbar: {
            //滚动条
            //     categorySize: 100,
            //     style: {
            //         thumbColor: '#1a448d',
            //         thumbHighlightColor: '#1a448d',
            //     }
            // },
            slider: {
              //缩略图
              start: 0,
              end: 1,
              trendCfg: {
                lineStyle: {
                  //趋势样式
                  stroke: sliderColor,
                },
              },
              handlerStyle: {
                //手柄样式
                stroke: 'transparent',
                fill: '#5192F3',
              },
              textStyle: {
                //文本样式
                fill: '#fff',
                fillOpacity: 0.9,
              },
              foregroundStyle: {
                //前景样式
                fill: '#2356B0',
                fillOpacity: 0.7,
              },
            },
            //线条动画
            animation: {
              appear: {
                animation: 'path-in',
                duration: 5000,
              },
            },
          });
          // 使用 plot.on 监听 slider 的变化
          line.on('slider:valuechanged', sliderChange);
          line.render();
          setChart(line);
        }
        if (timer == undefined && isRealTime) {
          realTimeDataHandler();
        }
      }
      /*
       **缩率图拖动处理
       */
      function sliderChange(): void {
        if (sliderFn) {
          sliderFn();
        }
      }
      return (
        <div ref={chartRef} className={styles['chart-container']}>
          {/* 统计区域 */}
          {loading === false && chartData.length === 0 ? (
            <div className={styles.empty}>
              <Empty description="暂无数据" />
            </div>
          ) : null}
        </div>
      );
    },
  ),
);
