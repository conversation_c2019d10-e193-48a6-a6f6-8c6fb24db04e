import { Fragment, memo, useEffect, useRef, useState } from 'react';
import styles from './monitoring-point-annotation.page.less';
import { MonitoringPoint } from './components/monitoring-point/monitoring-point.component';
import { BimTagging } from './components/bim-tagging/bim-tagging.component';
import { TreeOptions } from './components/monitoring-point/monitoring-point';
const MonitoringPointAnnotation = () => {
  const [nodeData, setNodeData] = useState<TreeOptions>({} as TreeOptions);
  const [pointData, setPointData] = useState<TreeOptions>({} as TreeOptions);
  const pointRef = useRef<any>(null);
  if (pointRef.current) {
    pointRef.current.treeRef.current.loadDataListFn = treeUpdate;
  }
  //树节点选中
  function treeNodeChange(node: TreeOptions | undefined): void {
    if (node) {
      if (node.pointCode) {
        setPointData(node);
        setNodeData(node.parentNode);
      } else {
        setPointData({} as TreeOptions);
        setNodeData(node);
      }
    }
  }
  //监听树节点更新，控制BimTagging组件的图片监测点数据
  function treeUpdate(node: TreeOptions, list: TreeOptions[]): void {
    //更新当前选中设备图片节点
    if (node.level === 1 && node.id === nodeData.id) {
      node.children = list;
      setNodeData({ ...nodeData, children: list });
      if (pointData.id) {
        const data = list.find((item) => item.id === pointData.id);
        if (data == undefined) {
          setPointData({} as TreeOptions);
        }
      }
    } else if (node.level === 0 && node.id === nodeData.parentId) {
      //更新当前选中根节点
      setNodeData(node);
      setPointData({} as TreeOptions);
    }
  }
  //标注点更新:删除也会触发
  function taggChange(): void {
    if (pointRef.current) {
      pointRef.current.treeRef.current.updateNode(nodeData.id);
    }
  }
  //标注删除
  function taggDelChange(): void {}
  return (
    <div className={styles['monitor-point-annotation']}>
      {/* 监测点树 */}
      <MonitoringPoint ref={pointRef} treeNodeChange={treeNodeChange} />
      {/* 标注 */}
      <BimTagging delChange={taggDelChange} taggChange={taggChange} pointData={pointData} nodeData={nodeData} />
    </div>
  );
};

export default memo(MonitoringPointAnnotation);
