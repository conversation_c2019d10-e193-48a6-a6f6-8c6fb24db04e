import styled from 'styled-components';

import BgPanel_Image from '@/assets/images/topology-content/bg_panel.png';

interface IProps {
  $visible: boolean;
}

export const DetailPanelWrapper = styled.div<IProps>`
  ${(props) => !props.$visible && 'display: none;'}
  position: absolute;
  z-index: 9;
  top: 5%;
  bottom: 5%;
  margin: 50px auto 0;
  right: 10px;
  width: 20%;
  border-radius: 1px;
  background-color: #0e2758;
  opacity: 0.9;
  overflow-y: auto;

  .title-bar {
    height: 30px;
    width: 94%;
    margin: 3% auto 0;
    padding-left: 5%;
    background: url(${BgPanel_Image});
    line-height: 30px;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    border-radius: 1px;
  }

  // Description
  .ant-descriptions {
    width: 96%;
    margin: auto;

    .ant-descriptions-row {
      border-color: #133769 !important;

      & > th.ant-descriptions-item-label {
        border-color: #133769 !important;
        text-align: center;
      }
    }
  }

  // Table
  .ant-table-wrapper {
    margin: 2% auto 0;
    width: 96%;
  }
`;
