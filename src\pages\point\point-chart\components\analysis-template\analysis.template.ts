import styled from 'styled-components';

interface WrapperProps {
  height?: number;
  titleFontSize?: number;
  rightWidth?: number;
}

export const ChartTemplateWrapper = styled.div<WrapperProps>`
  background-color: rgba(40, 130, 232, 0.14);
  border-radius: 2px;
  padding: 20px;
  height: ${(props) => `${props.height || 600}px`};
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    svg {
      cursor: pointer;
    }
    h2 {
      font-weight: 400;
      font-size: ${(props) => `${props.titleFontSize || 20}px`};
      color: #ffffff;
    }
    .setting-active {
      color: #0ad0ee;
    }
    .icon {
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      transition: color 0.3s ease;
      &:hover,
      &.active {
        color: #0ad0ee;
      }
    }
  }
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 12px;
    min-height: 200px;
    position: relative;
    .button {
      margin-bottom: 12px;
      .ant-btn {
        margin: 0;
      }
    }
    .wrapper {
      flex: 1;
      display: flex;
      min-height: 150px;
      position: relative;
      .chart-wrapper {
        flex: 1;
      }
    }
    .right {
      width: 160px;
      padding: 10px;
      margin: 0;
      background: #0a275f;
      display: flex;
      flex-direction: column;
      gap: 16px;
      border-radius: 8px;
      overflow-y: scroll;
      overflow-x: hidden;
      h4 {
        font-size: 14px;
        color: #ffffff;
        flex-shrink: 0;
        margin: 0;
      }
      .list {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
        cursor: pointer;
        width: 140px;
        .item {
          height: 30px;
          background: transparent;
          border-radius: 2px;
          padding: 0 8px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          line-height: 30px;
          font-size: 14px;
          color: #ffffff;
          &.active,
          &:hover {
            background: #2b69b2;
          }
        }
      }
    }
  }
`;
