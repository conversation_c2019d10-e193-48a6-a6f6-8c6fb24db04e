{"name": "micro_operations", "private": true, "scripts": {"start": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" umi dev", "build": "node --max-old-space-size=4096 ./node_modules/umi/bin/umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "webpack": "umi webpack"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@alitajs/keep-alive": "^2.9.0", "@ant-design/icons": "^5.2.6", "@ant-design/pro-form": "^2.11.3", "@ant-design/pro-layout": "^6.5.0", "@ant-design/pro-table": "^3.6.2", "@antv/g2": "5.2.11", "@antv/g2plot": "^2.4.31", "@dvgis/dc-sdk": "3.1.0", "@emotion/is-prop-valid": "^1.2.2", "@grapecity-software/spread-excelio": "18.1.0", "@grapecity-software/spread-sheets": "18.1.0", "@grapecity-software/spread-sheets-barcode": "18.1.0", "@grapecity-software/spread-sheets-charts": "18.1.0", "@grapecity-software/spread-sheets-designer": "18.1.0", "@grapecity-software/spread-sheets-designer-react": "18.1.0", "@grapecity-software/spread-sheets-designer-resources-cn": "18.1.0", "@grapecity-software/spread-sheets-io": "18.1.0", "@grapecity-software/spread-sheets-languagepackages": "18.1.0", "@grapecity-software/spread-sheets-pdf": "18.1.0", "@grapecity-software/spread-sheets-pivot-addon": "18.1.0", "@grapecity-software/spread-sheets-print": "18.1.0", "@grapecity-software/spread-sheets-react": "18.1.0", "@grapecity-software/spread-sheets-resources-zh": "18.1.0", "@grapecity-software/spread-sheets-shapes": "18.1.0", "@grapecity-software/spread-sheets-tablesheet": "18.1.0", "@stomp/stompjs": "^7.0.0", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.0", "@types/react-dom": "18.2.0", "@types/uuid": "^9.0.8", "@uiw/react-amap": "^5.0.1", "@umijs/plugin-qiankun": "^2.43.3", "ahooks": "^3.7.8", "ailabel": "^5.1.15", "antd": "^5.17.2", "axios": "^1.6.5", "bpmn-js": "^17.2.1", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "framer-motion": "^12.4.7", "jsencrypt": "^3.3.2", "moment": "^2.30.1", "numeral": "^2.0.6", "qs": "^6.11.1", "react": "18.2.0", "react-contexify": "^6.0.0", "react-countup": "^6.5.0", "react-dom": "18.2.0", "react-draggable": "^4.4.5", "react-flow-renderer": "^10.3.17", "react-redux": "^9.1.0", "rxjs": "^7.8.1", "sockjs-client": "^1.6.1", "umi": "^3.5.39", "umi-plugin-keep-alive": "^0.0.1-beta.35", "umi-request": "^1.4.0", "x-data-spreadsheet": "^1.1.9", "xlsx": "^0.18.5"}, "devDependencies": {"@types/history": "^5.0.0", "@types/react": "^18.3.23", "@types/sockjs-client": "^1.5.4", "@types/styled-components": "^5.1.28", "@umijs/preset-react": "^1.8.32", "@umijs/test": "^4.0.66", "babel-plugin-styled-components": "^2.1.4", "cross-env": "^7.0.3", "eslint": "^8.37.0", "lint-staged": "^10.0.7", "prettier": "^2.8.8", "react-jsx-runtime": "^1.0.0-alpha.1", "styled-components": "^6.0.8", "stylelint": "^15.6.0", "stylelint-config-css-modules": "^4.2.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^32.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.7.0", "stylelint-order": "^6.0.3", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "tslint-react": "^5.0.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}}