import { ReactNode, useEffect, useRef, useState } from 'react';
import styles from './report-add.page.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { SpreadTable } from '@/components/spread-table/spread-table.component';
import { history, useParams } from 'umi';
import { DatePicker, Form, Input, Spin, message } from 'antd';
import {
  InfluxQueryOptions,
  PlanQueryOptions,
  ReportHistoryOptions,
  addReport,
  planDataValue,
  reportAnalysisData,
  selectNrInflux,
  templateData,
} from '@/services/intelligentReport/report-management';
import { DetailOptions } from '@/pages/intelligentReport/templateManagement/pages/addTemplateVersion/add-template-version';
import {
  AuthoritieOptions,
  CellOptions,
  InfluxesEnums,
  UseImperativeOptions,
} from '@/components/spread-table/spread-table';
import { getLastMonthDate, getQuarter, toExcelCoordinate } from '@/utils/utils';
import { PageType } from '../../monthly-production-report';
import { AttributeOptions, FormOptions, HistoryOptions, InfluxesOptions } from './report-add';
import { PlanTypeEnum } from '@/services/intelligentReport/report-plan';
import dayjs from 'dayjs';
const ReportAddPage = () => {
  const [loading, setLoading] = useState<boolean>(false);
  //模板详情
  const [temData, setTemData] = useState<DetailOptions>();
  //工作薄操作实例
  const [designer, setDesigner] = useState<any>();
  //当前属性填充列表
  const [curAttributeList, setCurAttributeList] = useState<CellOptions[]>([]);
  //当前历史填充列表
  const [curHistoryList, setCurHistoryList] = useState<CellOptions[]>([]);
  //当前测试类型填充列表
  const [curInfluxList, setCurInfluxList] = useState<CellOptions[]>([]);
  const spreadRef = useRef<UseImperativeOptions>({} as UseImperativeOptions);
  const [forms] = Form.useForm<FormOptions>();
  //报表页面编码
  const { code } = useParams<{ code: string }>();
  useEffect(() => {
    getTemplateData();
    forms.setFieldValue('year', dayjs(getLastMonthDate()));
  }, []);
  useEffect(() => {
    if (temData && designer) {
      createWorkTable(temData);
    }
  }, [temData, designer]);
  //设置编辑权限区域
  function setAuthoritieBack(authorities: AuthoritieOptions[], status: boolean): void {
    spreadRef.current.tableCheckRangeEditHandler(authorities, status);
  }
  //获取最新模板版本数据
  function getTemplateData(): void {
    setLoading(true);
    templateData(code)
      .then((res) => {
        if (res.code === '1') {
          setTemData(res.data);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //创建工作薄
  function createWorkTable(data: DetailOptions): void {
    spreadRef.current.importJSON(data.templateData);
    spreadRef.current.verticalPositionTop();
    setAuthoritieBack(data.authorities || [], true);
    spreadRef.current.setActiveTableIndex(0);
    onDeteChange('', dayjs(getLastMonthDate()).format('YYYY-MM'));
  }
  //创建表格
  function createTable(designer: any): void {
    setDesigner(designer);
  }
  //获取工作薄数据
  function getWorkData(): any {
    temData && setAuthoritieBack(temData.authorities, false);
    const snapshot = spreadRef.current.getWorkData();
    return snapshot;
  }
  //暂存
  async function save(): Promise<any> {
    forms.validateFields().then((res) => {
      setLoading(true);
      const { monthlyName, year } = res;
      addReport({
        type: code,
        isAudit: 0,
        monthlyName,
        year: year.$y,
        month: year.$M + 1,
        monthlyData: JSON.stringify(getWorkData()),
      })
        .then((res) => {
          if (res.code === '1') {
            message.success('暂存成功！');
            history.replace('/monthlyProductionReportEdit/' + res.data, {
              type: PageType.EDIT,
            });
          } else {
            temData && setAuthoritieBack(temData.authorities, true);
            message.error(res.error || '暂存失败！');
          }
          setLoading(false);
        })
        .catch((err) => {
          temData && setAuthoritieBack(temData.authorities, true);
          setLoading(false);
        });
    });
  }
  //提交
  function commit(): void {
    forms.validateFields().then((res) => {
      setLoading(true);
      const { monthlyName, year } = res;
      addReport({
        type: code,
        isAudit: 1,
        monthlyName,
        year: year.$y,
        month: year.$M + 1,
        monthlyData: JSON.stringify(getWorkData()),
      })
        .then((res) => {
          if (res.code === '1') {
            message.success('提交成功！');
            history.goBack();
          } else {
            temData && setAuthoritieBack(temData.authorities, true);
            message.error(res.error || '提交失败！');
          }
          setLoading(false);
        })
        .catch((err) => {
          temData && setAuthoritieBack(temData.authorities, true);
          setLoading(false);
        });
    });
  }
  //计划数据时间格式处理
  function planDateFormatHandler(date: string, planCycle: PlanTypeEnum): string {
    switch (planCycle) {
      case PlanTypeEnum.YEAR:
        return date.substring(0, 4);
      case PlanTypeEnum.QUARTER:
        return date.substring(0, 4) + '/' + getQuarter(new Date(date));
      case PlanTypeEnum.MONTH:
        return date.substring(0, 4) + '-' + parseInt(date.substring(5, 7));
    }
  }
  //历史数据时间格式处理
  function historyDateFormatHandler(date: string, planCycle: PlanTypeEnum): string {
    const newDate = date.substring(0, 4) + '-' + parseInt(date.substring(5, 7));
    switch (planCycle) {
      case PlanTypeEnum.YEAR:
        return newDate;
      case PlanTypeEnum.QUARTER:
        return newDate;
      case PlanTypeEnum.MONTH:
        return newDate;
    }
  }
  //测点数据时间格式处理
  function influxDateFormatHandler(date: string): { startTime: string; endTime: string } {
    const year = date.substring(0, 4);
    const month = date.substring(5);
    const startTime = date + '-01 00:00';
    const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
    const endTime = date + `-${lastDay} 23:59`;
    return {
      endTime,
      startTime,
    };
  }
  //属性查询参数处理
  function attributeQueryHandler(date: string): PlanQueryOptions[] {
    if (temData && temData.regions) {
      const list = temData.regions.filter((item) => item.type === '0');
      return list.map((item) => {
        const planDate = planDateFormatHandler(date, item.planCycle);
        return {
          attributeId: item.attributeId,
          planCycle: item.planCycle,
          planDate: planDate,
        };
      });
    } else {
      return [];
    }
  }
  //坐标查询参数处理
  function positionQueryHandler(date: string): ReportHistoryOptions[] {
    if (temData && temData.regions) {
      const list = temData.regions.filter((item) => item.type === '1');
      return list.map((item) => {
        const planDate = historyDateFormatHandler(date, item.planCycle);
        return {
          rowCount: item.rowCount,
          columnCount: item.colCount,
          planCycle: item.planCycle,
          date: planDate,
          sheetId: item.tableId,
          type: code,
        };
      });
    } else {
      return [];
    }
  }
  //测点查询参数处理
  function influxesQueryHandler(date: string): InfluxQueryOptions[] {
    if (temData && temData.influxes) {
      const list = temData.influxes;
      return list.map((item) => {
        const { endTime, startTime } = influxDateFormatHandler(date);
        const dataType =
          item.type === InfluxesEnums.MAX || item.type === InfluxesEnums.MIN
            ? InfluxesEnums.MAX_OR_MIN
            : item.type;
        return {
          key: item.measureId, //测点code
          startTime,
          endTime,
          dataType,
          dateType: item.dateType,
          type: item.value,
        };
      });
    } else {
      return [];
    }
  }
  //清除上一次的填充值
  function clearPrevDataHandler(): void {
    const attributeList = curAttributeList.map((item) => {
      return {
        ...item,
        value: '',
      };
    });
    const histroyList = curHistoryList.map((item) => {
      return {
        ...item,
        value: '',
      };
    });
    const influxesList = curInfluxList.map((item) => {
      return {
        ...item,
        value: '',
      };
    });
    spreadRef.current.setTableCellValue(attributeList);
    spreadRef.current.setTableCellValue(histroyList);
    spreadRef.current.setTableCellValue(influxesList);
  }
  //填充属性值到表格
  function setAttributeDataHandler(list: AttributeOptions[]): void {
    if (list.length > 0 && temData?.regions) {
      const newList: CellOptions[] = temData.regions
        .filter((item) => item.type === '0')
        .map((item) => {
          const { tableId, planCycle, attributeId, startPosition, endPosition } = item;
          const data = list.find(
            (ritem) => ritem.planCycle === planCycle && ritem.attributeId === attributeId,
          );
          if (data) {
            const position = `${startPosition}:${endPosition}`;
            return {
              position,
              value: data.value,
              tableId: tableId,
            };
          } else {
            return {
              position: '',
              value: '',
              tableId: '',
            };
          }
        });
      setCurAttributeList(newList);
      spreadRef.current.setTableCellValue(newList);
    }
  }
  //填充历史数据到表格
  function setHistoryDataHandler(list: HistoryOptions[]): void {
    if (list.length > 0) {
      const newList: CellOptions[] = list.map((item) => {
        const start = toExcelCoordinate(parseInt(item.rowCount), parseInt(item.columnCount));
        const position = `${start}:${start}`;
        return {
          position,
          value: item.value,
          tableId: item.sheetId,
        };
      });
      setCurHistoryList(newList);
      spreadRef.current.setTableCellValue(newList);
    }
  }
  //填充测点类型数据到表格
  function setInfluxDataHandler(list: InfluxesOptions[]): void {
    if (list.length > 0 && temData && temData.influxes) {
      const newList: CellOptions[] = list.map((item) => {
        const data = temData.influxes.find((citem) => {
          const type =
            citem.type === InfluxesEnums.MAX || citem.type === InfluxesEnums.MIN
              ? InfluxesEnums.MAX_OR_MIN
              : citem.type;
          return (
            citem.measureId === item.key && type === item.dataType && citem.value === item.type
          );
        });
        if (data) {
          const position: string = `${data.startPosition}:${data.endPosition}`;
          return {
            position,
            value: item.dataType === InfluxesEnums.SWITCH ? item.switchValue : item.influxValue,
            tableId: data.tableId,
          };
        } else {
          return {
            position: '',
            value: '',
            tableId: '',
          };
        }
      });
      setCurInfluxList(newList);
      spreadRef.current.setTableCellValue(newList);
    }
  }
  //获取计划，历史，测点绑定数据
  function getTableOtherData(date: string): void {
    setLoading(true);
    const planQuery = attributeQueryHandler(date);
    const historyQuery = positionQueryHandler(date);
    const pointQuery = influxesQueryHandler(date);
    Promise.allSettled([
      selectNrInflux(pointQuery),
      reportAnalysisData(historyQuery),
      planDataValue(planQuery),
    ])
      .then((res) => {
        const [pointRes, historyRes, planRes] = res;
        if (pointRes && pointRes.status === 'fulfilled' && pointRes.value.code === '1') {
          setInfluxDataHandler(pointRes.value.data || []);
        }
        if (historyRes && historyRes.status === 'fulfilled' && historyRes.value.code === '1') {
          setHistoryDataHandler(historyRes.value.data || []);
        }
        if (planRes && planRes.status === 'fulfilled' && planRes.value.code === '1') {
          setAttributeDataHandler(planRes.value.data || []);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //清除之前的测点绑定值
  function clearPrevPointData(): void {
    const influxesList = curInfluxList.map((item) => {
      return {
        ...item,
        value: '',
      };
    });
    spreadRef.current.setTableCellValue(influxesList);
  }
  //刷新测点绑定数据
  function refreshPointData(): void {
    clearPrevPointData();
    const date = forms.getFieldValue('year');
    const data = influxesQueryHandler(dayjs(date.$d).format('YYYY-MM'));
    if (data.length > 0) {
      setLoading(true);
      selectNrInflux(data)
        .then((res) => {
          if (res.code === '1') {
            setInfluxDataHandler(res.data);
          }
          setLoading(false);
        })
        .catch((err) => {
          setLoading(false);
        });
    }
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    const reshStyle = 'border-top-right-radius:0px;border-bottom-right-radius:0px';
    const saveStyle = 'border-radius:0px;';
    const addStyle = 'border-top-left-radius:0px;border-bottom-left-radius:0px';
    return (
      <div className={styles['controller-container']}>
        <type-button loading={loading} styles={reshStyle} onClick={refreshPointData}>
          刷新测点
        </type-button>
        <type-button loading={loading} styles={saveStyle} onClick={save}>
          暂存
        </type-button>
        <type-button loading={loading} styles={addStyle} onClick={commit}>
          提交
        </type-button>
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  //日期改变
  function onDeteChange(date: any, dateString: string | string[]): void {
    if (typeof dateString === 'string') {
      clearPrevDataHandler();
      getTableOtherData(dateString);
    }
  }
  return (
    <div className={styles['report-add-page']}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          <span>新增月报</span>
        </div>
        {controllerComponent()}
      </section>
      <section className={styles.form}>
        <Form form={forms} layout="inline">
          <Form.Item<FormOptions>
            rules={[{ required: true, message: '请输入名称' }]}
            name="monthlyName"
            label="月报名称"
          >
            <Input placeholder="请输入..."></Input>
          </Form.Item>
          <Form.Item<FormOptions>
            rules={[{ required: true, message: '请选择报表月份' }]}
            name="year"
            label="报表月份"
          >
            <DatePicker onChange={onDeteChange} picker="month" />
          </Form.Item>
        </Form>
      </section>
      <Spin wrapperClassName={styles.spin} spinning={loading}>
        <section className={styles.excel}>
          <SpreadTable
            isWookTable={true}
            messageOptions={{
              disabledEditCell: '没有权限对该区域进行操作！',
              disabledAddTable: '没有权限新增工作表！',
            }}
            ref={spreadRef}
            createInit={createTable}
          ></SpreadTable>
        </section>
      </Spin>
    </div>
  );
};

export default ReportAddPage;
