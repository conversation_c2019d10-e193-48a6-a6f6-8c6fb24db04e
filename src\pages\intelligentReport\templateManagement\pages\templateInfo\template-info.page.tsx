import { ReactNode, useEffect, useState } from 'react';
import styles from './template-info.page.less';
import { DeleteOutlined, DoubleRightOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { Space, Spin, Tooltip } from 'antd';
import { history, useParams } from 'umi';
import { delTemplateVersion, templateInfo } from '@/services/intelligentReport/template-management';
import { versionStateMap, type DetailOptions, type TemplateVersionOptions } from './template-info';
import { EditTemModal } from '../../components/editTemplateModal/edit-template-modal.component';
import { DelModal } from '@/components/del-modal/del-modal.component';
import { ProductOptions, privilegeProductList } from '@/services/system';
import { FormEditOptions } from '../../components/editTemplateModal/edit-template-modal';
import { productUpdate } from '../../template-management';

const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
const TemplateInfoPage = () => {
  //版本列表
  const [tableList, setTableList] = useState<TemplateVersionOptions[]>([]);
  //编辑弹框控制
  const [editTemModalShow, setEditTemModalShow] = useState<boolean>(false);
  //删除弹框控制
  const [delModalShow, setDelModalShow] = useState<boolean>(false);
  //当前操作数据
  const [curTableData, setCurTableData] = useState<TemplateVersionOptions>();
  //当前模板菜单数据
  const [menuData, setMenuData] = useState<ProductOptions>();
  //详情数据
  const [detail, setDetail] = useState<DetailOptions>();
  const [loading, setLoading] = useState<boolean>(true);
  //路由id
  const { id } = useParams<{ id: string }>();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '版本名称',
      dataIndex: 'versionName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '版本状态',
      dataIndex: 'versionState',
      align: 'center',
      ellipsis: true,
      render: (text: string) => versionStateMap.get(text),
    },
    {
      title: '修改日期',
      dataIndex: 'createDate',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      render: (text: string, record: any) => controllerComponet(record),
      align: 'center',
      ellipsis: true,
      width: 200,
    },
  ];
  useEffect(() => {
    getDetail();
  }, []);
  useEffect(() => {
    if (detail) {
      setTableList(detail.templateVersionList);
    }
  }, [detail]);
  //获取模板菜单数据
  function getTemplateMenuData(data: DetailOptions): void {
    privilegeProductList(data.type).then((res) => {
      if (res.code === '1') {
        ///筛选出pc菜单
        const list = res.data.filter((item) => item.idPath?.startsWith('0,1'));
        if (list.length > 0) {
          setMenuData(list[0]);
        } else {
          setMenuData(undefined);
        }
      }
    });
  }
  //获取详情
  function getDetail() {
    setLoading(true);
    templateInfo(id)
      .then((res) => {
        if (res.code === '1') {
          setDetail(res.data);
          getTemplateMenuData(res.data);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //打开编辑弹框
  function openEditModal(): void {
    setEditTemModalShow(true);
  }
  //刷新页面
  function refreshPage(data: FormEditOptions): void {
    if (detail && data.name !== detail.name) {
      productUpdate();
    }
    getDetail();
  }
  //表格操作栏渲染
  function controllerComponet(record: TemplateVersionOptions): ReactNode {
    const { createDate } = record;
    const look = (
      <Tooltip title="查看">
        <EyeOutlined onClick={() => toLook(record)} className={styles.icon} />
      </Tooltip>
    );
    const edit = (
      <Tooltip title="编辑">
        <EditOutlined onClick={() => toEdit(record)} className={styles.icon} />
      </Tooltip>
    );
    const del = (
      <Tooltip title="删除">
        <DeleteOutlined onClick={() => openDelModal(record)} className={styles.icon} />
      </Tooltip>
    );
    const isAuthoritie = (detail && detail.createUserId === userId) || userId == 1;
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          {look}
          {isAuthoritie && edit}
          {isAuthoritie && del}
        </Space>
      </div>
    );
  }
  //查看版本
  function toLook(data: TemplateVersionOptions): void {
    history.push(`/addTemplateVersion/${id}/${data.id}`, {
      type: 'look',
    });
  }
  //编辑版本
  function toEdit(data: TemplateVersionOptions): void {
    history.push(`/addTemplateVersion/${id}/${data.id}`, {
      type: 'edit',
    });
  }
  //打开删除弹框
  function openDelModal(data: TemplateVersionOptions): void {
    setDelModalShow(true);
    setCurTableData(data);
  }
  //删除表格版本数据方法
  function delTableVersionDataFn(): Promise<any> {
    if (curTableData) {
      return delTemplateVersion(curTableData.id);
    } else {
      return Promise.resolve({});
    }
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    const addStyle = 'border-top-right-radius:0px;border-bottom-right-radius:0px';
    const exportStyle = 'border-top-left-radius:0px;border-bottom-left-radius:0px';
    const isAuthoritie = (detail && detail.createUserId === userId) || userId == 1;
    return (
      <div className={styles['controller-container']}>
        {isAuthoritie && (
          <>
            <type-button onClick={openEditModal} styles={addStyle}>
              编辑基本信息
            </type-button>
            <type-button onClick={toAdd} styles={exportStyle}>
              新增版本
            </type-button>
          </>
        )}
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //跳转新增版本页面
  function toAdd(): void {
    history.push(`/addTemplateVersion/${id}/add`);
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  return (
    <div className={styles['report-add-page']}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          <span>{detail?.name}模板信息</span>
        </div>
        {controllerComponent()}
      </section>
      <section className={styles.info}>
        <p className={styles['info-title']}>
          <span>基本信息：</span>
        </p>
        <Spin spinning={loading}>
          <div className={styles['base-info']}>
            <div className={styles['base-info-item']}>
              <span>模板名称</span>
              <span>{detail?.name}</span>
            </div>
            <div className={styles['base-info-item']}>
              <span>编码</span>
              <span>{detail?.code}</span>
            </div>
            <div className={styles['base-info-item']}>
              <span>备注</span>
              <span>{detail?.remark}</span>
            </div>
          </div>
        </Spin>
        <div className={styles.line}></div>
        <p className={styles['info-title']}>
          <span>版本列表：</span>
        </p>
        <TableComponent loading={loading} columns={columns} tableList={tableList}></TableComponent>
      </section>
      {/* 版本删除弹框 */}
      <DelModal
        onOk={getDetail}
        delFn={delTableVersionDataFn}
        open={delModalShow}
        close={() => setDelModalShow(false)}
      ></DelModal>
      {/* 信息编辑弹框 */}
      <EditTemModal
        menuData={menuData}
        editData={detail}
        onOk={refreshPage}
        open={editTemModalShow}
        close={() => setEditTemModalShow(false)}
      ></EditTemModal>
    </div>
  );
};

export default TemplateInfoPage;
