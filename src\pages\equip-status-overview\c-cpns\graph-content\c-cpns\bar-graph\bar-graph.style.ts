import styled from 'styled-components';

import bannerBgIcon from '@/assets/images/icon_banner_bg.png';

export const BarGraphWrapper = styled.div`
  .title {
    text-align: center;
    height: 10%;
    font-size: 18px;
    font-weight: bold;
    background: url(${bannerBgIcon});
    background-position: center center;
    background-repeat: no-repeat;
  }

  .chart {
    height: 90%;
    padding-top: 2%;

    /* 修复全局样式污染 */
    .g2-tooltip {
      .g2-tooltip-title {
        color: #fff !important;
      }

      .g2-tooltip-list .g2-tooltip-list-item-value {
        color: #fff !important;
      }
    }
  }
`;
