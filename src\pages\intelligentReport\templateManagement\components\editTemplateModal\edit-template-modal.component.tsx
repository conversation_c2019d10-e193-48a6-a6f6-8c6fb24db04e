import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { FC, memo, useEffect, useState } from 'react';
import { FormEditOptions, PropOptions } from './edit-template-modal';
import { Form, Input, Select, message } from 'antd';
import styles from './edit-template-modal.component.less';
import { editTemplateBaseInfo } from '@/services/intelligentReport/template-management';
import { ProductOptions, updatePrivilegeProduct } from '@/services/system';
import { ResponseOptions } from '@/services/monitoring-point-annotation';

const { TextArea } = Input;
export const EditTemModal: FC<PropOptions> = memo(({ menuData, open, close, onOk, editData }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm();
  useEffect(() => {
    if (open && editData) {
      const { code, name, remark } = editData;
      forms.setFieldsValue({
        code,
        name,
        remark,
      });
    }
  }, [open]);
  function closeFn(): void {
    //updatePrivilegeProduct
    forms.resetFields();
    close();
  }
  //菜单参数处理
  function menuProductDataHandler(
    formData: FormEditOptions,
    menuData: ProductOptions,
  ): ProductOptions {
    return {
      ...menuData,
      name: formData.name,
    };
  }
  //表单请求
  function formRequests(formData: FormEditOptions): Promise<ResponseOptions<any>>[] {
    if (editData && menuData) {
      return [
        updatePrivilegeProduct(menuProductDataHandler(formData, menuData)), //更新菜单
        editTemplateBaseInfo({ ...formData, type: editData.type, id: editData ? editData.id : '' }), //更新模板
      ];
    } else {
      return [];
    }
  }
  function submit(): void {
    if (editData) {
      forms.validateFields().then((data: FormEditOptions) => {
        setLoading(true);
        Promise.allSettled(formRequests(data))
          .then((res) => {
            let fulList = res.filter(
              (item) => item.status === 'fulfilled' && item.value.code === '1',
            );
            if (fulList.length === 2) {
              message.success('更新完成！');
              closeFn();
              onOk(data);
            } else {
              message.warning('更新失败！');
            }
            setLoading(false);
          })
          .catch((err) => {
            setLoading(false);
          });
      });
    }
  }
  return (
    <SvgModal confirmloading={loading} onOk={submit} close={closeFn} open={open} title="编辑信息">
      <div className={styles['modal-container']}>
        <Form className="report-form" labelCol={{ span: 5 }} style={{ width: '100%' }} form={forms}>
          <Form.Item<FormEditOptions>
            rules={[{ required: true, message: '输入名称' }]}
            label="模板名称"
            name="name"
          >
            <Input placeholder="请输入..."></Input>
          </Form.Item>
          <Form.Item<FormEditOptions> label="编码" name="code">
            <Input disabled placeholder="请输入..."></Input>
          </Form.Item>
          <Form.Item<FormEditOptions> label="备注" name="remark">
            <TextArea rows={4} placeholder="请输入..." />
          </Form.Item>
        </Form>
      </div>
    </SvgModal>
  );
});
