.container {
  padding: 20px;
}
.header-fixed {
  position: fixed;
  top: 75px;
  left: 248px;
  right: 20px;
}
.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    color: #ffffff;
  }

  .backBtn {
    cursor: pointer;
    color: #0ad0ee;
    font-size: 14px;
    font-weight: 400;
    &:hover {
      opacity: 0.8;
    }
  }
}

.template-container {
  margin-top: 80px;
  display: grid;
  gap: 20px;
  width: 100%;
  transition: all 0.3s ease;
  height: calc(100vh - 180px);
  scrollbar-gutter: stable; // 稳定滚动条
  overflow-y: auto; // 开启垂直滚动

  &.layout-1 {
    grid-template-columns: 1fr;
  }

  &.layout-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  &.layout-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.title-container {
  display: flex;
  align-items: center;
  input {
    margin-right: 8px;
  }
  .edit-icon {
    margin-left: 8px;
    cursor: pointer;
  }
}
