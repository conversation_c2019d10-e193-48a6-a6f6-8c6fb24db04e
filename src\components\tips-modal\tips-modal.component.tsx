import { FC, ReactNode, memo } from 'react';
import { SvgModal } from '../svg-modal/svg-modal.component';
import { PropOptions } from './tips-modal';
import styles from './tips-modal.component.less';
import { InfoCircleOutlined } from '@ant-design/icons';

export const TipsModal: FC<PropOptions> = memo(
  ({ loading = false, icon, open, close, onOk, children, tipsText, title = '提示信息' }) => {
    //提示模板
    function tipsComponent(): ReactNode {
      return (
        <div className={styles['del-tips-container']}>
          {icon ? icon : <InfoCircleOutlined className={styles.tips} />}
          <span className={styles.text}>{tipsText}</span>
        </div>
      );
    }
    //提交处理
    function commit(): void {
      onOk();
    }
    return (
      <SvgModal
        height="200px"
        confirmloading={loading}
        onOk={commit}
        close={close}
        open={open}
        title={title}
      >
        <div className={styles['tips-modal-container']}>
          {children ? children : tipsComponent()}
        </div>
      </SvgModal>
    );
  },
);
