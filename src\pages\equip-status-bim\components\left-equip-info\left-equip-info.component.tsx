import { FC, ReactNode, memo, useEffect, useState } from 'react';
import styles from './left-equip-info.component.less';
import { LeftEquipModal } from '../left-equip-modal/left-equip-modal.component';
import { PropOptions } from './left-equip-info';
import { urlPrefix } from '@/utils/request';
import { Empty, Spin, Tooltip } from 'antd';
import { baseUnitTree } from '@/services/monitoring-point-annotation';
import { TreeOptions } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import {
  CurPosImg,
  PosImg,
  BlueImg,
  RedImg,
  GreenImg,
} from '@/pages/monitoring-point-annotation/components/bim-tagging/bim-tagging.component';
import { ModalComponent } from '../modal/modal.component';
import { pointDataList } from '@/services/equip-status-bim';
import { isJsonString, isMaxOrMin, listOrder } from '@/utils/utils';
import { leftSourceHead, subTheme, unsubTheme } from '../../equip-status-bim';
import { rsaEncrypt } from '@/utils/jsencrypt.util';

const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const LeftEquipInfo: FC<PropOptions> = memo(
  ({ socketClient, openModal, loading, imgData }) => {
    const [modalOpen, setModalOpen] = useState<boolean>(false);
    //设备图片列表
    const [imgList, setImgList] = useState<TreeOptions[]>([]);
    //获取温度后的标注点列表
    const [taggList, setTaggList] = useState<TreeOptions[]>([]);
    //初始标注点列表
    const [initTaggList, setInitTaggList] = useState<TreeOptions[]>([]);
    //socket初始化
    const [socketInit, setSokcetInit] = useState<boolean>(false);
    useEffect(() => {
      if (imgData) {
        if (initTaggList.length > 0) {
          const keyList = initTaggList.map((item) => item.pointCode);
          unsubMsg(keyList);
        }
        getTaggList();
      }
    }, [imgData]);
    useEffect(() => {
      const keyList = initTaggList.map((item) => item.pointCode);
      if (keyList.length > 0) {
        getPointDataList(keyList);
        subMsg(keyList);
      }
    }, [initTaggList]);
    //每次机组初始化时接口获取一次最新数据,防止socket获取不到最新数据
    function getPointDataList(keyList: string[]): void {
      pointDataList({ keyList }).then((res) => {
        if (res.code === '1' && res.data) {
          const list = initTaggList.map((item) => {
            item.celsius = res.data[Number(item.pointCode)];
            return item;
          });
          setTaggList(list);
        }
      });
    }
    //获取标注点
    function getTaggList(): void {
      //只展示前三组数据
      const list = imgData.filter((item, index) => item && item.id && index <= 3);
      setImgList(list);
      if (list.length > 0) {
        //请求数组
        const fetchList = list.map((item) => baseUnitTree<TreeOptions[]>({ parentId: item.id }));
        Promise.allSettled(fetchList).then((res) => {
          //过滤出请求成功的数据
          let fulList: any[] = res.filter((item) => item.status === 'fulfilled');
          const taggLists: TreeOptions[] = [];
          fulList.forEach((item) => {
            if (item.value.data) {
              taggLists.push(...item.value.data);
            }
          });
          setInitTaggList(taggLists);
        });
      }
    }
    //取消订阅
    function unsubMsg(pointCodes: string[]): void {
      const ids = pointCodes.join(',');
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids,
          userId,
          topicType: leftSourceHead,
        }),
        destination: unsubTheme,
        headers: {
          Authorization: rsaEncrypt(unsubTheme).toString(),
        },
      };
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
    //消息返回
    function messageReturn(soucre: string) {
      if (socketClient) {
        socketClient.subscribe(soucre, function (message: any) {
          const recv = message.body;
          setSokcetInit(true);
          if (isJsonString(recv)) {
            getTaggCelsiusData(JSON.parse(recv));
          }
        });
      }
    }
    //发送订阅
    function subMsg(pointCodes: string[]) {
      const ids = pointCodes.join(',');
      //消息体
      const msgBody = {
        //body只接受字符串数据
        body: JSON.stringify({
          pointCodes: ids,
          userId,
          topicType: leftSourceHead,
        }),
        destination: subTheme,
        headers: {
          Authorization: rsaEncrypt(subTheme).toString(),
        },
      };
      // 接受返回消息
      socketInit === false && messageReturn(leftSourceHead + userId);
      // 发送消息
      socketClient && socketClient.publish(msgBody);
    }
    //获取标点温度
    function getTaggCelsiusData(socketData: any): void {
      if (socketData) {
        const { key, value } = socketData;
        const code = new RegExp(/\d+/g).exec(key);
        if (code) {
          const list = initTaggList.map((item) => {
            if (item.pointCode === code[0]) {
              item.celsius = value;
            }
            return item;
          });
          setTaggList(list);
        }
      }
    }
    //空数据占位模板
    function emptyComponent(): ReactNode {
      return (
        <div>
          <Empty description={<span>暂无设备图片</span>} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }
    //点击打开弹框处理
    function openModalHandler(list: TreeOptions[], title: string, units: string): void {
      const data = list.map((item) => {
        return {
          id: item.pointCode,
          name: item.pointName,
        };
      });
      openModal(data, title, units);
    }
    //获取图片所有测点code 只获取温度
    function getImgCode(data: TreeOptions): void {
      if (taggList.length > 0) {
        const list = taggList.filter((item) => item.parentId === data.id);
        const temperaturetileData = list.filter((item) => item.name.includes('温度'));
        openModalHandler(temperaturetileData, data.name, '℃');
      }
    }
    //图片列表模板展示
    function imgListComponent(): ReactNode {
      return imgList.map((item) => (
        <div className={styles['img-list-item']} key={item.id}>
          <img
            onClick={() => getImgCode(item)}
            src={urlPrefix + '/Attachment/downloadAttachment/' + item.imgId}
          />
          {taggComponent(item.id)}
        </div>
      ));
    }
    //内容展示模板
    function containerShowComponent(): ReactNode {
      if (loading) {
        return <></>;
      } else if (imgList.length > 0) {
        return <>{imgListComponent()}</>;
      } else {
        return emptyComponent();
      }
    }
    //标点颜色处理
    function taggColorHandler(index: number, lastIndex: number): ReactNode {
      switch (index) {
        case 0:
          return <RedImg />;
        case 1:
          return <CurPosImg />;
        case 2:
          return <GreenImg />;
        case lastIndex:
          return <BlueImg />;
        default:
          return <PosImg />;
      }
    }
    //最高最低温度数值处理
    function celsiusMaxMinNumberHandler(
      value: number | undefined,
      status: string,
      tf: boolean,
    ): ReactNode {
      if (tf && status === 'max') {
        return <></>;
      } else if (status === 'max' || status === 'min') {
        return <span className={`${styles.celsius} ${styles[status]}`}>{value}</span>;
      } else {
        return <></>;
      }
    }
    //标注点模板
    function taggComponent(parentId: string): ReactNode {
      const fileList = taggList.filter((item) => item.parentId === parentId);
      //不包含油槽
      const otherList = fileList.filter((item) => !item.name.includes('油槽'));
      //油槽
      const oilGrooveData = fileList.find((item) => item.name.includes('油槽'));
      //温度降序排序后的列表
      const list: TreeOptions[] = listOrder(otherList, 'celsius');
      const nodeList = list.map((item, index: number) => {
        const status = isMaxOrMin(list, item, 'celsius');
        return (
          <div key={item.id}>
            {celsiusMaxMinNumberHandler(item.celsius, status, Boolean(oilGrooveData))}
            <Tooltip placement="top" title={item.name}>
              <div
                onClick={() => openModalHandler([item], item.pointName, '℃')}
                style={{
                  left: item.xcoordinate - 10,
                  top: item.ycoordinate - 12,
                }}
                className={styles.tagg}
              >
                {taggColorHandler(index, list.length - 1)}
              </div>
            </Tooltip>
          </div>
        );
      });
      let maxCelsiusDiff = 0;
      if (otherList.length > 0) {
        //最大温差
        maxCelsiusDiff =
          (otherList[0].celsius || 0) - (otherList[otherList.length - 1].celsius || 0);
      }
      const celsiusDiffNode = (
        <span key={Math.random()} className={`${styles.celsius} ${styles.diff}`}>
          {maxCelsiusDiff.toFixed(1)}
        </span>
      );
      //油槽点位
      const oilGrooveNode = oilGrooveData ? (
        <Tooltip key={oilGrooveData.pointCode} placement="top" title={oilGrooveData.name}>
          <div
            onClick={() => openModalHandler([oilGrooveData], oilGrooveData.pointName, 'mm')}
            style={{
              left: oilGrooveData.xcoordinate - 10,
              top: oilGrooveData.ycoordinate - 12,
            }}
            className={styles.tagg}
          >
            <PosImg />
          </div>
        </Tooltip>
      ) : null;
      return [celsiusDiffNode, oilGrooveNode, ...nodeList];
    }
    return (
      <ModalComponent direction="left">
        <div className={styles['left-equip-info-container']}>
          <Spin spinning={loading} size="large">
            {containerShowComponent()}
          </Spin>
        </div>
      </ModalComponent>
    );
  },
);
