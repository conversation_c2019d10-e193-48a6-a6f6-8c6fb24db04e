.textOverflow {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.flexNormal {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.comparison-points-card-container {
  height: 100%;
  width: 100%;
  :global {
    .ant-card-bordered {
      border: 1px solid #1a448d;
      width: 400px;
      height: 100%;
    }
    .ant-card {
      background-color: transparent;
    }
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #1a448d;
    }
    .ant-card-body {
      overflow-y: scroll;
      // scrollbar-gutter: stable; // 稳定滚动条
      padding: 12px;
      height: calc(100% - 65px);
    }
  }
}

.column-content-item {
  padding: 8px;
  border-bottom: 1px solid #1a448d;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  border-radius: 6px;
  .textOverflow();
  &.active,
  &:hover {
    background: #2b69b2;
  }
}

.point-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 12px 0;
  .point-info-item {
    .flexNormal();
  }
  .point-info-item-label {
    color: #8fd4ff;
    font-size: 14px;
    font-weight: bold;
  }
  .point-info-item-value {
    color: #fff;
    font-size: 14px;
    text-align: left;
  }
}
