import React, { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import { useSetState } from 'ahooks';
import styles from './point-chart.page.less';
// interfaces
import { ISearchFormConfig, EnumElement } from '@/components/form-search/type';
import { PointAnalysis } from '@/pages/point/type';
// funcs
import _ from 'lodash';
import { handleShareToOther, handleAdd } from './server';
import {
  getPointAnalysisList,
  getMyLegendList,
  getShareAndFavoriteList,
} from '@/pages/point/point.service';
// components
import { Tabs, message, Pagination, type PaginationProps, type TabsProps } from 'antd';
import { PlusOutlined, SearchOutlined, RedoOutlined } from '@ant-design/icons';
import LegendList from './components/legend-list/legend.list.component';
import UserSelector from '@/components/user-selector/user.selector.component';
import NewPointModal from './components/add-legend/modal.component';
import SearchForm from '@/components/form-search/search.component';

interface LegendData {
  list: PointAnalysis.List[];
  total: number;
}
export interface LegendTab {
  myLegends: LegendData;
  myShares: LegendData;
  myCollections: LegendData;
  sharesToMe: LegendData;
  legendCenter: LegendData;
}
type LegendTabKey = keyof LegendTab;
export default function MeasurePointAnalysis() {
  const [loading, setLoading] = useState<boolean>(false);
  const searchFormRef = useRef<React.ElementRef<typeof SearchForm>>(null); // 顶部搜索组件
  const addLegendModalRef = useRef<React.ElementRef<typeof NewPointModal>>(null); // 新增弹窗组件
  const userSelectorRef = useRef<React.ElementRef<typeof UserSelector>>(null); // 用户选择组件（点击分享）
  const [currentTab, setCurrentTab] = useState<LegendTabKey>('myLegends'); // 当前 tab
  const [shareItem, setShareItem] = useState<string>(''); // 分享 item
  const [userVisible, setUserVisible] = useState<boolean>(false); // 用户选择组件是否显示
  const [legends, setLegends] = useSetState<LegendTab>({
    myLegends: { list: [], total: 0 }, // 我的图例
    myShares: { list: [], total: 0 }, // 我的分享
    myCollections: { list: [], total: 0 }, // 我的收藏
    sharesToMe: { list: [], total: 0 }, // 分享给我
    legendCenter: { list: [], total: 0 }, // 图例中心
  });
  const tabTotal = useMemo(() => legends[currentTab].total, [legends, currentTab]);
  const [pagination, setPagination] = useState<PaginationProps>({
    current: 1,
    pageSize: 8,
  });

  // 挂载完成
  useEffect(() => {
    getList();
  }, [currentTab]);

  // 更新图例数据
  const updateLegends = (key: LegendTabKey, data: LegendData) => {
    // @ts-ignore
    setLegends({
      [key]: {
        list: data.list,
        total: Number(data.total),
      },
    });
  };

  // 获取数据
  const getList = async (
    params: Partial<PointAnalysis.Query> = {
      pageNum: 1,
      pageSize: 8,
    },
  ) => {
    setLoading(true);
    try {
      let result = null;
      switch (currentTab) {
        case 'myLegends':
          result = await getMyLegendList(params);
          updateLegends('myLegends', result.data);
          break;
        case 'myShares':
          result = await getShareAndFavoriteList({ ...params, myShare: 1 });
          updateLegends('myShares', result.data);
          break;
        case 'myCollections':
          result = await getShareAndFavoriteList({ ...params, myFavorites: 1 });
          updateLegends('myCollections', result.data);
          break;
        case 'sharesToMe':
          result = await getShareAndFavoriteList({ ...params, shareWithMe: 1 });
          updateLegends('sharesToMe', result.data);
          break;
        case 'legendCenter':
          result = await getPointAnalysisList(params);
          updateLegends('legendCenter', result.data);
          break;
      }
      if (result) {
        setPagination((prev) => ({
          ...prev,
          current: result.data.pageNum,
          pageSize: result.data.pageSize,
        }));
      }
    } catch (error) {
      message.error('获取列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 顶部查询表单配置
  const searchFormConfig = useMemo<ISearchFormConfig>(
    () => ({
      layout: 'inline', // 表单排版
      initialValues: {}, // 初始值
      // 组件
      elements: [
        {
          key: 'add',
          type: EnumElement.BUTTON,
          label: '新增',
          onClick: () => addLegendModalRef.current?.openModal(),
          props: {
            icon: <PlusOutlined />,
          },
        },
        {
          key: 'name',
          type: EnumElement.INPUT,
          label: '图例名称',
          field: 'name',
          props: {
            placeholder: '请输入图例名称',
          },
        },
        {
          key: 'ok',
          type: EnumElement.BUTTON,
          label: '查询',
          onClick: () => handleQuery(),
          props: {
            icon: <SearchOutlined />,
          },
        },
        {
          key: 'reset',
          type: EnumElement.BUTTON,
          label: '重置',
          onClick: () => handleReset(),
          props: {
            icon: <RedoOutlined />,
          },
        },
      ],
    }),
    [],
  );

  // 查询
  const handleQuery = useCallback(
    _.debounce(async () => {
      const query = searchFormRef.current?.baseFormInstance?.getFieldsValue();
      await getList({ pageNum: 1, pageSize: 8, ...query });
    }, 300),
    [currentTab],
  );

  // 重置
  const handleReset = useCallback(
    _.debounce(async () => {
      searchFormRef.current?.baseFormInstance?.resetFields();
      await getList();
    }, 300),
    [],
  );

  // 打开用户选择组件
  const handleOpenUserSelector = useCallback((id: string) => {
    setShareItem(id); // 缓存分享的业务 item
    setUserVisible(true);
    setTimeout(() => {
      userSelectorRef.current?.openUserSelector();
    });
  }, []);

  // 分享请求
  const handleShareRequest = useCallback(
    async (noticerIdList: string[]) => {
      const params = {
        noticerIdList,
        optionId: shareItem,
      };
      await handleShareToOther(params).then(async () => {
        await getList();
      });
    },
    [shareItem],
  );

  // 处理分页变化
  const onChange: PaginationProps['onChange'] = async (pageNumber, pageSize) => {
    await getList({ pageNum: pageNumber, pageSize });
  };

  // 页签配置
  const tabItems: TabsProps['items'] = useMemo(() => {
    const tabs: Array<{
      key: LegendTabKey;
      label: string;
    }> = [
      { key: 'myLegends', label: '我的图例' },
      { key: 'myShares', label: '我的分享' },
      { key: 'myCollections', label: '我的收藏' },
      { key: 'sharesToMe', label: '分享给我' },
      { key: 'legendCenter', label: '图例中心' },
    ];
    return tabs.map(({ key, label }) => ({
      key,
      label,
      children: (
        <LegendList
          loading={loading} // 加载中
          list={legends[key].list} // 列表数据
          handleOpenUserSelector={handleOpenUserSelector}
          currentTab={currentTab}
          getList={getList}
        />
      ),
    }));
  }, [loading, legends, pagination]);

  // 新增确定
  const handleModalFinish = useCallback(
    async (params: PointAnalysis.Add) => {
      await handleAdd(params, () => getList());
    },
    [getList],
  );

  return (
    <>
      <section className={styles.pointChart}>
        <header className={styles.pointChart_header}>
          <SearchForm ref={searchFormRef} config={searchFormConfig} />
        </header>
        <main className={styles.pointChart_main}>
          <Tabs
            items={tabItems}
            onChange={(key) => setCurrentTab(key as LegendTabKey)}
            activeKey={currentTab}
            tabPosition="top" // 设置为顶部横向排列
            style={{ width: '100%' }}
          />
          {tabTotal ? (
            <div className={styles.pagination}>
              <Pagination
                align="end"
                current={pagination.current} // 当前页码
                pageSize={pagination.pageSize} // 每页条数
                total={tabTotal} // 总条数
                onChange={onChange} // 页码变化
                showQuickJumper // 显示跳转
                pageSizeOptions={[8, 16, 32, 64]} // 每页条数选项
                showTotal={(total) => `共 ${total} 条`} // 显示总条数
              />
            </div>
          ) : null}
        </main>
      </section>
      {userVisible ? <UserSelector okCallback={handleShareRequest} ref={userSelectorRef} /> : null}
      <NewPointModal ref={addLegendModalRef} callback={handleModalFinish} />
    </>
  );
}
