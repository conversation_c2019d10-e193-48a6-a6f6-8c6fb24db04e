import React, { useRef, useState, useCallback, useMemo } from 'react';
import styles from './point-history.content.less';
import { DatePicker, Form, Button, message, Table, Segmented } from 'antd';
import { ColumnType } from 'antd/lib/table';
import dayjs from 'dayjs';
import {
  getHistoryPointList,
  getOriginPointList,
  getPointListHistoryData,
  exportHistoryData,
} from '@/pages/point/point.service';
import Empty from '@/components/empty-component/empty-component';
import LegendListModal from '@/components/modal-point-list/mpl.component';
import { Pointlist, CommonPointType } from '@/pages/point/type';
import GTwoLine from '@/components/multiple-chart/line.component';
import GTwoBar from '@/components/multiple-chart/bar.component';
import { getHalfHourTime } from '@/utils/date';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import ComparisonPoints from './history-riside/comparison.points.component';
const { RangePicker } = DatePicker;
// import FluxInsertPanel from '@/pages/point/insert.mock.history';
import { PointType } from '@/pages/point/enum';
import { showLoading, hideLoading } from '@/utils/global-loading';
import { useMount } from 'ahooks';
import { disabledDate, disabledTime } from '@/utils/date';
import { useSelector } from 'umi';
interface Iprops {
  hasHistory?: boolean;
  details: Pointlist.Details;
}
interface IChartData {
  time: string;
  value: number;
  type: string;
  pointCode: string;
  tableId: string;
  pointType: CommonPointType;
}
enum SegmentedType {
  TABLE = '表格',
  CHART = '折线',
  BAR = '柱状',
}
const SegmentedOptions = [
  { label: '折线', value: SegmentedType.CHART },
  { label: '柱状', value: SegmentedType.BAR },
  { label: '表格', value: SegmentedType.TABLE },
];
type IPointList = Array<Pointlist.List | Pointlist.Details>;
const PointHistory: React.FC<Iprops> = ({ hasHistory, details }) => {
  const [forms] = Form.useForm();
  const devicesList = useSelector((state: any) => state.point.devices || []);
  const modalRef = useRef<React.ElementRef<typeof LegendListModal>>(null);
  const [chartData, setChartData] = useState<Array<IChartData>>([]);
  const [pointList, setPointList] = useState<IPointList>([]);
  const [segmented, setSegmented] = useState<SegmentedType>(SegmentedType.CHART);
  const isYaoxin = useMemo(() => details?.pointType === PointType.STATE, [details]);

  // 重置和页面初始化的测点
  const defaultPointList = useMemo(() => {
    const type = devicesList?.find((el: any) => el.dictCode === details?.pointType)?.dictName;
    return [
      {
        ...details,
        pointType: type as CommonPointType, // 转换中文
        entryTime: details?.entryTime ? dayjs(details.entryTime).format('YYYY-MM-DD HH:mm:ss') : '',
      },
    ];
  }, [details?.entryTime]);

  // 获取时间段
  const getTimeRange = () => {
    const formValues = forms.getFieldsValue();
    return [
      dayjs(formValues?.date[0])?.format('YYYY-MM-DD HH:mm:ss'),
      dayjs(formValues?.date[1])?.format('YYYY-MM-DD HH:mm:ss'),
    ];
  };

  useMount(() => {
    if (hasHistory) {
      init();
    } else {
      message.info('未同步无数据');
    }
  });

  const columns: ColumnType<(typeof tableData)[number]>[] = [
    {
      title: '测点名称',
      dataIndex: 'type',
      align: 'center',
      width: '35%',
    },
    {
      title: '测点编码',
      dataIndex: 'pointCode',
      align: 'center',
      width: '25%',
    },
    {
      title: '测点值',
      dataIndex: 'value',
      align: 'center',
      width: '10%',
    },
    {
      title: '测点类型',
      dataIndex: 'pointType',
      align: 'center',
      width: '10%',
    },
    {
      title: '时间',
      dataIndex: 'time',
      align: 'center',
      width: '20%',
    },
  ];

  const tableData = useMemo(() => {
    const groupedData = _.groupBy(chartData, 'pointCode'); // 按 pointCode分组
    const tableData = Object.keys(groupedData).map((pointCode) => {
      const group = groupedData[pointCode];
      const firstItem = group[0];
      return {
        key: pointCode,
        pointCode: firstItem.pointCode,
        type: firstItem.type,
        value: group.length > 0 ? `${group.length}条记录` : '-',
        time: '-',
        pointType: firstItem.pointType,
        children: group.map((item) => ({
          ...item,
          key: item.tableId,
        })),
      };
    });
    return tableData;
  }, [chartData]);

  // 组装数据
  const assembleData = (
    response: Global.Response<Record<string, { time: string; value: number }>>,
  ) => {
    const { code, data } = response;
    if (_.isEqual(code, '0')) {
      message.info('获取数据失败，请稍后再试');
      return;
    }
    if (_.isEmpty(data) || _.isNull(data) || _.isUndefined(data)) {
      setChartData([]);
      return;
    }
    const sub = [] as Array<IChartData>;
    Object.entries(data).forEach(([key, values]) => {
      if (Array.isArray(values)) {
        sub.push(
          ...values.map((item: { time: string; value: number }) => ({
            time: item.time,
            value: item.value,
            type: details?.pointName as string,
            pointCode: key,
            tableId: uuidv4(),
            pointType: details?.pointType as CommonPointType,
          })),
        );
      }
    });
    setChartData(sub);
  };

  // 获取 - 遥测 历史数据
  const getAnalogList = async () => {
    const [selectBegin, selectEnd] = getTimeRange();
    const params = {
      selectBegin,
      selectEnd,
      keyList: [details.pointCode],
    };
    showLoading();
    const response = await getHistoryPointList(params);
    hideLoading();
    assembleData(response);
  };

  // 获取 - 遥信 历史数据
  const getStateList = async () => {
    const [selectBegin, selectEnd] = getTimeRange();
    const params = {
      selectBegin,
      selectEnd,
      key: details.pointCode,
    };
    showLoading();
    const response = await getOriginPointList(params);
    hideLoading();
    assembleData(response);
  };

  // 获取多个历史记录
  const getHistory = async (rows: Pointlist.List[]) => {
    const [selectBegin, selectEnd] = getTimeRange();
    const params = {
      basePointList: rows,
      selectBegin,
      selectEnd,
    };
    showLoading();
    const response = await getPointListHistoryData(params);
    const { code, data } = response;
    if (_.isEqual(code, '0')) {
      message.info(response.message || '获取数据失败，请稍后再试');
      hideLoading();
      return;
    }
    if (_.isEmpty(data) || _.isNull(data) || _.isUndefined(data)) {
      hideLoading();
      setChartData([]);
      return;
    }
    hideLoading();
    setChartData(
      data
        ?.map((point) => {
          return (
            point.historyDatas?.map((__) => ({
              time: __.time,
              value: __.value,
              type: point.pointName,
              pointCode: point.pointCode,
              tableId: uuidv4(),
              pointType: point.pointType as CommonPointType,
            })) || []
          );
        })
        .flat() || [],
    );
  };

  // 初始化
  const init = useCallback(async () => {
    const [startTime, endTime] = getHalfHourTime(); // 获取当前半小时的时间
    forms.setFieldsValue({ date: [dayjs(startTime), dayjs(endTime)] }); // 设置日期
    setPointList(defaultPointList); // 初始化测点
    isYaoxin ? getStateList() : getAnalogList(); // 获取遥信或遥测数据
  }, [defaultPointList]);

  // 查询
  const handleSearch = useCallback(async () => {
    if (_.size(pointList) > 1) {
      await getHistory(pointList as Pointlist.List[]);
    } else {
      isYaoxin ? getStateList() : getAnalogList();
    }
  }, [pointList, isYaoxin]);

  // 重置
  const handleReset = useCallback(async () => {
    forms.resetFields();
    await init();
  }, []);
  // 导出
  const handleExport = useCallback(async () => {
    if (_.size(tableData) === 0) {
      message.info('请确认至少一个测点有数据');
      return;
    }
    const [selectBegin, selectEnd] = getTimeRange();
    const params = {
      selectBegin,
      selectEnd,
      basePointList: pointList,
    };
    showLoading();
    await exportHistoryData(params);
    hideLoading();
  }, [pointList, tableData]);

  // 新增测点
  const handleChoosePoint = useCallback(
    async (rows: Pointlist.List[]) => {
      // 判断是否选择了基准测点（该测点）
      const isPickDefault = rows.some((item) => item.pointCode === details.pointCode);
      const newPoint = isPickDefault ? [...rows] : [...defaultPointList, ...rows];
      setPointList(newPoint);
      await getHistory(newPoint);
    },
    [defaultPointList],
  );

  // 删除测点
  const handleRemovePoint = useCallback(
    (delPoint: (typeof pointList)[number]) => {
      if (delPoint.pointCode === details.pointCode) {
        message.info('不能删除当前基准测点');
        return;
      }
      setPointList(pointList.filter((h) => h.pointCode !== delPoint.pointCode));
      setChartData(chartData.filter((h) => h.pointCode !== delPoint.pointCode));
    },
    [pointList, chartData, details],
  );

  // 渲染数据展示形式
  const renderData = () => {
    switch (segmented) {
      case SegmentedType.TABLE:
        return (
          <div className={styles['table-container']}>
            <Table
              dataSource={tableData}
              columns={columns}
              rowKey={(record) => record.key}
              size="small"
              pagination={false}
            />
          </div>
        );
      case SegmentedType.CHART:
        return (
          <GTwoLine
            data={chartData}
            options={{
              appendPadding: [10, 20, 10, 10],
            }}
            chartType={details?.pointType} // 折线图的平滑度，遥信使用梯形折线图
          />
        );
      case SegmentedType.BAR:
        return <GTwoBar data={chartData} />;
      default:
        return null;
    }
  };

  if (!hasHistory) return <Empty />;
  return (
    <>
      <div className={styles['modal-container']}>
        <div className={styles['container']}>
          <div className={styles['left-column']}>
            <div className={styles['left-top']}>
              <Form
                form={forms}
                layout="inline"
                initialValues={{ layout: 'inline' }}
                style={{ maxWidth: 'none' }}
              >
                <Form.Item label="日期" name="date">
                  <RangePicker
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                    disabledDate={disabledDate}
                    disabledTime={disabledTime}
                  />
                </Form.Item>
                <Form.Item>
                  <Button onClick={handleSearch}>查询</Button>
                </Form.Item>
                <Form.Item>
                  <Button onClick={handleReset}>重置</Button>
                </Form.Item>
                <Form.Item>
                  <Button onClick={handleExport}>导出</Button>
                </Form.Item>
              </Form>
              <div className={styles['use']}>
                {/*
                别删，因为只有现场有数据，用来插入数据用的。
           */}
                {/* <FluxInsertPanel /> */}
                <Segmented options={SegmentedOptions} value={segmented} onChange={setSegmented} />
              </div>
            </div>
            <div className={styles['left-bottom']}>{renderData()}</div>
          </div>
          <div className={styles['right-column']}>
            <ComparisonPoints
              handleSelectPoint={() => modalRef.current?.openModal('create')}
              handleRemovePoint={handleRemovePoint}
              list={pointList as Pointlist.List[]}
            />
          </div>
        </div>
      </div>
      <LegendListModal
        ref={modalRef}
        callback={(rows: Pointlist.List[]) => handleChoosePoint(rows)} // 选择测点回调
      />
    </>
  );
};

export default PointHistory;
