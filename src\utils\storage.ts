/**
 * <AUTHOR>
 * @description 本地存储工具类
 */
export default {
  // 设置
  set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value));
  },
  // 获取
  get(key: string) {
    const value = localStorage.getItem(key);
    if (!value) return '';
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  },
  // 删除
  remove(key: string) {
    localStorage.removeItem(key);
  },
  // 清空
  clear() {
    localStorage.clear();
  },
};
