/* antd modal 样式 */
.modal {
  :global {
    .ant-modal-content {
      border-radius: 4px;
      border: 1px solid #00ffea91;
    }

    .ant-modal-close {
      top: 11px;
    }

    .ant-modal-close-x {
      width: auto;
      height: auto;
      color: #bbbaba;
    }

    .ant-modal-close:hover .ant-modal-close-x {
      color: #fff;
    }

    .ant-modal-header {
      border: none;
      border-radius: 2px;
    }

    .ant-modal-body {
      padding: 30px 40px 0px 20px;
    }

    .ant-modal-footer {
      border: none;
      text-align: center;

      .ant-btn-default {
        width: 80px;
        height: 31px;
        border-radius: 2px;
        line-height: 0px;
        background: rgba(255, 255, 255, 0);
        color: #8fd4ff;
        box-shadow: inset 0px 0px 5px 2px #00ffeb91;
      }

      .ant-btn-default:hover {
        box-shadow: inset 0px 0px 5px 2px #00ffeae0;
      }

      .ant-btn-primary {
        width: 80px;
        height: 31px;
        border-radius: 2px;
        line-height: 0px;
        background: rgba(255, 255, 255, 0);
        box-shadow: inset 0px 0px 5px 2px #00ffeb91;
        border-color: #0a88af;
        color: #8fd4ff;
      }

      .ant-btn-loading {
        width: 90px;
        box-shadow: inset 0px 0px 5px 2px #00ffeae0;
        color: #33eeff;
        border-color: #33eeff;
      }

      .ant-btn:not(.ant-btn-icon-only) > .ant-btn-icon.ant-btn-loading-icon,
      .ant-btn:not(.ant-btn-icon-only) > .ant-btn-icon:not(:last-child) {
        margin-inline-end: 0px;
      }

      .ant-btn-primary:hover {
        box-shadow: inset 0px 0px 5px 2px #00ffeae0;
        color: #33eeff;
        border-color: #33eeff;
      }
    }
  }
}
