import React, { memo, useEffect, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import { Chart } from '@antv/g2';
import { v4 as uuidv4 } from 'uuid';

import { BarGraphWrapper } from './bar-graph.style';

interface IProps {
  children?: ReactNode;
  title: string;
}

const BarGraph: FC<IProps> = (props) => {
  const { title } = props;

  const idRef = useRef<string>(`chart_source_${uuidv4().slice(0, 8)}`);

  useEffect(() => {
    renderGraph();
  }, []);

  const renderGraph = () => {
    const chart = new Chart({ container: idRef.current });

    chart.options({
      type: 'interval',
      autoFit: true,
      data: [
        { name: '有功功率', 机组: '1号机', 发电功率: 28.9 },
        { name: '有功功率', 机组: '2号机', 发电功率: 18.8 },
        { name: '有功功率', 机组: '3号机', 发电功率: 39.3 },
        { name: '无功功率', 机组: '1号机', 发电功率: 12.4 },
        { name: '无功功率', 机组: '2号机', 发电功率: 33.2 },
        { name: '无功功率', 机组: '3号机', 发电功率: 24.5 },
      ],
      encode: { x: '机组', y: '发电功率', color: 'name' },
      axis: {
        y: {
          title: '(MW/MVar)',
          titleFill: '#fff',
          titlePosition: 'lc', // 标题相对坐标轴的位置
          labelFormatter: '~s',
          labelFill: '#fff',
          line: true, // 是否显示坐标轴线
          lineStroke: '#6CA1DD',
          lineExtension: [0, 0], // 轴线两侧的延长线
          grid: true, // 是否显示刻度线
          gridStroke: '#eee', // 纵轴网格线颜色设置为淡灰色
          gridLineWidth: 1, // 纵轴网格线宽度
          gridLineDash: [3, 3], // 纵轴网格线虚线样式
        },
        x: {
          title: null,
          labelFill: '#fff', // 设置横坐标文字颜色
          line: true,
          lineStroke: '#6CA1DD',
        },
      },
      legend: {
        color: {
          itemLabelFill: '#fff',
          layout: {
            justifyContent: 'flex-end',
            alignItems: 'center',
          },
        },
      },
      transform: [{ type: 'dodgeX' }],
      interaction: { elementHighlight: { background: true } },
    });

    chart.render();
  };

  return (
    <BarGraphWrapper>
      <div className="title">{title}</div>
      <div className="chart" id={idRef.current}></div>
    </BarGraphWrapper>
  );
};

export default memo(BarGraph);
