import React, { FC, useEffect, useRef, useState } from 'react';
import styles from './monitor.page.less';
import { Tabs } from 'antd';
import SummaryItem from './components/summary-item/summary-item.component';
import electricIcon from '@/assets/images/icon_electric.png';
import flowIcon from '@/assets/images/icon_flow.png';
import timeIcon from '@/assets/images/icon_time.png';
import waterlevelIcon from '@/assets/images/icon_waterlevel.png';
import SvgBox from './components/svg-box/svg-box.component';
import { getHeadshow } from './monitor.service';
import request from 'umi-request';
import moment from 'moment';
import { socketConnect } from '@/utils/socket';
import { connectUrl, sourceHead1, subTheme, unsubTheme } from './constant';
import { Client } from '@stomp/stompjs';
import { findDictTree } from '../point/point.service';
import { PointUnitDictionary } from '../point/type';
import { LineModal } from './components/line-modal/line-modal';
import { rsaEncrypt } from '@/utils/jsencrypt.util';
export interface SvgFormData {
  name: string;
  data: {
    alias: string;
    value: number;
  }[];
}
let hasSub = false;
export let socket: Client | null = null;
const MonitorPage: FC = () => {
  const pageRef = useRef<HTMLDivElement>(null);
  const [tabs, setTabs] = useState([
    { key: '1', label: '运行监视' },
    { key: '2', label: '1#机组单元监视' },
    { key: '3', label: '2#机组单元监视' },
    { key: '4', label: '3#机组单元监视' },
    { key: '5', label: '画面索引' },
  ]);
  const [activeTabName, setActiveTabName] = useState('运行监视');
  const [now, setNow] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [headData, setHeadData] = useState<any>(null);
  const headDataRef = useRef<any>({});
  const [svgFormData, setSvgFormData] = useState<SvgFormData[]>([]);
  const svgFormRef = useRef<any>({});
  const [socketClient, setSocketClient] = useState<Client | null>(null);
  const [pointInfoDict, setPointInfoDict] = useState<PointUnitDictionary[]>([]);
  const [modalTitle, setModalTitle] = useState('');
  const [open, setOpen] = useState(false);

  // #region 事件监听
  const onTabsChange = (key: any) => {
    setActiveTabName(tabs?.filter((item) => item.key === key)?.[0]?.label);
  };
  const onModalTitleChange = (title: string) => {
    setModalTitle(title);
  };
  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };
  const onHeadClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const text = (e.target as HTMLElement)?.textContent;
    if (!!text && text !== '-') {
      const dictInfo = pointInfoDict?.find((item: any) => item.dictName?.includes(text));
      console.log(text, dictInfo, 'onHeadClick============text,dictInfo====');
      if (dictInfo?.dictValue) {
        setModalTitle(`${dictInfo?.dictName}-${dictInfo?.dictValue}`);
        setOpen(true);
      }
    }
  };
  // #region 接口-顶部数据
  const getHeadshowData = (isInit: boolean) => {
    getHeadshow().then((res) => {
      if (res.code === '1') {
        const data = isInit
          ? res.data
          : {
              ...headDataRef.current,
              waterHead: res.data?.waterHead,
              totalFlow: res.data?.totalFlow,
            };
        setHeadData(data);
        headDataRef.current = data;
      }
    });
  };
  // #region 接口-获取点位信息字典
  const getMonitorPointInfo = () => {
    findDictTree('monitor_point_info').then((res) => {
      if (res.code === '1') {
        const list = res.data?.[0]?.children;
        setPointInfoDict(list);
      }
    });
  };
  // #region socket-连接处理
  function socketConnectHandler(): void {
    // 建立 WebSocket 连接并添加事件处理器
    socketConnect(connectUrl)
      .then((client) => {
        if (client) {
          setSocketClient(client);
          socket = client;
        }
      })
      .catch((error) => {
        console.log(error, 'socket-连接处理error==========');
      });
  }
  // #region socket-消息返回
  function messageReturn(soucre: string, client: Client | null = null) {
    console.log(client, 'client==========');

    if (client) {
      client.subscribe(soucre, function (message: any) {
        try {
          // 处理解析后的 JSON 数据
          const recv = JSON.parse(message.body);
          console.log('收到返回的消息(page):', recv);
          const dictInfo: any = pointInfoDict?.find((item: any) => item.dictValue === recv.key);
          // 顶部数据处理，字典中的memo有值的就是顶部数据（对应接口字段）
          const dictMemo = dictInfo?.memo;
          if (dictMemo) {
            const newHeadData = {
              ...headDataRef.current,
              [dictMemo]: parseFloat(recv.value.toFixed(2)),
            };
            headDataRef.current = newHeadData;
            setHeadData(newHeadData);
          } else {
            // 展示框数据处理
            if (dictInfo) {
              const name = dictInfo.dictName?.split('-')?.[0];
              const data = {
                alias: dictInfo.dictName?.split('-')?.[1],
                value: parseFloat(recv.value.toFixed(2)),
              };

              // 检查 svgFormRef 中是否已经存在该 name
              if (svgFormRef.current[name]) {
                // 如果存在，将新的 data 添加到数组中
                svgFormRef.current[name].data.push(data);
              } else {
                // 如果不存在，创建一个新的数组
                svgFormRef.current[name] = {
                  name: name,
                  data: [data],
                };
              }

              // 更新 svgFormData
              const newSvgFormData: SvgFormData[] = Object.values(svgFormRef.current);
              setSvgFormData(newSvgFormData);
            }
          }
        } catch (error) {
          console.log('收到返回的消息(page)不是有效的 JSON 格式:', message.body, error);
        }
      });
    }
  }
  // #region socket-发送消息
  function sendMsg(pointCodes: string, isSubscribe: boolean) {
    const userId = JSON.parse(localStorage.getItem('user') as string).id;
    //消息体
    const msgBody = {
      //body只接受字符串数据
      body: JSON.stringify({
        pointCodes,
        userId,
        topicType: sourceHead1,
      }),
      destination: isSubscribe ? subTheme : unsubTheme,
      headers: {
        Authorization: rsaEncrypt(isSubscribe ? subTheme : unsubTheme).toString(),
      },
    };
    console.log(`消息体(${isSubscribe ? '页面订阅' : '取消页面订阅'})：`, msgBody);

    // 接受返回消息
    !hasSub && messageReturn(sourceHead1 + userId, socketClient);
    hasSub = true;
    // 发送消息
    socketClient && socketClient.publish(msgBody);
  }

  // #region useEffect
  useEffect(() => {
    socketConnectHandler();
    getHeadshowData(true);
    getMonitorPointInfo();
    const timer = setInterval(() => {
      setNow(moment().format('YYYY-MM-DD HH:mm:ss'));
    }, 1000);
    const timer2 = setInterval(() => {
      getHeadshowData(false);
    }, 20000);
    const container = document.getElementById('micro_app_container');
    if (container) {
      container.style.overflow = 'hidden';
    }
    return () => {
      console.log('socketClient.deactivate');
      clearInterval(timer);
      clearInterval(timer2);
      // 关闭socket连接
      socket && socket.deactivate();
    };
  }, []);
  useEffect(() => {
    if (socketClient && pointInfoDict?.length) {
      const pointCodes = pointInfoDict
        ?.map((item: any) => item.dictValue)
        ?.filter((item: any) => !!item)
        ?.join(',');
      sendMsg(pointCodes, true);
    }
  }, [socketClient, pointInfoDict]);

  return (
    <div className={styles.monitorPage} id="monitorPage" ref={pageRef}>
      <div className={styles.head}>
        {/* <div className={styles.headLeft}>{`${now} 安全运行天数：${moment().diff('2024-06-19', 'days')}天`}</div> */}
        <div className={styles.headLeft}>{`${now}`}</div>
        <div className={styles.headCenter}>羊曲水电厂{activeTabName}</div>
        <div className={styles.headRight}>
          {/* <Tabs items={tabs} onChange={onTabsChange} /> */}
        </div>
      </div>
      <div className={styles.topContent} onClick={onHeadClick}>
        <SummaryItem title="坝前水位" icon={flowIcon} num={headData?.frontDam || null} unit={'m'} />
        <SummaryItem title="尾水水位" icon={flowIcon} num={headData?.endDam || null} unit={'m'} />
        <SummaryItem title="水头" icon={flowIcon} num={headData?.waterHead || null} unit={'m'} />
        <SummaryItem
          title="全厂总有功"
          icon={electricIcon}
          num={headData?.totalActive || null}
          unit={'MW'}
        />
        <SummaryItem
          title="全厂总无功"
          icon={timeIcon}
          num={headData?.totalReactive || null}
          unit={'Mvar'}
        />
        <SummaryItem
          title="机组总流量"
          icon={waterlevelIcon}
          num={headData?.totalFlow || null}
          unit={'m³/s'}
        />
      </div>
      <div className={styles.svgContent} id="svgContent">
        <SvgBox
          pageRef={pageRef}
          client={socketClient}
          pointInfoDict={pointInfoDict}
          svgFormData={svgFormData}
          onModalTitleChange={onModalTitleChange}
          onOpenChange={onOpenChange}
        />
      </div>
      <LineModal
        title={`${modalTitle.split('-')?.[0]} - ${modalTitle.split('-')?.[1]}`}
        close={() => {
          setOpen(false);
          setModalTitle('');
        }}
        open={open}
        client={socketClient}
        showName={modalTitle?.split('-')?.[0]}
        dataType={modalTitle?.split('-')?.[1]}
        pointCode={modalTitle?.split('-')?.[2]}
      />
    </div>
  );
};

export default MonitorPage;
