import type { ColumnsType } from 'antd/es/table';
export interface PropsOptions {
  scroll?: {
    x?: number;
    y?: number;
  };
  indexW?: number; //序号宽度
  isIndex?: boolean; //是否需要序号默认true
  loading?: boolean; //加载动画控制
  params?: ParamsOptions; //分页参数
  rowKey?: string; //列表key，默认id
  className?: string; //类名
  isVirtual?: boolean; //是否开启虚拟列表
  rowClassName?: string; //行类名
  columns: ColumnsType<any>; //列项配置
  tableList: any[]; //渲染列表数据
  rowSelection?: RowSelectionOptions;
  onTableRowClick?: (record: any) => void; //表格行点击方法
}

interface ParamsOptions {
  //分页配置
  total: number; //总数
  pageSize: number; //一页数量
  current: number; //页码
  pageChange: (page: number, pageSize: number) => void; //页码，数量改变方法
}
interface RowSelectionOptions {
  columnWidth?: string | number; //自定义列表选择框宽度
  selectedRowKeys?: any[]; //选中项数组
  onChange?: (newSelectedRowKeys: React.Key[], selectedRows: any[]) => void; //选中项改变方法
  getCheckboxProps?: (record: any) => { disabled: boolean }; //选择器属性配置
}
export interface ColumnsOptions {
  //表格Columns配置项
  title: string;
  dataIndex: string;
  align: string;
  ellipsis?: true;
  type?: string; //类型，默认text，file,text
  render?: (val: any, record: any, index: number) => React.ReactNode;
}
