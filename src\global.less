@lightBlue: #8fd4ff;
@hoverBlue: rgba(26, 68, 141, 0.5);
@tableHeadBlue: rgba(33, 122, 255, 0.2);
/** 按钮(或输入框)颜色 */
@buttonDefaltColor: #1577a5; // 默认
@buttonHoverColor: #409eff; // hover
@buttonActiveColor: #1890ff; // 选中
@buttonDisabledColor: #244984; // 禁选
@buttonDangerColor: #da360f; // 危险
/** 下拉节点、树节点颜色 */
@treeNodeActiveColor: #2b69b2; // 选中
@treeNodeHoverColor: #244984; // hover
@treeNodeDisabledColor: #9e9e9e; // 禁选
/** 下拉弹窗背景色 */
@dropdownBackground: #041c5f;

@font-face {
  font-family: Digital;
  src: url(~'/micro_operations/fonts/DJB-Get-Digital.ttf');
}

html,
body,
#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background: transparent;
  color: #fff;

  // .ant-notifiction-notice-with-icon {
  //   .ant-notifiction-notice-icon-error {
  //     background-color: #fff;

  //   }
  // }
  .ant-popconfirm .ant-popconfirm-message .ant-popconfirm-description {
    color: #fff;
  }

  // pro-component container tab header
  .ant-pro-page-container .ant-pro-page-container-warp-page-header {
    padding-block-start: 8px;
    padding-block-end: 0;
    padding-inline-start: 12px;
    padding-inline-end: 12px;
    border-bottom: 1px solid #2a497b;
  }

  .ant-select-outlined {
    &.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector {
      background-color: transparent;
      border-color: @buttonDisabledColor !important;
      color: rgba(255, 255, 255, 1);
    }
  }

  .ant-pro-query-filter.ant-pro-query-filter {
    padding: 24px 0;
  }

  .ant-pro-page-container-children-container {
    padding-inline: 24px;
  }

  .ant-col-offset-1,
  .ant-col-offset-2,
  .ant-col-offset-3,
  .ant-col-offset-4,
  .ant-col-offset-5,
  .ant-col-offset-6,
  .ant-col-offset-7,
  .ant-col-offset-8,
  .ant-col-offset-9,
  .ant-col-offset-10,
  .ant-col-offset-11 {
    margin-inline-start: 0;
  }

  .form .ant-col {
    width: initial !important;
  }

  .ant-switch {
    border: 1px solid #cccccc;
  }

  .ant-switch.ant-switch-checked:hover:not(.ant-switch-disabled) {
    background: rgba(0, 0, 0, 0.25);
  }

  .ant-switch-checked:focus {
    box-shadow: none;
  }

  .ant-switch.ant-switch-checked {
    border: 1px solid #1890ff;
  }

  .ant-switch.ant-switch-checked {
    background: rgba(0, 0, 0, 0.25);
  }

  .ant-switch.ant-switch-checked .ant-switch-handle::before {
    background: #1890ff;
  }

  .ant-switch.ant-switch-checked .ant-switch-loading-icon {
    color: #fff;
  }

  .ant-switch .ant-switch-handle::before {
    background: #cccccc;
  }

  .ant-modal {
    .ant-modal-close {
      width: auto;
      color: #fff;
      height: 40px;
      z-index: 900;

      &:hover {
        color: @buttonHoverColor;
        background: transparent;
      }
    }

    .ant-modal-close-x {
      width: 40px;
      height: 40px;
    }

    .ant-modal-header {
      color: rgb(255 255 255);
      background: #113f79;
      border-radius: 0 0 0 0;
      height: 40px;
    }

    .ant-modal-title {
      color: rgb(255 255 255);
      word-wrap: break-word;
      height: 100%;
      font-weight: 400;
      font-size: 18px;
      display: flex;
      padding-left: 12px;
      align-items: center;
      font-family: Alibaba PuHuiTi;
    }

    .ant-modal-content {
      background-color: @dropdownBackground;
      border-radius: 2px;
      border: 1px solid #409eff;
      padding: 11px;
      background: #091f4c;
      box-shadow: inset 0 0 8px 2px rgba(64, 158, 255, 0.5);
      position: relative;
      max-height: 800px;
      overflow-y: auto;
      overflow-x: hidden;

      .ant-select-selector {
        background-color: transparent;
        border-color: @buttonDefaltColor;
      }

      // focused antd Select
      .ant-select.ant-select-focused > .ant-select-selector {
        box-shadow: none !important;
      }
    }
  }

  .ant-modal-footer {
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
}

.ant-modal-confirm .ant-modal-confirm-title {
  color: rgba(255, 255, 255, 0.88);
}

.ant-modal-confirm .ant-modal-confirm-content {
  color: rgba(255, 255, 255, 0.88);
}

.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner:after {
  border-color: #1677ff;
}

.ant-picker-dropdown .ant-picker-panel-container {
  background: @dropdownBackground;

  .ant-picker-header {
    color: white;

    > button {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  .ant-picker-content th {
    color: rgba(255, 255, 2555, 0.88);
  }

  .ant-picker-cell {
    color: rgba(255, 255, 255, 0.25);
  }

  .ant-picker-cell-in-view {
    color: rgba(255, 255, 255, 0.88);
  }

  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(
      .ant-picker-cell-range-end
    ):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
    .ant-picker-cell-inner {
    background: #1677ff;
  }

  .ant-picker-cell-in-view.ant-picker-cell-in-range:not(.ant-picker-cell-disabled),
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-disabled),
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-disabled) {
    &::before {
      background: #1677ff;
    }
  }

  .ant-picker-time-panel-column
    > li.ant-picker-time-panel-cell-selected
    .ant-picker-time-panel-cell-inner {
    background-color: #1677ff;
  }

  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
    color: #fff;
  }
}

.ant-picker-ok {
  .ant-btn-primary {
    background: #1677ff !important;
    box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1) !important;
    color: #fff;

    &:hover {
      background: #1677ff !important;
      box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1) !important;
      color: #fff;
    }
  }
}

.ant-picker-range-arrow {
  padding: 0 !important;
  width: 0 !important;
  height: 0 !important;
  border-width: 10px;
  border-style: solid;
  border-color: transparent transparent rgb(255, 255, 255);
}

.ant-picker-outlined {
  background: transparent;
  border-color: @buttonDefaltColor;

  &.ant-picker-disabled {
    border-color: @buttonDisabledColor;
    color: #fff;

    &:hover,
    &:hover:not([disabled]) {
      box-shadow: 0 0 8px 1px @buttonDisabledColor;
      border-color: @buttonDisabledColor;
      background: transparent;
      color: #fff;
    }

    .ant-picker-suffix {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  &:focus {
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
    border-color: @buttonDefaltColor;
    background: transparent;
  }

  &:hover {
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
    border-color: @buttonDefaltColor;
    background: transparent;
  }

  &:focus-within {
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
    border-color: @buttonDefaltColor;
    background: transparent;
  }

  &.ant-picker-status-error:not(.ant-picker-disabled):focus-within {
    box-shadow: 0 0 8px 1px @buttonDangerColor;
    border-color: @buttonDangerColor;
    background: transparent !important;
  }

  &.ant-picker-status-error:not(.ant-picker-disabled):hover {
    box-shadow: 0 0 8px 1px @buttonDangerColor;
    border-color: @buttonDangerColor;
    background: transparent !important;
  }
}

.ant-btn-default:disabled {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDisabledColor;
  box-shadow: inset 0 0 8px 1px @buttonDisabledColor;

  &:hover {
    color: #fff;
    background-color: transparent;
    border: 1px solid @buttonDisabledColor;
    box-shadow: inset 0 0 8px 1px @buttonDisabledColor;
  }
}

// 上传附件按钮
.ant-btn-default:not(:disabled):not(.ant-btn-disabled) {
  color: #fff;
  border-color: @buttonDefaltColor;
  background: transparent;

  &:hover {
    color: #fff;
    box-shadow: inset 0 0 8px 1px @buttonHoverColor;
    border-color: @buttonHoverColor;
    background: transparent;
  }
}

.ant-btn-default:focus,
.ant-btn-default:active {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonActiveColor;
  box-shadow: inset 0 0 12px @buttonActiveColor;
}

// 日历hover
.ant-picker-dropdown
  .ant-picker-panel-container
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(
    .ant-picker-cell-range-end
  ):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
  .ant-picker-cell-inner {
  border: 1px solid @buttonHoverColor;
  background: transparent;
}

// 日历选中
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-selected:not(.ant-picker-cell-disabled)
  .ant-picker-cell-inner {
  border: 1px solid @buttonActiveColor;
  background: transparent;
}

// 日历当日
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
  border: 1px solid @buttonDefaltColor;
  background: transparent;
}

.anticon-close-circle {
  color: @buttonDefaltColor;
  background-color: #091f4c;
}

.ant-picker {
  color: #fff;

  .ant-picker-separator {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-picker-suffix {
    color: rgba(255, 255, 255, 0.45);
  }

  .ant-picker-input > input::placeholder {
    color: rgba(255, 255, 255, 0.25);
  }

  .ant-picker-input > input[disabled] {
    cursor: not-allowed !important;
    color: #fff;
  }

  .ant-picker-input-placeholder > input {
    color: #fff;
  }
}

/* antd Radio */
.ant-upload-list-picture-card .ant-upload-list-item-actions {
  margin-left: 50px;
}

.ant-radio-button-wrapper:first-child {
  border-left: 1px solid @buttonDefaltColor;
  border-inline-start: 1px solid @buttonDefaltColor;
}

.ant-radio-button-wrapper {
  color: #fff;
  background-color: transparent;
  border-color: @buttonDefaltColor;

  &:hover {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    color: #fff;
  }

  &:active {
    background-color: transparent;
    border-color: @buttonDefaltColor;
  }

  &:focus-within {
    background-color: transparent;
    border-color: @buttonDefaltColor;
  }

  &:focus {
    background-color: transparent;
    border-color: @buttonDefaltColor;
  }
}

.ant-radio-button-wrapper:not(:first-child)::before {
  background-color: transparent;
}

.ant-radio-group-solid {
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: inset 0 0 8px 1px @buttonDefaltColor;

    &:hover {
      background-color: transparent;
      border-color: @buttonDefaltColor;
      box-shadow: inset 0 0 8px 1px @buttonDefaltColor;
    }

    &:active {
      background-color: transparent;
      border-color: @buttonDefaltColor;
      box-shadow: inset 0 0 8px 1px @buttonDefaltColor;
    }

    &:focus-within {
      background-color: transparent;
      border-color: @buttonDefaltColor;
      box-shadow: inset 0 0 8px 1px @buttonDefaltColor;
    }

    &:focus {
      background-color: transparent;
      border-color: @buttonDefaltColor;
      box-shadow: inset 0 0 8px 1px @buttonDefaltColor;
    }
  }
}

/* antd Tabs */
.ant-tabs .ant-tabs-tab {
  color: rgba(255, 255, 255, 88%);
}

.anticon {
  color: #fff;
}

.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff;
}

.ant-tabs .ant-tabs-ink-bar {
  background: #03a2f0;
}

.ant-tabs-top > .ant-tabs-nav::before,
.ant-tabs-bottom > .ant-tabs-nav::before,
.ant-tabs-top > div > .ant-tabs-nav::before,
.ant-tabs-bottom > div > .ant-tabs-nav::before {
  border-bottom: 1px solid rgba(65, 174, 251, 0.3);
}

/* antd table 样式 */
.ant-table-wrapper
  .ant-table-tbody
  .ant-table-row.ant-table-row-selected
  > .ant-table-cell-row-hover {
  background: rgba(173, 255, 254, 20%);
}

.ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell {
  background: rgba(230, 255, 254, 10%);
}

.ant-table-wrapper .ant-table {
  color: #fff;
  background-color: transparent;
  border: 1px solid @tableHeadBlue;
  border-bottom: none;
  border-right: none;
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder {
  background-color: transparent;
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td {
  background: #1a448d;
}

.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(
    :last-child,
    .ant-table-selection-column,
    .ant-table-row-expand-icon-cell,
    [colspan]
  )::before {
  background-color: transparent !important;
}

// antd table表头单元格
.ant-table-wrapper .ant-table-thead > tr > th {
  background-color: rgba(15, 69, 163, 0.5);
  border-bottom-color: rgba(15, 69, 163, 0.5);
  color: @lightBlue;

  &.ant-table-cell-scrollbar {
    box-shadow: none;
  }
}

// antd table表体单元格
.ant-table-wrapper .ant-table .ant-table-tbody > tr > td {
  border-bottom-color: rgba(143, 212, 255, 0.1);
}

.ant-table-wrapper .ant-table-row-expand-icon {
  color: #0ae4ff;
  background-color: transparent;
  border-color: #0ae4ff;
}

.ant-empty-normal .ant-empty-description {
  color: @lightBlue;
}

.ant-table-wrapper .ant-table-tbody .ant-table-row > .ant-table-cell-row-hover {
  background: #1a448d //#fafafa00;;
}

/* antd form 样式 */
.ant-form-inline .ant-form-item {
  margin-bottom: 10px;
}

.ant-form-item .ant-form-item-label > label {
  color: @lightBlue !important;
}

.ant-form-item .ant-form-item-label > label,
.ant-typography {
  color: @lightBlue !important;
}

.ant-form-item.ant-form-item-vertical {
  width: 100%;
}

.ant-input-number .ant-input-number-input {
  color: #fff;
}

.ant-input-number-input-wrap {
  border-color: #d9dce2;
}

.ant-select-selector {
  background-color: transparent;
  border-color: @buttonDefaltColor;
  border-radius: 5px !important;
}

.ant-select:hover .ant-select-clear {
  background-color: #091f4c;
}

.ant-input {
  border-color: @buttonDefaltColor;
  border-radius: 2px;
}

.ant-input {
  color: #fff;

  &::placeholder {
    color: #bbb !important;
  }

  &:disabled {
    color: #fff;
    border-color: @buttonDisabledColor;
  }
}

.ant-input-group-wrapper-outlined .ant-input-group-addon {
  background: rgba(0, 0, 0, 0%);
  border: 1px solid @buttonDefaltColor;
}

.ant-input-group .ant-input-group-addon {
  color: rgba(255, 255, 255, 88%);
}

.ant-input-number-status-error:not(.ant-input-number-disabled):not(
    .ant-input-number-borderless
  ).ant-input-number {
  background: transparent;
  border: 1px solid @buttonDangerColor;

  &:hover {
    background: transparent;
    border: 1px solid @buttonDangerColor;
    box-shadow: 0 0 8px 1px @buttonDangerColor;
  }

  &:focus-within {
    background: transparent;
    border: 1px solid @buttonDangerColor;
    box-shadow: 0 0 8px 1px @buttonDangerColor;
  }

  &:focus {
    background: transparent;
    border: 1px solid @buttonDangerColor;
    box-shadow: 0 0 8px 1px @buttonDangerColor;
  }
}

.ant-input-number {
  color: #fff;
  width: 100%;

  .ant-input-number-handler-down-inner {
    color: #fff;
  }

  .ant-input-number-handler-up-inner {
    color: #fff;
  }
}

.ant-input-number-input::placeholder {
  color: #bbb !important;
}

.ant-input-number-outlined {
  background: transparent;
  border: 1px solid @buttonDefaltColor;

  .ant-input-number-handler-wrap {
    background: transparent;
  }

  &.ant-input-number-disabled {
    background-color: transparent;
    border-color: @buttonDisabledColor;
    box-shadow: 0 0 8px 1px @buttonDisabledColor;
    color: rgba(255, 255, 255, 1);

    &:hover {
      background-color: transparent;
      border-color: @buttonDisabledColor;
      box-shadow: 0 0 8px 1px @buttonDisabledColor;
      color: rgba(255, 255, 255, 1);
    }
  }

  &:hover {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }

  &:focus {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }

  &:focus-within {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }
}

.ant-input-outlined {
  background: transparent;
  border: 1px solid @buttonDefaltColor;

  &.ant-input-disabled {
    background-color: transparent;
    border-color: @buttonDisabledColor;
    box-shadow: 0 0 8px 1px @buttonDisabledColor;
    color: rgba(255, 255, 255, 1);

    &:hover {
      background-color: transparent;
      border-color: @buttonDisabledColor;
      box-shadow: 0 0 8px 1px @buttonDisabledColor;
      color: rgba(255, 255, 255, 1);
    }
  }

  &.ant-input-status-error:not(.ant-input-disabled) {
    background: transparent !important;
    border: 1px solid @buttonDangerColor;

    &:hover {
      background: transparent;
      border: 1px solid @buttonDangerColor;
      box-shadow: 0 0 8px 1px @buttonDangerColor;
    }

    &:focus-within {
      background: transparent;
      border: 1px solid @buttonDangerColor;
      box-shadow: 0 0 8px 1px @buttonDangerColor;
    }

    &:focus {
      background: transparent;
      border: 1px solid @buttonDangerColor;
      box-shadow: 0 0 8px 1px @buttonDangerColor;
    }
  }

  .ant-input {
    border-color: #0000;
    box-shadow: none;
  }

  &:hover {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }

  &:focus {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }

  &:focus-within {
    background-color: transparent;
    border-color: @buttonDefaltColor;
    box-shadow: 0 0 8px 1px @buttonDefaltColor;
  }
}

.ant-input-affix-wrapper {
  color: rgba(255, 255, 255, 0.88);
}

.ant-input-affix-wrapper .ant-input-clear-icon {
  color: rgba(255, 255, 255, 0.25);
}

.ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item-remove > .anticon {
  color: #fff;
}

.ant-select-item {
  color: #fff !important;
  border: 1px solid transparent !important;
}

.ant-select-single.ant-select-open .ant-select-selection-item {
  color: #fff !important;
}

.ant-select-item-option-active {
  background-color: @treeNodeHoverColor !important;
}

.ant-select-dropdown {
  background-color: rgb(4, 28, 95) !important;
}

.ant-select-tree-list {
  color: #fff !important;
  background-color: rgb(4, 28, 95) !important;
}

.ant-tree-select-dropdown
  .ant-select-tree
  .ant-select-tree-treenode-disabled
  .ant-select-tree-node-content-wrapper {
  color: gray !important;
}

// 树结构样式修改
.ant-tree {
  color: #fff !important;
  background: transparent !important;
}

.ant-tree-treenode {
  padding: 4px 0;
  border: 1px solid transparent;
  width: 100%;
}

.ant-tree-treenode:hover {
  border: 1px solid @treeNodeHoverColor;
  background-color: @treeNodeHoverColor;
  // box-shadow: inset 0 0 4px #1157b9;
}

.ant-tree-treenode-selected {
  border: 1px solid @treeNodeActiveColor;
  background-color: @treeNodeActiveColor;
  // box-shadow: inset 0 0 4px #1157b9;
}

.ant-tree-node-content-wrapper {
  width: 100%;
  background: transparent !important;
}

.ant-select-tree-list-holder::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.ant-select-tree-list-holder::-webkit-scrollbar-corner {
  background-color: rgb(4, 28, 95) !important;
}

.ant-select-item-option-selected,
.ant-select-tree-node-selected {
  background-color: @treeNodeActiveColor !important;
  border-color: @treeNodeActiveColor !important;
}

.ant-select-tree-node-content-wrapper {
  outline: none !important;
}

.ant-divider.ant-divider-horizontal {
  border-color: #1578a5;
}

.ant-btn-dashed[disabled] {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDisabledColor;
  box-shadow: inset 0 0 12px @buttonDisabledColor;

  &:hover {
    color: #fff;
    background-color: transparent;
    box-shadow: inset 0 0 12px @buttonDisabledColor;
    border: 1px solid @buttonDisabledColor;
  }
}

.ant-btn-dashed:not(:disabled):not(.ant-btn-disabled) {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDefaltColor;
  box-shadow: inset 0 0 12px @buttonDefaltColor;

  &:hover {
    color: #fff;
    background-color: transparent;
    box-shadow: inset 0 0 12px @buttonHoverColor;
    border: 1px solid @buttonHoverColor;
  }
}

.ant-btn-primary {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDefaltColor;
  box-shadow: inset 0 0 12px @buttonDefaltColor;
}

.ant-btn-primary:hover {
  color: #fff;
  background-color: transparent !important;
  box-shadow: inset 0 0 12px @buttonHoverColor;
  border: 1px solid @buttonHoverColor;
}

.ant-btn-primary:focus,
.ant-btn-primary:active {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonActiveColor;
  box-shadow: inset 0 0 12px @buttonActiveColor;
}

.ant-btn-primary:disabled {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDefaltColor;
  box-shadow: inset 0 0 12px @buttonDefaltColor;
  opacity: 0.7;

  &:hover {
    color: #fff;
    background-color: transparent;
    border: 1px solid @buttonDefaltColor;
    box-shadow: inset 0 0 12px @buttonDefaltColor;
    opacity: 0.7;
  }
}

.ant-btn-default {
  color: #fff;
  background-color: transparent;
  border: 1px solid @buttonDefaltColor;
  box-shadow: inset 0 0 12px @buttonDefaltColor;

  &:hover {
    color: #fff;
    background-color: transparent;
    border: 1px solid @buttonDefaltColor;
    box-shadow: inset 0 0 12px @buttonDefaltColor;
  }
}

.ant-btn-dangerous {
  border-color: @buttonDangerColor !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12) !important;
  box-shadow: 0 2px #0000000b !important;
}

/* 可排序列 hover及排序状态下 */
.ant-table-column-sort,
.ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:hover {
  background: transparent;
}

.ant-popover {
  .ant-popover-arrow:before {
    background-color: @dropdownBackground;
  }

  .ant-popover-inner {
    background-color: @dropdownBackground;

    .ant-popover-inner-content {
      color: #fff;
    }
  }
}

.ant-popconfirm .ant-popconfirm-message .ant-popconfirm-title {
  color: #fff;
}

.ant-select-selection-placeholder {
  color: #bbb !important;
}

.ant-pro-table .ant-table {
  border: 1px solid @tableHeadBlue;
  border-right: none;
}

/* 高级table样式 */
.ant-pro-table {
  .ant-pro-table-list-toolbar-title {
    color: #fff;
  }

  .ant-table-wrapper .ant-table-container table > thead > tr:first-child > * {
    &:first-child {
      border-start-start-radius: 2px;
    }

    &:last-child {
      border-start-end-radius: 2px;
    }
  }

  .ant-pro-card,
  .ant-table {
    color: #fff !important;
    background: transparent;
  }

  .ant-table {
    border: 1px solid @tableHeadBlue;
    border-bottom: none;
    border-right: none;
  }

  .ant-table-thead .ant-table-cell {
    color: @lightBlue;
    background: @tableHeadBlue;

    &::before {
      background-color: transparent !important;
    }
  }

  .ant-table-thead > tr > th,
  .ant-table-thead > tr > td {
    border-bottom: none !important;
  }

  .ant-table-tbody td {
    border-bottom: 1px solid @tableHeadBlue !important;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover,
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: @hoverBlue;
  }

  .ant-table-tbody > tr.ant-table-placeholder:hover > td {
    background: transparent !important;
  }

  .ant-pro-table-list-toolbar-setting-item {
    color: #fff !important;
  }

  .ant-empty-description {
    color: #fff !important;
  }

  tr.ant-table-expanded-row > td,
  tr.ant-table-expanded-row:hover > td {
    background: transparent;
  }

  .ant-table-row-expand-icon {
    background: transparent;
    border: 1px solid @lightBlue;
  }

  .ant-pro-field-index-column-border {
    background-color: transparent;
  }

  /* 滚动条样式 */
  .ant-table-cell-scrollbar {
    box-shadow: none !important;
  }

  .ant-table-body::-webkit-scrollbar-thumb {
    background: rgb(33 122 255 / 40%);
    border-radius: 0;
    box-shadow: 0 0 3px 0 rgb(177 237 255 / 45%);
    opacity: 0.88;
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(33 122 255 / 40%);
    border-radius: 0;
    box-shadow: 0 0 3px 0 rgb(177 237 255 / 45%);
    opacity: 0.88;
  }

  ::-webkit-scrollbar-track {
    background: rgb(33 122 255 / 40%);
    border-radius: 0;
    box-shadow: 0 0 3px 0 rgb(177 237 255 / 45%);
    opacity: 0.88;
  }

  ::-webkit-scrollbar {
    width: 4px;
  }

  .ant-table-body::-webkit-scrollbar {
    width: 4px;
  }

  .ant-pro-table-search-query-filter,
  .ant-pro-card-body {
    background: transparent !important;
  }

  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    background: transparent;
  }
}

/* 分页样式 */
.ant-pagination {
  color: #fff !important;

  .ant-pagination-item a,
  .anticon {
    color: #fff !important;
  }

  .ant-pagination-item-active {
    color: @lightBlue !important;
    background: transparent !important;
  }

  .ant-select-selector,
  .ant-pagination-options-quick-jumper input {
    color: #fff;
    background: transparent !important;
  }

  .ant-pagination-item,
  .ant-pagination-prev .ant-pagination-item-link,
  .ant-pagination-next .ant-pagination-item-link {
    background: transparent !important;
  }

  .ant-pagination-item-ellipsis {
    color: rgb(255 255 255 / 60%) !important;
  }
}

/* 解决empty table留白 */
.ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder:hover > td,
.ant-table-tbody > tr.ant-table-placeholder:hover > td {
  background: transparent !important;
}

/* 解决empty table高度跳动 */
.ant-empty-normal,
.ant-empty-normal:hover {
  line-height: inherit !important;
}

/* 上传组件样式 */
.ant-upload-list {
  color: @lightBlue;
}

.ant-dropdown {
  width: auto;

  .ant-dropdown-menu {
    background-color: @dropdownBackground;

    .ant-dropdown-menu-item {
      color: #fff;
    }

    .ant-dropdown-menu-item:hover {
      background-color: @treeNodeHoverColor;
    }

    .ant-dropdown-menu-item-disabled {
      color: @treeNodeDisabledColor;
    }

    .ant-dropdown-menu-item-disabled:hover {
      color: @treeNodeDisabledColor;
    }
  }
}

.ant-picker-range-arrow {
  padding: 0 !important;
  width: 0 !important;
  height: 0 !important;
  border-width: 10px;
  border-style: solid;
  border-color: transparent transparent rgb(255, 255, 255);
}

h2 {
  margin: 0;
}

html .ant-select-selector,
body .ant-select-selector,
#root .ant-select-selector {
  background: #ffffff00 !important;
  border: 1px solid @buttonDefaltColor !important;
  box-shadow: none !important;
  color: #fff !important;
}

.ant-pagination-options-quick-jumper {
  input {
    border: 1px solid #0572a1 !important;
  }
}

.ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table,
.ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table {
  border-top: none;
}

:where(.css-dev-only-do-not-override-wkcq1s).ant-table-wrapper .ant-table-cell-fix-left,
:where(.css-dev-only-do-not-override-wkcq1s).ant-table-wrapper .ant-table-cell-fix-right {
  background: rgba(38, 38, 38, 0);
}

.ant-checkbox-group-item {
  color: #8fd4ff !important;
}

.ant-table-tbody-virtual .ant-table-cell {
  border-bottom: 1px solid rgba(143, 212, 255, 0.1) !important;
}

.ant-table-tbody-virtual-scrollbar-thumb {
  background: #1a448d !important;
}

.ant-table-tbody-virtual-scrollbar-horizontal {
  .ant-table-tbody-virtual-scrollbar-thumb {
    background: transparent !important;
  }
}

// 抽屉
.ant-drawer-content {
  background-color: #091f4c !important;
}

.ant-drawer-title {
  color: #fff !important;
}

.report-form {
  .ant-select-selector,
  .ant-picker,
  .ant-input {
    border-radius: 3px !important;
  }
}

.ant-picker-status-error {
  background: transparent !important;
}

.gc-designer-resize-container .gc-designer-resize-container-vertical-handle {
  z-index: 1 !important;
}
