import { ExportOutlined, SettingOutlined, DeleteOutlined } from '@ant-design/icons';
import { Button, Flex } from 'antd';
import { useState, useCallback, useMemo, memo } from 'react';
import classNames from 'classnames';
import { ChartTemplateWrapper } from './analysis.template';
import { ITemplates } from '@/pages/point/point-chart/point-analysis.page';
import GTwoLine from '@/components/multiple-chart/line.component';
import { useAnalysis } from './useAnalysis';
import { SettingModal } from '../setting.modal/setting.modal.component';
import PointDetail from './point.detail.component';
import { usePointAnalysisContext } from '@/pages/point/point-chart/point-analysis.provider';
import useQueryParams from '@/hooks/useQueryParams';
type PointItem = ITemplates['basePointDTOS'][0];
interface Props {
  handleRemoveChart: () => void;
  handleSelect: () => void;
  handleRemovePoint: (item: PointItem) => void;
  handleExport: () => Promise<void>;
  layout: 1 | 2 | 3;
  points: ITemplates;
  chartType?: string;
}

export default memo(function AnalysisTemplate({
  handleRemoveChart, // 删除图表
  handleSelect, // 选择图例
  handleRemovePoint, // 删除对比测点
  handleExport, // 导出
  layout, // 配列方式
  points, // 测点
  chartType, // 折线图类型
}: Props) {
  const { saveSync, templates } = usePointAnalysisContext();
  const [activePointCode, setActivePointCode] = useState<string | null>(null);
  const { layoutConfig, chartOptions, chartData, isLayout } = useAnalysis(layout, points);
  const { height, titleFontSize } = layoutConfig[layout];
  const [settingActive, setSettingActive] = useState(false);
  const { type } = useQueryParams();
  const isEdit = useMemo(() => type === 'edit', [type]);

  // 激活的测点
  const activePoint = useMemo(() => {
    return points.basePointDTOS?.find((point) => point.pointCode === activePointCode) || null;
  }, [points, activePointCode]);

  // 打开设置
  const handleSetting = useCallback((point?: PointItem) => {
    setSettingActive(true);
    SettingModal.show({
      callback: saveSetting,
      initialValues: point,
      closeCallback: () => setSettingActive(false),
    });
  }, []);

  // 打开测点详情
  const openDetail = useCallback((point: PointItem) => {
    setActivePointCode(point.pointCode);
  }, []);

  // 保存设置
  const saveSetting = useCallback(
    async (params: any) => {
      const newTem = templates.map((template) => {
        return {
          ...template,
          exceedsLimits: params?.greaterThan,
          lessThanLimits: params?.lessThan,
        };
      });
      await saveSync(newTem);
    },
    [saveSync],
  );

  // 删除测点
  const removePoint = useCallback(
    (point: PointItem) => {
      setActivePointCode('');
      handleRemovePoint(point);
    },
    [handleRemovePoint],
  );

  // 渲染右侧对比测点
  const renderRight = () => {
    if (!isLayout(1)) return null;
    return (
      <aside className="right">
        <h4>对比测点</h4>
        <section className="list">
          {points.basePointDTOS?.map((point, index) => (
            <span
              key={`${point.pointName}-${index}`} // 唯一标识
              className={classNames('item', { active: activePointCode === point.pointCode })}
              onClick={() => openDetail(point)}
            >
              {point.pointName}
            </span>
          ))}
        </section>
        <Button type="primary" onClick={handleSelect} size={isLayout(1) ? 'middle' : 'small'}>
          新增测点
        </Button>
      </aside>
    );
  };

  return (
    <>
      <ChartTemplateWrapper height={height} titleFontSize={titleFontSize}>
        <header className="top">
          <h2>{points.name}</h2>
          <Flex gap={10}>
            <ExportOutlined onClick={() => handleExport()} className="icon" />
            {isEdit && (
              <>
                <SettingOutlined
                  onClick={() => handleSetting()}
                  className={classNames('icon', { active: settingActive })}
                />
                <DeleteOutlined onClick={handleRemoveChart} className="icon" />
              </>
            )}
          </Flex>
        </header>
        <main className="content">
          <div className="wrapper">
            <div className="chart-wrapper">
              <GTwoLine data={chartData} options={chartOptions} chartType={chartType || ''} />
            </div>
            {renderRight()}
          </div>
          {activePointCode && activePoint && (
            <PointDetail
              pointItem={activePoint}
              points={points}
              handleClose={() => setActivePointCode('')}
              handleRemovePoint={(ponit) => removePoint(ponit)}
            />
          )}
        </main>
      </ChartTemplateWrapper>
    </>
  );
});
