import styles from './motion-detail.component.less';
import {
  DragElastic,
  motion,
  TargetAndTransition,
  useAnimation,
  useMotionValue,
  VariantLabels,
} from 'framer-motion';
import Detail from './components/detail/detail.component';
import { useEffect, useLayoutEffect } from 'react';
interface Props {
  isFixed: boolean; // 是否固定
  visible: boolean; //是否显示
  position: { left: number; top: number };
  onMouseLeave: () => void;
  onMouseEnter: () => void;
  detailData: { key: string; label: string; value: string }[]; //详情数据
  onLabelClick: ((title: any, label: any, value: any, key: any) => void) | null; //详情label点击
  title: string;
  onFixedChange: (title: string) => void; //固定状态改变
  parentRef: any; //父节点引用(限制拖拽范围)
}
const MotionDetail: React.FC<Props> = ({
  parentRef,
  onFixedChange,
  title,
  onLabelClick,
  detailData,
  position,
  onMouseLeave,
  onMouseEnter,
  isFixed = false,
  visible = false,
}) => {
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // useLayoutEffect避免弹窗闪烁
  useLayoutEffect(() => {
    if (!isFixed && visible) {
      x.set(0);
      y.set(0);
    }
  }, [isFixed, visible]);

  return (
    <motion.div
      drag={isFixed} // 启用拖拽
      whileDrag={{ scale: 1.1, cursor: 'grabbing' }} // 拖拽时样式
      dragElastic={0} // 关闭弹性效果
      // dragMomentum={false} // 关闭惯性
      dragConstraints={parentRef}
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
      className={styles.popupBox}
      style={{
        display: isFixed ? 'block' : visible ? 'block' : 'none',
        x,
        y,
        left: position.left + 'px',
        top: position.top + 'px',
        cursor: isFixed ? 'grabbing' : 'default',
      }}
    >
      <Detail
        data={detailData}
        onLabelClick={onLabelClick}
        title={title}
        isFixed={isFixed}
        onFixedChange={onFixedChange}
      />
    </motion.div>
  );
};

export default MotionDetail;
