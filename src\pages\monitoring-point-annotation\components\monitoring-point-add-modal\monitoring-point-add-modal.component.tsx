import { FC, memo, useEffect, useRef, useState } from 'react';
import type { PointParams, PropOptions } from './monitoring-point-add-modal';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import styles from './monitoring-point-add-modal.component.less';
import { Form, Input, message } from 'antd';
import { ControllerType } from '../monitoring-point/monitoring-point';
import { addImageNode, updateImageNode } from '@/services/monitoring-point-annotation';
export const MonitoringPointAdd: FC<PropOptions> = memo(({ onOk, nodeData, type, close, open, title }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm();
  useEffect(() => {
    if (open && type === ControllerType.EDIT) {
      formDataShowHandler();
    }
  }, [open]);
  //表单数据回显处理
  function formDataShowHandler(): void {
    forms.setFieldsValue({
      pointName: nodeData.pointName,
      pointCode: nodeData.pointCode,
    });
  }
  //表单数据处理
  function formDataHandler(values: PointParams): PointParams {
    return {
      //树节点显示字段用的name，所以这里给name也传个测点名称
      name: values.pointName,
      pointCode: values.pointCode,
      pointName: values.pointName,
      parentId: type === ControllerType.ADD ? nodeData.id : nodeData.parentId,
      id: type === ControllerType.EDIT ? nodeData.id : undefined,
    };
  }
  function closeHandler(): void {
    resetForm();
    close();
  }
  //重置表单
  function resetForm(): void {
    forms.resetFields();
  }
  //新增请求
  function addRequest(data: PointParams): void {
    setLoading(true);
    addImageNode<PointParams>(data)
      .then((res) => {
        if (res.code == '1') {
          resetForm();
          message.success('添加成功');
          onOk && onOk();
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //编辑请求
  function updateRequest(data: PointParams): void {
    setLoading(true);
    updateImageNode<PointParams>(data, nodeData.id)
      .then((res) => {
        if (res.code == '1') {
          resetForm();
          message.success('更新成功');
          onOk && onOk();
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //提交数据
  function commit(): void {
    forms.validateFields().then((values) => {
      const data = formDataHandler(values);
      if (type === ControllerType.ADD) {
        addRequest(data);
      } else {
        updateRequest(data);
      }
    });
  }
  return (
    <SvgModal
      confirmText="保存"
      cancelText="取消"
      width="500px"
      height="350px"
      close={closeHandler}
      open={open}
      title={title + '监测点'}
      confirmloading={loading}
      onOk={commit}
    >
      <div className={styles['point-add-form']}>
        <Form labelCol={{ span: 5 }} form={forms}>
          <Form.Item<PointParams> label="测点编号" name="pointCode" rules={[{ required: true, message: '请输入测点编号' }]}>
            <Input placeholder="请输入..." />
          </Form.Item>
          <Form.Item<PointParams> label="测点名称" name="pointName" rules={[{ required: true, message: '请输入测点名称' }]}>
            <Input placeholder="请输入..." />
          </Form.Item>
        </Form>
      </div>
    </SvgModal>
  );
});
