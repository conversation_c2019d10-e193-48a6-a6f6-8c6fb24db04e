import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { FC, memo, useEffect, useState } from 'react';
import { FormOptions, PropOptions } from './examine-modal';
import { Form, Input, Radio, Select, Slider, message } from 'antd';
import styles from './examine-modal.component.less';
import { ReportExamineOptions, updateReport } from '@/services/intelligentReport/report-management';

const { TextArea } = Input;
export const ExamineModal: FC<PropOptions> = memo(({ detail, tasksParams, open, close, onOk }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm<FormOptions>();
  const resultList = tasksParams.map((item, index) => {
    const data = JSON.parse(item.conditionParams || `{'pass':${index}}`);
    return {
      label: item.name,
      value: data.pass,
      conditionParams: item.conditionParams,
    };
  });
  useEffect(() => {
    if (open) {
      forms.setFieldsValue({});
    }
  }, [open]);
  function closeFn(): void {
    forms.resetFields();
    close();
  }
  function submit(): void {
    forms.validateFields().then((res) => {
      const { result, opinion } = res;
      const node = resultList.find((item) => item.value === result);
      if (node && detail) {
        const examineParams: ReportExamineOptions = {
          id: detail.id,
          isAudit: 1,
          processInstanceId: detail.processInstanceId,
          taskId: detail.taskId,
          behavior: detail.taskName,
          opinion,
          variables: JSON.parse(node.conditionParams || `{'pass':${result}}`),
        };
        setLoading(true);
        updateReport<ReportExamineOptions>(examineParams)
          .then((res) => {
            if (res.code === '1') {
              message.success('审批完成');
              onOk();
            } else {
              message.warning(res.message || '审批失败');
            }
            setLoading(false);
          })
          .catch((err) => {
            setLoading(false);
          });
      }
    });
  }
  return (
    <SvgModal
      confirmloading={loading}
      height="350px"
      onOk={submit}
      close={closeFn}
      open={open}
      title="报表审核"
    >
      <div className={styles['modal-container']}>
        <Form labelCol={{ span: 5 }} style={{ width: '100%' }} form={forms}>
          <Form.Item<FormOptions>
            rules={[{ required: true, message: '请选择审核结果' }]}
            label="审核结果"
            name="result"
          >
            <Radio.Group>
              {resultList.map((item) => (
                <Radio key={item.value} value={item.value}>
                  {' '}
                  <span className={styles.radio}>{item.label}</span>
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item<FormOptions> label="审核意见" name="opinion">
            <TextArea rows={4} placeholder="请输入..." />
          </Form.Item>
        </Form>
      </div>
    </SvgModal>
  );
});
