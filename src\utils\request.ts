/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { message, notification } from 'antd';
import { debounce } from 'lodash';
import { stringify } from 'querystring';
import { history } from 'umi';
import { extend, RequestOptionsInit } from 'umi-request';
import { downFile } from './downFile';

import { getPageQuery } from './get-page-query';

const codeMessage: any = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

const notice = debounce((status?: number, errorText?: string, url?: string) => {
  if (status === 401 || status === 404 || status === 504) {
    // notification.error({
    //   // message: `Request: ${status}`,
    //   description: '登录已过期，请重新登录',
    // });
  }
  console.error(status, errorText, url);
  notification.error({
    message: `请求错误 ${status}: ${url}`,
    description: errorText,
  });
}, 1000);

/** 请求URL的前缀 */
export const urlPrefix = '/dxdsapi';

export const previewPrefix = '/preview';

export const formHeader = {
  Accept: '*/*',
  'Access-Control-Allow-Origin': '*',
};

export const jsonHeader = {
  'Content-Type': 'application/json',
};

/**
 * 配置request请求时的默认参数
 */
export const request = extend({
  prefix: urlPrefix,
  credentials: 'include', // 默认请求是否带上cookie
  requestType: 'form',
});

/** 全局拦截器 */
request.interceptors.response.use((response, options) => {
  const loginUrl = window.localStorage.getItem('login_url') || '/account/login';
  if (response && response.status !== 200 && response.status !== 201) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notice(status, errorText, url);

    // if (status === 401 || status === 403 || status >= 500) {
    if (status === 401 || status === 403) {
      const { redirect } = getPageQuery();
      window.localStorage.clear();
      window.sessionStorage.clear();
      if (window.location.pathname !== loginUrl && !redirect) {
        history.replace({
          pathname: loginUrl,
          search: stringify({
            redirect: window.location.href,
          }),
        });
      }
    }
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }

  if (options.responseType === 'blob') {
    //判断返回数据是否为二进制流
    return response.blob().then((blob) => {
      return blob.text().then((text) => {
        //判断二进制是否有数据
        if (text.includes('数据为空')) {
          notification.error({
            description: '数据为空，无法导出',
            message: '数据异常',
          });
          return {
            flag: false,
          };
        } else {
          if (options.isReturnBlob) {
            return {
              flag: true,
              data: blob,
            };
          }
          //获取响应头文件名称
          let fileName: string = response.headers.get('Content-Disposition') || '';
          //解码文件名称
          const finalFileName = fileName.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (finalFileName) {
            fileName = decodeURIComponent(finalFileName[1]);
          }
          if (options.tranferUrl) {
            return {
              url: URL.createObjectURL(blob),
              fileName,
            };
          }
          downFile(blob, fileName);
          return {
            flag: true,
          };
        }
      });
    });
  }

  if (
    response.headers.get('content-type') === 'application/octet-stream' ||
    response.headers.get('content-type') === 'text/xml'
  )
    return response.blob();

  return response.json().then((resData) => {
    const flag =
      resData &&
      ((resData.state && resData.state.code === '1') ||
        resData.code === '1' ||
        resData.status === 200 ||
        resData.code === 200);
    const errorMessage = resData.state ? resData.state.msg : resData.message;

    if (!flag && (resData.state || resData.code)) {
      message.error(errorMessage);
    }

    return {
      ...resData,
      flag,
      message: errorMessage || '',
    };
  });
});
