/**
 * @description 交接班页面
 * @returns
 */

import { Tabs } from 'antd';
import styles from './change-shifts.page.less';
import NavBarComponent from '../../components/nav-bar/nav.bar.component';
import NavBtnComponent from './components/nav-button/nav.btn.component';
import DutyFormComponent from './components/duty-form/duty.form.component';
import DutyRecordComponent from './components/duty-record/duty-record.component';
import WorkCondComponent from './components/work-cond/work-cond.component';
import AttachmentComponent from './components/attachment/attachment.component';

export default function ChangeShiftsPage() {
  const items = [
    {
      key: '1',
      label: '值班记录',
      children: <DutyRecordComponent />,
    },
    {
      key: '2',
      label: '交接班工况',
      children: <WorkCondComponent />,
    },
    {
      key: '3',
      label: '附件资料',
      children: <AttachmentComponent isCanOperate={true} />,
    },
  ];

  return (
    <div className={styles['change-shifts-page']}>
      <NavBarComponent rightComponent={<NavBtnComponent />} />
      <div className={styles['change-shifts-page-content']}>
        <DutyFormComponent />
        <Tabs items={items} size="large" />
      </div>
    </div>
  );
}
