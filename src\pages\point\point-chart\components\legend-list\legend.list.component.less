@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.list-container {
  display: grid;
  grid-template-columns: repeat(4, 375px); /* 4列，每列375px */
  gap: 35px 0;
  justify-content: space-between;
  height: calc(100vh - 275px);
  .list-wrapper {
    opacity: 0;
    width: 375px;
    animation: fadeInUp 0.5s ease-out forwards;
    animation-delay: calc(var(--i) * 0.08s);
  }
}

.overlay-wrapper {
  width: 100%;
  height: 214px;
  border-radius: 10px;
  position: relative;
  border: solid 1px #244984;
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  &:hover {
    box-shadow: 0 0 10px rgba(21, 119, 165, 0.2), 0 0 20px rgba(21, 119, 165, 0.1),
      0 0 30px rgba(21, 119, 165, 0.05);
  }
  &:hover .overlay {
    opacity: 1;
  }
}
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.top-icons {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 16px;
  cursor: pointer;
  .icon {
    &:hover {
      color: #0ad0ee;
    }
  }
}
.center-buttons {
  display: flex;
  gap: 16px;
}
aside {
  margin-top: 10px;
  p {
    margin: 0;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
  }
}
.tlt {
  font-size: 14px;
  color: #ffffff;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.date {
  font-size: 12px;
  color: #cccccc;
  line-height: 16px;
}
