import { jsonHeader, request } from '@/utils/request';

// 查询根节点关系
export function findAndInsertRoot(params: any) {
  return request.post('/BusinessNodeEntity/findAndInsert', { params });
}

//设备信息详情
export function getDeviceItem(id: string) {
  return request.get(`/DeviceResume/${id}`);
}

// 列表查询现存测点
export function getMonitoringPointList() {
  return request.get('/MonitorBaseInfo/list');
}

// 字典
export function findDictTree(dictCode: string) {
  return request.get('/Dict/findDictTree', {
    params: { dictCode },
  });
}

/**
 * 测点菜单 - 测点列表页 - 获取测点列表
 */
export function getPointList(params: any) {
  return request.post(`/BasePoint/listPage`, { data: params });
}

// 新增节点
export function addNode(data: any) {
  return request.post('/BusinessNodeEntity/insertNode', {
    data: JSON.stringify({
      ...data,
      businessType: '业务',
    }),
    headers: jsonHeader,
  });
}

//设备信息详情
export function getDeviceInfoById(id: string) {
  return request.get(`/DeviceResume/${id}`);
}

// 删除节点关系
export function deleteEdge(params: any) {
  return request.post('/BusinessNodeEntity/deleteRelation', { params });
}
