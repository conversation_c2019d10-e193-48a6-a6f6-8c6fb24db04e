import type { Effects, Reducers } from '@/global';

export interface IRootState {
  TopologyContent: IState;
}

export interface IState {
  queryInfo: any;
  monitoringDeviceTree: any[];
}

const initalState: IState = {
  queryInfo: null,
  monitoringDeviceTree: [], // 监测设备
};

const effects: Effects = {};

const reducers: Reducers = {
  changeQueryInfo(state: IState, { payload }) {
    return { ...state, queryInfo: payload };
  },
  changeMonitoringDeviceTree(state: IState, { payload }) {
    return { ...state, monitoringDeviceTree: payload };
  },
};

export default {
  state: initalState,
  effects,
  reducers,
};
