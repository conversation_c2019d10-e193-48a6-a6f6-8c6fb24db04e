import { useState, useEffect, useRef, useCallback } from 'react';

interface WebSocketOptions {
  reconnectLimit?: number; // 最大自动重连尝试次数
  reconnectInterval?: number; // 重试间隔时间基数
  heartbeatInterval?: number; // 心跳间隔时间
  onMessage?: (data: any) => void; // 消息回调，data获取实时数据
  protocols?: string | string[]; //连接协议
}

export const useWebSocket = (url: string, options?: WebSocketOptions) => {
  const { reconnectLimit = 5, reconnectInterval = 1000, heartbeatInterval = 30000, onMessage, protocols = 'json' } = options || {};

  // 状态管理
  const [readyState, setReadyState] = useState<number>(WebSocket.CONNECTING); // 当前连接状态
  const [manualClose, setManualClose] = useState(false); // 是否已经手动停止了连接
  const [receivedData, setReceivedData] = useState<any>(null); // 存储接收的数据，适用于历史数据

  // 引用存储
  const wsRef = useRef<WebSocket | null>(null); // 实例
  const reconnectCountRef = useRef(0); // 重连次数
  const messageQueueRef = useRef<any[]>([]); // 消息队列
  const heartbeatTimerRef = useRef<NodeJS.Timeout>(); // 心跳机制定时器
  const reconnectTimerRef = useRef<NodeJS.Timeout>(); // 自动重连定时器

  // 状态常量映射
  const readyStateMapping: any = {
    [WebSocket.CONNECTING]: 0,
    [WebSocket.OPEN]: 1,
    [WebSocket.CLOSING]: 2,
    [WebSocket.CLOSED]: 3,
  };

  // 核心连接方法
  const connect = useCallback(() => {
    // 清理旧连接
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    // 生成新连接
    const ws = new WebSocket(url, protocols);
    wsRef.current = ws;

    // #region 建立连接
    ws.onopen = () => {
      console.log('WebSocket connected');
      setReadyState(WebSocket.OPEN); // 存储连接状态
      reconnectCountRef.current = 0; // 连接次数清零
      startHeartbeat(); // 启动心跳检测
      flushMessageQueue(); // 建立连接后处理消息队列信息
    };

    // #region 接受信息
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      // 根据约定的type处理返回的数据
      switch (data.type) {
        case 'pong':
          console.log('收到心跳响应');
          break;
        case 'curData':
          console.log('收到实时数据:', data.message);
          if (onMessage) {
            onMessage(data); // 回调函数获取实时数据
          }
          setReceivedData((prev: any) => [...prev, data]); // 更新接收过的数据（存储历史数据）
          break;
        default:
          console.log('未知消息类型:', data);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    // #region 监听连接断开
    ws.onclose = (event) => {
      console.log('WebSocket closed:', event);
      setReadyState(WebSocket.CLOSED); // 存储连接状态
      stopHeartbeat(); // 停止心跳检测

      // 如果没有手动停止重连并且连接失败就自动重连
      if (!manualClose && event.code !== 1000) {
        scheduleReconnect();
      }
    };
  }, [url, manualClose]);

  // #region 自动重连逻辑
  const scheduleReconnect = useCallback(() => {
    // 当前重连次数大于等于设定的最大重连次数就停止重连
    if (reconnectCountRef.current >= reconnectLimit) {
      console.log('Reached maximum reconnect attempts');
      return;
    }
    // 指数退避策略，让间隔时间随着重连次数成2的指数型增长
    const delay = reconnectInterval * Math.pow(2, reconnectCountRef.current);
    reconnectTimerRef.current = setTimeout(() => {
      reconnectCountRef.current++;
      connect(); // 建立连接
    }, delay);
  }, [reconnectInterval, reconnectLimit, connect]);

  // #region 开启心跳机制
  const startHeartbeat = useCallback(() => {
    heartbeatTimerRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        // 定时发送心跳验证信息如ping，具体格式需要两边约定好
        wsRef.current.send('ping');
      }
    }, heartbeatInterval);
  }, [heartbeatInterval]);

  // #region 关闭心跳机制
  const stopHeartbeat = useCallback(() => {
    heartbeatTimerRef.current && clearInterval(heartbeatTimerRef.current);
  }, []);

  // #region 消息队列处理
  const flushMessageQueue = useCallback(() => {
    while (messageQueueRef.current.length > 0) {
      const message = messageQueueRef.current.shift();
      wsRef.current?.send(message);
    }
  }, []);

  // #region 提供给外部：发送消息的方法
  const sendMessage = useCallback((data: string) => {
    // 连接中就直接发，否则放进消息队列里
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(data);
    } else {
      messageQueueRef.current.push(data);
    }
  }, []);

  // #region 提供给外部：手动关闭连接的方法
  const closeConnection = useCallback(() => {
    setManualClose(true); // 已手动关闭连接
    wsRef.current?.close(1000, 'Manual close');
  }, []);

  // #region 网络恢复监听
  useEffect(() => {
    const handleOnline = () => {
      // 如果网络恢复之后连接状态是断开的且没有被手动关闭连接就重连
      if (wsRef.current?.readyState === WebSocket.CLOSED && !manualClose) {
        connect();
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [connect, manualClose]);

  // #region 初始化连接
  useEffect(() => {
    connect();

    return () => {
      // 清理操作
      closeConnection();
      stopHeartbeat();
      reconnectTimerRef.current && clearTimeout(reconnectTimerRef.current);
    };
  }, [connect, closeConnection, stopHeartbeat]);

  return {
    sendMessage, //发送信息
    closeConnection, //手动关闭连接
    receivedData, // 接收的数据
    readyState: readyStateMapping[readyState], // 转换为通用常量
    isConnected: readyState === WebSocket.OPEN,
  };
};
