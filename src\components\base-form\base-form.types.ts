import type { ReactNode } from 'react';
import type {
  ButtonProps,
  CheckboxOptionType,
  DatePickerProps,
  FormItemProps,
  FormProps,
  ImageProps,
  InputNumberProps,
  InputProps,
  RadioProps,
  SelectProps,
  SwitchProps,
  UploadFile,
} from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import type { TextAreaProps } from 'antd/es/input';
import type { FormInstance, Rule } from 'antd/es/form';
import type { UploadChangeParam, UploadProps } from 'antd/es/upload';
import type { UploadListType } from 'antd/es/upload/interface';

export type FieldType = any;

export interface IBaseFormExp {
  baseFormInstance: FormInstance<FieldType>;
}

export interface IBaseFormConfig {
  elements: ElementType[];
  layout?: 'inline' | 'horizontal' | 'vertical';
  initialValues?: Record<string, any>;
  formPorps?: FormProps;
}

export type ElementType =
  | IButton
  | IInput
  | IInputNumber
  | ITextArea
  | ISelect
  | IRadio
  | ISwitch
  | IDatePicker
  | IRangePicker
  | IUpload
  | ISingleImage
  | IGroupImage
  | ICustom;

export interface IElement {
  key: string;
  type: EElementType;
  label: FormItemProps['label'];
  formItemProps?: FormItemProps;
}

export enum EElementType {
  BUTTON,
  INPUT,
  INPUTNUMBER,
  TEXTAREA,
  SELECT,
  RADIO,
  SWITCH,
  DATEPICKER,
  RANGEPICKER,
  UPLOAD,
  IMAGE,
  CUSTOM,
}

export interface IFieldElement extends IElement {
  field: string;
  rules?: Rule[];
}

export interface IButton extends IElement {
  type: EElementType.BUTTON;
  disabled?: boolean;
  noStyle?: boolean;
  loading?: boolean;
  onClick?: ButtonProps['onClick'];
  props?: ButtonProps;
}

export interface IInput extends IFieldElement {
  type: EElementType.INPUT;
  disabled?: boolean;
  props?: InputProps;
  onBlur?: InputProps['onBlur'];
  onChange?: InputProps['onChange'];
}

export interface IInputNumber extends IFieldElement {
  type: EElementType.INPUTNUMBER;
  disabled?: boolean;
  min?: InputNumberProps['min'];
  max?: InputNumberProps['max'];
  addonAfter?: InputNumberProps['addonAfter'];
  props?: InputNumberProps;
  onBlur?: InputNumberProps['onBlur'];
  onChange?: InputNumberProps['onChange'];
}

export interface ITextArea extends IFieldElement {
  type: EElementType.TEXTAREA;
  disabled?: boolean;
  props?: TextAreaProps;
  onBlur?: TextAreaProps['onBlur'];
  onChange?: TextAreaProps['onChange'];
}

export interface ISelect extends IFieldElement {
  type: EElementType.SELECT;
  options: IOption[];
  allowClear?: boolean;
  disabled?: boolean;
  props?: SelectProps;
  onChange?: SelectProps['onChange'];
}

export interface IRadio extends IFieldElement {
  type: EElementType.RADIO;
  options: CheckboxOptionType[];
  disabled?: boolean;
  props?: RadioProps;
}

export interface ISwitch extends IFieldElement {
  type: EElementType.SWITCH;
  disabled?: boolean;
  defaultChecked?: boolean;
  onChange?: SwitchProps['onChange'];
  props?: SwitchProps;
}

export interface IDatePicker extends IFieldElement {
  type: EElementType.DATEPICKER;
  allowClear?: boolean;
  props?: DatePickerProps;
}

export interface IRangePicker extends IFieldElement {
  type: EElementType.RANGEPICKER;
  allowClear?: boolean;
  props?: RangePickerProps;
}

export interface IUpload extends IFieldElement {
  type: EElementType.UPLOAD;
  listType?: UploadListType;
  accept?: UploadProps['accept'];
  maxCount?: UploadProps['maxCount'];
  multiple?: boolean;
  onChange?: ((info: UploadChangeParam<UploadFile<any>>) => void) | undefined;
  children?: ReactNode;
  props?: UploadProps;
}

export interface IImage extends IElement {
  type: EElementType.IMAGE;
  props?: ImageProps;
  group?: boolean;
}

export interface ISingleImage extends IImage {
  group?: false;
  src: string;
}

export interface IGroupImage extends IImage {
  group: true;
  items: string[];
}

export interface ICustom extends IElement {
  type: EElementType.CUSTOM;
  noStyle?: boolean;
  element: ReactNode;
}

export interface IOption {
  key: string;
  label: string;
  value: string | number;
}
