import { useState } from 'react';
import { Form, Button, Space, Input, Select, Alert } from 'antd';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { insertFlux } from './point.service';
// 解析时间
export const parseTime = (timeStr: string) => {
  return new Date(timeStr);
};
// 格式化时间为指定格式
export const formatTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
  // 格式化
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}:${milliseconds}`;
};

// 递增时间函数
export const incrementTime = (timeStr: string) => {
  const date = parseTime(timeStr);
  date.setSeconds(date.getSeconds() + 10); // 递增秒
  return formatTime(date);
};

export const defaultParams = {
  datatype: '', // 自己更改
  key: '', // 编码 - 自己更改
  name: '', // 名称 - 自己更改
  quality: '0',
  realTimeStr: (() => {
    const date = new Date();
    date.setMinutes(date.getMinutes() - 30); // 设置为当前时间30分钟前
    return formatTime(date); // 使用已定义的formatTime函数格式化时间
  })(), // 时间 - 设置为当前时间半小时前
  value: parseFloat((Math.random() * 100 + 1).toFixed(2)), // 保留两位小数
};

/**
 * <AUTHOR>
 * 测试数据插入组件
 * @returns 测试数据插入UI组件
 */
const FluxInsertPanel = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [polling, setPolling] = useState(false);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  // 点击确定
  const handleOk = async () => {
    const valid = await form.validateFields();
    if (valid) {
      const values = form.getFieldsValue();
      setPolling(true);
      setOpen(false);
      const id = setInterval(async () => {
        values.realTimeStr = incrementTime(values.realTimeStr); // 更新时间
        values.value = parseFloat((Math.random() * 100 + 1).toFixed(2)); // 更新值并保留两位小数
        await insertFlux(values); // 插入数据
      }, 100); // 每秒插入一次
      setIntervalId(id);
    }
  };
  // 停止轮询
  const stopPolling = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
      setPolling(false);
    }
  };
  // 点击取消
  const handleCancel = () => {
    setOpen(false);
    form.resetFields();
  };
  return (
    <>
      <Space style={{ marginRight: 10 }}>
        <Button onClick={() => setOpen(true)}>新增测试数据</Button>
        <Button onClick={stopPolling} disabled={!polling}>
          停止
        </Button>
      </Space>
      <DragModal
        okText="确定"
        cancelText="取消"
        title="新增测试数据"
        open={open}
        onCancel={handleCancel}
        onOk={handleOk}
        width={500}
      >
        <Form
          form={form}
          initialValues={{
            ...defaultParams,
          }}
        >
          <Form.Item
            label="类型"
            name="datatype"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select>
              <Select.Option value="state">遥信</Select.Option>
              <Select.Option value="analog">遥测</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="编码" name="key" rules={[{ required: true, message: '请输入编码' }]}>
            <Input />
          </Form.Item>
          <Form.Item label="名称" name="name" rules={[{ required: true, message: '请输入名称' }]}>
            <Input />
          </Form.Item>
          <Form.Item label="值" name="value" hidden>
            <Input />
          </Form.Item>
          <Form.Item label="时间" name="realTimeStr" hidden>
            <Input />
          </Form.Item>
          <Form.Item label="质量" name="quality" hidden>
            <Input />
          </Form.Item>
          <Form.Item label="提示">
            <Alert
              message="由于测试环境没有现场数据，提供此组件插入模拟数据，点击确定后，当前请打开Network面板，查看插入数据"
              type="info"
            />
          </Form.Item>
        </Form>
      </DragModal>
    </>
  );
};
export default FluxInsertPanel;
