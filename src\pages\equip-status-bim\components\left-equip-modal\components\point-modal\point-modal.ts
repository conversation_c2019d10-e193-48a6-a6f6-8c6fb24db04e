import { TreeOptions } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';

export interface PropOptions {
  open: boolean;
  close: () => void; //关闭方法
  onOk: (list: TreeOptions[]) => void; //确定
}
export const paramsOptions = {
  //表格请求参数
  pageNum: 1,
  pageSize: 10,
  pointCode: '', //编码
  pointName: '', //名称
  pointType: '', //类型analog
};
export enum PointType {
  state = 'state',
  analog = 'analog',
}
export const PointTypes = [
  {
    label: '遥信',
    value: PointType.state,
  },
  {
    label: '遥测',
    value: PointType.analog,
  },
];
