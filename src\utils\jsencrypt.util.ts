import JSEncrypt from 'jsencrypt';
const pubKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeVp+B5QC2io6RJYx5NtMryiE02yIRVi0Ead2Oaklfo6cc/vBoSh35oxVDA2WSfgP5JwZ74DqRHFHt5LbXVnR//CebiHrvaFfcF31TNIriDtvitGqGaONbzRSYjI0t4OVHUtR1MGXtD+yCuXnLnZJE8Rnn+c5YHROG+AH3gV6t1wIDAQAB';

export function rsaEncrypt(value: string) {
  const encryptor = new JSEncrypt(); // 创建加密对象实例
  encryptor.setPublicKey(pubKey); // 设置公钥
  return encryptor.encrypt(value);
}
