import { FC, ReactNode, memo, useEffect, useState } from 'react';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { PropOptions, paramsOptions, PointTypes } from './point-modal';
import { Form, Input, Select, Space, Tooltip, message } from 'antd';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsOptions } from '@/components/table-component/table';
import { ColumnsType } from 'antd/es/table';
import styles from './point-modal.component.less';
import { getPointList } from '@/pages/point/point.service';
import { TreeOptions } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { CheckOutlined } from '@ant-design/icons';

export let params = JSON.parse(JSON.stringify(paramsOptions));
export const PointModal: FC<PropOptions> = memo(({ onOk, open, close }) => {
  //测点列表
  const [pointList, setPointList] = useState<TreeOptions[]>([]);
  //表格数据总数
  const [total, setTotal] = useState<number>(0);
  //加载控制
  const [loading, setLoading] = useState<boolean>(false);
  //当前选择数据
  const [curTableData, setCurTableData] = useState<TreeOptions>();
  const [forms] = Form.useForm();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '名称',
      dataIndex: 'pointName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '编码',
      dataIndex: 'pointCode',
      align: 'center',
      ellipsis: true,
      width: 250,
    },
    {
      title: '类型',
      dataIndex: 'pointType',
      align: 'center',
      ellipsis: true,
      width: 70,
    },
    {
      title: '操作',
      dataIndex: 'controller',
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (val: string, record: any) => tableControllerComponent(record),
    },
  ];
  useEffect(() => {
    if (open) {
      reset();
    }
  }, [open]);
  //表格操作模板
  function tableControllerComponent(record: TreeOptions): ReactNode {
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          <Tooltip title="确定">
            <CheckOutlined onClick={() => onOk(record)} className={styles.icon} />
          </Tooltip>
        </Space>
      </div>
    );
  }
  //关闭处理
  function closeHandler(): void {
    close();
  }
  //确定选择
  function sure(): void {
    if (curTableData) {
      onOk(curTableData);
    } else {
      message.warning('请选择一个测点');
    }
  }
  //数据查询
  function query(param?: any): void {
    setLoading(true);
    if (param) {
      params = { ...params, ...param };
    }
    getPointList(params)
      .then((res) => {
        if (res.code === '1') {
          if (res.data.list) {
            const list: any[] = res.data.list;
            setPointList(list);
          }
          setTotal(parseInt(res.data.total));
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //页码切换
  function pageChange(page: number, pageSize: number): void {
    query({
      pageSize: pageSize,
      pageNum: page,
    });
  }
  //条件查询
  function paramsQuery(): void {
    const param = forms.getFieldsValue();
    params = JSON.parse(JSON.stringify(paramsOptions));
    query(param);
  }
  //已选中测点重置
  function reset(): void {
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    query();
  }
  return (
    <SvgModal
      width="1000px"
      height="530px"
      close={closeHandler}
      isFooterBtn={false}
      onOk={sure}
      open={open}
      title="测点列表"
    >
      <Form
        form={forms}
        layout="inline"
        initialValues={{ layout: 'inline' }}
        style={{ maxWidth: 'none', paddingLeft: '23px' }}
      >
        <Form.Item label="名称" name="pointName">
          <Input style={{ width: '170px' }} placeholder="请输入..." />
        </Form.Item>
        <Form.Item label="编码" name="pointCode">
          <Input style={{ width: '170px' }} placeholder="请输入..." />
        </Form.Item>
        <Form.Item label="类型" name="pointType">
          <Select options={PointTypes} style={{ width: '170px' }} placeholder="请选择..." />
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={paramsQuery}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={reset}>
            重置
          </type-button>
        </Form.Item>
      </Form>
      <TableComponent
        isVirtual={true}
        params={{
          pageChange: pageChange,
          total: total,
          pageSize: params.pageSize,
          current: params.pageNum,
        }}
        loading={loading}
        scroll={{ y: 270 }}
        columns={columns}
        tableList={pointList}
      ></TableComponent>
    </SvgModal>
  );
});
