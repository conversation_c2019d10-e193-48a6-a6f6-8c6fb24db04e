import React, { memo, useEffect, useState } from 'react';
import type { ReactNode, FC } from 'react';

import { GraphContentWrapper } from './graph-content.style';
import DashboradGraph from './c-cpns/dashborad-graph/dashborad-graph.component';
import BarGraph from './c-cpns/bar-graph/bar-graph.component';
import LineGraph from './c-cpns/line-graph/line-graph.component';
import AreaGraph from './c-cpns/area-graph/area-graph.component';
import { getWaterStatistic } from '../../epuip-status-overview.service';

interface IProps {
  children?: ReactNode;
}

const GraphContent: FC<IProps> = () => {
  const [waterStatistic, setWaterStatistic] = useState<any[]>([]);

  useEffect(() => {
    initWaterStatistic();
  }, []);

  const initWaterStatistic = () => {
    getWaterStatistic().then((res) => {
      setWaterStatistic(res.data);
    });
  };

  return (
    <GraphContentWrapper>
      <DashboradGraph key="1号机组" title="1号机组" subTitle="1号机组并网" />
      <DashboradGraph key="2号机组" title="2号机组" subTitle="2号机组并网" />
      <DashboradGraph key="3号机组" title="3号机组" subTitle="3号机组并网" />
      <BarGraph title="机组发电功率统计" />
      <LineGraph title="日发电曲线" />
      <AreaGraph title="日水情信息" waterStatistic={waterStatistic} />
    </GraphContentWrapper>
  );
};

export default memo(GraphContent);
