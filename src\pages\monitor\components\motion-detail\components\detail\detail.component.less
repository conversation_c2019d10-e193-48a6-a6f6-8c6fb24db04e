.descriptionsBox {
  .iconBox {
    padding: 6px 10px 0px 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .descriptionsPanel {
    padding: 6px;

    :global {
      .ant-descriptions-title {
        margin: 0;
        padding: 8px;
        font-size: 14px;
        background-color: #ebebeb;
      }

      .ant-descriptions-row:nth-child(2n + 1) {
        background-color: #071f43;
      }

      .ant-descriptions-item-label {
        width: 140px;
        padding-left: 24px;
        color: #d4ebfd !important;

        &::after {
          content: '';
        }
      }

      .ant-descriptions-item-content {
        color: #fff !important;
      }

      .ant-descriptions-item-label,
      .ant-descriptions-item-content {
        min-height: auto;
        font-size: 13px;
        line-height: 40px;
      }

      .ant-descriptions-row > th,
      .ant-descriptions-row > td {
        padding-bottom: 0;
      }
    }
  }
}

.empty {
  height: 50px;
  color: #fff !important;

  :global {
    .ant-empty-description {
      color: #fff;
      font-size: 12px;
    }
  }
}
