import { FC, memo, useEffect } from 'react';
import styles from './unit-tab.component.less';
import { PropOptions, UnitListData, UnitOptions } from './unit-tab';
import iconNoSelecteIcon from '@/assets/images/icon_notselected.png';
import iconSelecteIcon from '@/assets/images/icon_select.png';
export const UnitTab: FC<PropOptions> = memo(({ tabKey, onChange }) => {
  useEffect(() => {
    onChange(JSON.parse(JSON.stringify(UnitListData[0])));
  }, []);
  //变压器切换
  function onTabChange(data: UnitOptions): void {
    onChange(JSON.parse(JSON.stringify(data)));
  }
  return (
    <section className={styles['unit-tab']}>
      {UnitListData.map((item) => (
        <div
          onClick={() => onTabChange(item)}
          style={{
            backgroundImage: `url(${tabKey === item.value ? iconSelecteIcon : iconNoSelecteIcon})`,
          }}
          className={styles['unit-tab-item']}
          key={item.value}
        >
          <span>{item.label}</span>
        </div>
      ))}
    </section>
  );
});
