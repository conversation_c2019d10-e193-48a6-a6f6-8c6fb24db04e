import { Button, Upload, Table, ConfigProvider, Space, Tooltip } from 'antd';
import styles from './attachment.component.less';
import { useState } from 'react';
import { ColumnsType } from 'antd/es/table';
import { UploadOutlined, EyeOutlined, DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import { urlPrefix } from '@/utils/request';

interface AttachmentComponentProps {
  isCanOperate: boolean; // 是否可以操作
}
export default function AttachmentComponent({ isCanOperate = true }: AttachmentComponentProps) {
  const [fileList, setFileList] = useState<any[]>([]); // 文件列表

  // 删除
  const onDelete = (record: any) => {
    setFileList(fileList.filter((item) => item.uid !== record.uid));
  };
  // 表格列
  const columns: ColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 100,
      align: 'center',
      render: (_text, _record, index: number) => index + 1,
    },
    { title: '附件名称', dataIndex: 'name', key: 'name', align: 'center' },
    {
      title: '上传人',
      dataIndex: 'uploadUserName',
      key: 'uploadUserName',
      align: 'center',
    },
    {
      title: '上传时间',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      render: (text, record, _index: number) => {
        if (record.fileType === 'local') return '待上传';
        return text;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '10%',
      align: 'center',
      render: (_text, record, _index: number) => {
        return (
          <Space size="middle">
            {record.url ? (
              <a
                onClick={() => {
                  window.open(`${window.location.origin}/preview/onlinePreview?url=${record.url}`);
                }}
              >
                <Tooltip title="预览">
                  <EyeOutlined title="预览" style={{ fontSize: '18px' }} />
                </Tooltip>
              </a>
            ) : null}
            {isCanOperate ? (
              <a onClick={() => onDelete(record)}>
                <Tooltip title="删除">
                  <DeleteOutlined title="删除" style={{ fontSize: '18px' }} />
                </Tooltip>
              </a>
            ) : null}
            <a href={`${urlPrefix}/Attachment/downloadAttachment/${record.id}`}>
              <Tooltip title="下载">
                <DownloadOutlined title="下载" style={{ fontSize: '18px' }} />
              </Tooltip>
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <div className={styles.upload}>
      <div className={styles['upload-file-box']}>
        {/* <div className={styles['upload-file-box-title']}>相关附件:</div> */}
        {isCanOperate ? (
          <Upload
            fileList={fileList}
            name="file"
            accept=".xlsx,.xls,.txt"
            beforeUpload={() => false}
            multiple={true} // 是否支持多选
            headers={{
              authorization: 'authorization-text', // 授权
            }}
            showUploadList={false}
            onChange={(info) => {
              setFileList((prev) => {
                // 获取新上传的文件
                const newFiles = info?.fileList?.map((item) => {
                  return {
                    originFileObj: item.originFileObj,
                    name: item.name, // 文件名
                    type: item.type, // 文件类型
                    id: item.uid, // 临时
                    uploadUserName: name, // 上传人
                    fileType: 'local', // 文件状态
                  };
                });

                // 判重处理：过滤掉已存在的文件（根据文件名判断）
                const existingFileNames = prev?.map((file) => file.name);
                const uniqueNewFiles = newFiles?.filter(
                  (file) => !existingFileNames?.includes(file.name),
                );

                // 合并现有文件和新文件
                return [...prev, ...uniqueNewFiles];
              });
            }}
          >
            <Button icon={<UploadOutlined />} type="primary">
              上传文件
            </Button>
          </Upload>
        ) : null}
      </div>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2F4F81', // 表格边框颜色
            },
          },
        }}
      >
        <Table
          bordered
          size="small"
          rowKey="id" // key
          dataSource={fileList}
          columns={columns}
          scroll={{ y: 600, scrollToFirstRowOnChange: true }}
          pagination={false}
          className={styles['upload-table']}
        />
      </ConfigProvider>
    </div>
  );
}
