import { FC, memo } from 'react';
import styles from './data-indicators.component.less';
import { PropOptions } from './data-indicators';
import boxIcon from '@/assets/images/icon_databack.png';
export const DataIndicators: FC<PropOptions> = memo(({ pointData }) => {
  return (
    <section className={styles['data-indicators']}>
      <div className={styles['data-indicators-container']}>
        {pointData ? (
          pointData.indicators.map((item, index) => (
            <div
              style={{
                backgroundImage: `url(${boxIcon})`,
              }}
              className={styles['data-item']}
              key={item.value}
            >
              <span>{item.label}</span>
              <span>
                {item.price ? item.price : '—'}
                <span className={styles['data-item-unit']}>{item.unit}</span>
              </span>
            </div>
          ))
        ) : (
          <></>
        )}
      </div>
    </section>
  );
});
