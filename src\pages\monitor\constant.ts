export const connectUrl = `http://${window.location.hostname}:8017/dxdsapi/ws-service/register`; //socket url
// export const connectUrl = 'http://*************:8017/dxdsapi/ws-service/register'; //socket url 游

export const subTheme = '/dxdsapi/subscribe'; //消息主题-订阅
export const unsubTheme = '/dxdsapi/unsubscribe'; //消息主题-取消订阅
export const sourceHead1 = '/topic/pageTopic'; //监听前缀，供页面数据使用
export const sourceHead2 = '/topic/modalTopic'; //监听前缀，供弹窗数据使用
export const sourceHead3 = '/topic/hoverTopic'; //监听前缀，供悬浮窗详情数据使用

// 接口字段值与svg中id的映射表
export const YjxInfoMap2: any = {
  '羊加Ⅰ线有功功率P(计算)': 'p',
  '羊加Ⅰ线无功功率Q(计算)': 'q',
  '羊加Ⅱ线有功功率P(计算)': 'p',
  '羊加Ⅱ线无功功率Q(计算)': 'q',
  线电压Uab: 'uab',
  电流Ia: 'ia',
};

export const MxInfoMap2: any = {
  '330kVⅠ母Uab相电压(变送器)': 'uab',
  '330kVⅡ母Uab相电压(变送器)': 'uab',
  频率: 'f',
};

export const JzInfoMap: any = {
  有功功率: 'p',
  无功功率: 'q',
  导叶开度: 'dykd',
};
export const SvgLabelIds: any = [
  '1号机组-有功功率',
  '1号机组-无功功率',
  '1号机组-导叶开度',
  '2号机组-有功功率',
  '2号机组-无功功率',
  '2号机组-导叶开度',
  '3号机组-有功功率',
  '3号机组-无功功率',
  '3号机组-导叶开度',
  '330kVⅠ母-330kVⅠ母Uab相电压(变送器)',
  '330kVⅠ母-频率',
  '330kVⅡ母-330kVⅡ母Uab相电压(变送器)',
  '330kVⅡ母-频率',
  '羊加Ⅰ线-羊加Ⅰ线有功功率P(计算)',
  '羊加Ⅰ线-羊加Ⅰ线无功功率Q(计算)',
  '羊加Ⅰ线-线电压Uab',
  '羊加Ⅰ线-电流Ia',
  '羊加Ⅱ线-羊加Ⅱ线有功功率P(计算)',
  '羊加Ⅱ线-羊加Ⅱ线无功功率Q(计算)',
  '羊加Ⅱ线-线电压Uab',
  '羊加Ⅱ线-电流Ia',
];
export const circles: string[] = [
  '180',
  '18011',
  '180167',
  '18017',
  '180117',
  '180217',
  '180317',
  '18021',
  '180267',
  '18027',
  '18031',
  '180317',
  '180367',
  '18037',
  '310',
  '320',
  '3117',
  '320',
  '3217',
  '330',
  '33101',
  '331017',
  '33102',
  '331027',
  '33111',
  '331117',
  '33112',
  '331127',
  '33116',
  '3311617',
  '331167',
  '33121',
  '331217',
  '33122',
  '331227',
  '33126',
  '3312617',
  '331267',
  '33201',
  '332017',
  '33202',
  '332027',
  '33211',
  '332117',
  '33212',
  '332127',
  '33216',
  '3321617',
  '332167',
  '33301',
  '333017',
  '33302',
  '333027',
  '33311',
  '333117',
  '33312',
  '333127',
  '33316',
  '3331617',
  '333167',
  '33321',
  '333217',
  '33322',
  '333227',
  '33326',
  '3332617',
  '333267',
];
export const lines: string[] = [
  '3332',
  '3330',
  '3331',
  '3312',
  '3310',
  '3311',
  '3320',
  '3321',
  '1801',
  '1802',
  '1803',
];
export const ST: string[] = ['ST1', 'ST2', 'ST3'];
export const nF: string[] = ['1F', '2F', '3F'];
export const greenColor = '#08bc23';
export const redColor = '#e9050a';
export const colorMap: any = {
  0: greenColor, //开闸
  1: redColor, //合闸
  2: greenColor, // 停机态
  3: redColor, // 发电态
  4: '#d5c06f', // 空载
  5: '#c669a3', // 空转
  6: '#fff', // 检修态
  7: '#0667ca', // 过渡态
};
// 停机态绿色2、发电态红色3、空载黄色4、空转紫色5、检修态白色6、过渡态蓝色7
export const TextMap: any = {
  2: '停机态',
  3: '发电态',
  4: '空载',
  5: '空转',
  6: '检修态',
  7: '过渡态',
};
