import React from 'react';

export const vmsroot = 'http://**************:1934';
const { NODE_ENV, PORT, HOST } = process.env;

// export const publicPath = NODE_ENV === 'production' ? '' : '';
export const publicPath = NODE_ENV === 'production' ? '/micro_security' : '/micro_security';

/**
 * 将对象转换成FormData
 * @export
 * @param {object} [param={}]
 * @returns
 */
export function parseParamFromData(param: any = {}) {
  const formData = new FormData();
  for (const key in param) {
    if (key && param[key] !== undefined) {
      if (Object.prototype.toString.call(param[key]) === '[object Array]') {
        for (let i = 0; i < param[key].length; i += 1) {
          formData.append(key, param[key][i]);
        }
      } else {
        formData.append(key, param[key]);
      }
    }
  }
  return formData;
}

/**
 * 安全总览、安全统计柱线图数据处理
 * @param data 接口返回数据
 * @param typeList 页面展示类型
 * @returns
 */
export const handleColumnChartData = (data: any[], typeList: any[]) => {
  const newArr: { unit: string; name: string; xData: string; yData: number }[] = [];
  if (data?.length > 0) {
    data.forEach((item) => {
      typeList.forEach((it) => {
        if (item.unitName) {
          newArr.push({
            unit: '个',
            name: it.text,
            xData: item.unitName,
            yData: item?.[it.key] || 0,
          });
        }
      });
    });
  }
  return newArr;
};

// 遍历树 获取叶子节点
export const deepLeafFn = (
  arr: any[],
  fn = (params: any) => {
    console.log(params);
  },
  children = 'children',
) => {
  if (Array.isArray(arr)) {
    arr.forEach((item) => {
      if (Array.isArray(item[children]) && item[children].length > 0) {
        deepLeafFn(item[children], fn, children);
      } else {
        if (fn) fn(item);
      }
    });
  }
};

// 过滤 {} 空值
export const filterObjWithoutEmpty = (obj: any) => {
  const finalObj: any = {};
  const includeArray = ['number', 'boolean'];
  Object.keys(obj).forEach((item) => {
    if (obj[item] || includeArray.includes(typeof obj[item])) {
      finalObj[item] = obj[item];
    }
  });
  return finalObj;
};
//事件防抖
export function throttle(fn: Function, delay: number) {
  //事件上一次触发的时间
  let last = 0;
  //计时器
  let timer: NodeJS.Timeout | null = null;
  return function () {
    // 记录本次触发回调的时间
    let now = +new Date();
    // 判断上次触发的时间和本次触发的时间差是否小于传入得时间间隔delay
    if (now - last < delay) {
      // 如果间隔小于设定的时间间隔，重新计时
      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        last = now;
        fn.apply(React, arguments);
      }, delay);
    } else {
      //间隔超出立马触发
      last = now;
      fn.apply(React, arguments);
    }
  };
}

enum MaxOrMin {
  max = 'max',
  min = 'min',
  neither = 'neither',
}
//判断对象值大小
export function isMaxOrMin(arr: any[], obj: any, key: string = 'value'): MaxOrMin {
  // 获取数组中所有对象的value值
  const values = arr.map((item) => item[key]);

  // 获取最大值和最小值
  const max = Math.max(...values);
  const min = Math.min(...values);

  // 判断目标对象的value是否是最大或最小
  if (obj[key] === max) {
    return MaxOrMin.max; // 是最大值
  } else if (obj[key] === min) {
    return MaxOrMin.min; // 是最小值
  } else {
    return MaxOrMin.neither; // 既不是最大值也不是最小值
  }
}
//获取树节点某部分数据
export function findNodesByCodes({
  tree,
  codes,
  key,
}: {
  tree: any[];
  codes: string[];
  key: string;
}) {
  const result = [];
  const codeSet = new Set(codes);
  const stack = [];

  // 初始化栈
  if (Array.isArray(tree)) {
    // 如果是数组，倒序压入栈以保证处理顺序
    stack.push(...tree.slice().reverse());
  } else {
    stack.push(tree);
  }

  while (stack.length > 0) {
    const node = stack.pop();

    // 检查当前节点是否匹配
    if (codeSet.has(node[key])) {
      result.push(node);
    }

    // 如果有子节点，倒序压入栈以保证处理顺序
    if (node.children && node.children.length > 0) {
      stack.push(...node.children.slice().reverse());
    }
  }
  const newResult: any[] = [];
  result.forEach((item) => {
    //去掉重复key
    const keys = newResult.map((item) => item[key]);
    if (!keys.includes(item[key])) {
      newResult.push(item);
    }
  });
  return newResult;
}
//获取树节点某个数据
export function getTreeSingleData(
  list: any[],
  id: string,
  key: string = 'id',
  callBack?: (val: any) => boolean,
): any {
  let data: any = {};
  if (callBack) {
    list.find((item) => {
      if (callBack(item)) {
        //根据传入callBack条件判断是否返回值
        data = item;
        return true;
      } else {
        const cur = getTreeSingleData(item.children, id, key, callBack);
        if (cur[key]) {
          data = cur;
          return true;
        }
      }
    });
  } else {
    list.find((item) => {
      //根据传入key与id是否相等
      if (item[key] === id) {
        data = item;
        return true;
      } else if (item.children) {
        const cur = getTreeSingleData(item.children, id, key);
        if (cur[key]) {
          data = cur;
          return true;
        }
      }
    });
  }
  return data;
}
//数组排序-key:比较字段,type：True升序、False降序
export function listOrder(list: any[], key: string, type = false): any[] {
  let b;
  for (let i = 0; i < list.length; i++) {
    for (let j = 0; j <= i; j++) {
      if (list[i][key] < list[j][key] && type) {
        b = list[j];
        list[j] = list[i];
        list[i] = b;
      } else if (list[i][key] > list[j][key] && !type) {
        b = list[j];
        list[j] = list[i];
        list[i] = b;
      }
    }
  }
  return list;
}
//是否能被JSON.parse解析
export function isJsonString(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 将Excel列字母转换为数字（A=1, B=2, ..., Z=26, AA=27等）
function columnLetterToNumber(letters: string): number {
  let column = 0;
  const length = letters.length;
  for (let i = 0; i < length; i++) {
    column += (letters.charCodeAt(i) - 64) * Math.pow(26, length - i - 1);
  }
  return column;
}

// 校验Excel位置格式
export function validateExcelPosition(position: string): {
  column: number;
  row: number;
  valid: boolean;
} {
  const regex = /^[A-Za-z]{1,3}[1-9]\d*$/;
  if (!regex.test(position)) {
    return { column: 0, row: 0, valid: false };
  }
  const columnMath = position.match(/^[A-Za-z]+/);
  const rowMath = position.match(/\d+$/);
  // 分离列字母和行数字
  const columnPart = columnMath ? columnMath[0].toUpperCase() : '0';
  const rowPart = parseInt(rowMath ? rowMath[0] : '0');

  // 校验列号是否在Excel范围内 (A-XFD)
  if (columnPart.length > 3) return { column: 0, row: 0, valid: false };
  const columnNumber = columnLetterToNumber(columnPart);
  if (columnNumber > 16384 || columnNumber < 1) return { column: 0, row: 0, valid: false }; // XFD是16384列

  // 校验行号是否在Excel范围内 (1-1048576)
  if (rowPart > 1048576 || rowPart < 1) return { column: 0, row: 0, valid: false };

  return { column: columnNumber, row: rowPart, valid: true };
}

export interface IRange {
  startRow: number;
  endRow: number;
  startColumn: number;
  endColumn: number;
}
//判断日期字符串属于哪个季度
export function getQuarter(dateData: Date) {
  if (isNaN(dateData.getTime())) {
    throw new Error('Invalid date string');
  }

  const month = dateData.getMonth() + 1; // 月份从0开始，所以加1

  if (month >= 1 && month <= 3) {
    return '一季度';
  } else if (month >= 4 && month <= 6) {
    return '二季度';
  } else if (month >= 7 && month <= 9) {
    return '三季度';
  } else {
    return '四季度';
  }
}
//季度日期(YYYY/季度)转为正常日期格式
export function quarterStringToDate(quarterStr: string): Date {
  // 分割字符串获取年份和季度
  const [yearStr, quarter] = quarterStr.split('/');
  const year = parseInt(yearStr, 10);

  // 根据季度确定月份（季度第一个月）
  let month;
  switch (quarter) {
    case '一季度':
    case '1季度':
      month = 0; // 一月（JavaScript月份是0-11）
      break;
    case '二季度':
    case '2季度':
      month = 3; // 四月
      break;
    case '三季度':
    case '3季度':
      month = 6; // 七月
      break;
    case '四季度':
    case '4季度':
      month = 9; // 十月
      break;
    default:
      throw new Error('无效的季度格式');
  }

  // 创建日期对象（季度第一天）
  return new Date(year, month, 1);
}

// 将行列索引转换为Excel坐标（如 0,0 -> 'A1'）
export function toExcelCoordinate(row: number, col: number): string {
  let letter = '';
  while (col >= 0) {
    letter = String.fromCharCode(65 + (col % 26)) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter + (row + 1);
}
//将表格单元格范围对象转为单个单元格坐标
export function getRangeCells(range: any): string[] {
  const list: string[] = [];
  for (let r = 0; r < range.rowCount; r++) {
    for (let c = 0; c < range.colCount; c++) {
      const row = range.row + r;
      const col = range.col + c;
      const start = toExcelCoordinate(row, col);
      list.push(start);
    }
  }
  return list;
}
//判断两个单元格区域是否有交集
export function hasIntersection(range1: any, range2: any) {
  const r1StartRow = range1.row;
  const r1EndRow = range1.row + range1.rowCount - 1;
  const r1StartCol = range1.col;
  const r1EndCol = range1.col + range1.colCount - 1;
  const r2StartRow = range2.row;
  const r2EndRow = range2.row + range2.rowCount - 1;
  const r2StartCol = range2.col;
  const r2EndCol = range2.col + range2.colCount - 1;

  // 判断行是否有交集
  const rowsOverlap = r1EndRow >= r2StartRow && r1StartRow <= r2EndRow;

  // 判断列是否有交集
  const colsOverlap = r1EndCol >= r2StartCol && r1StartCol <= r2EndCol;

  // 行列都有交集时返回true
  return rowsOverlap && colsOverlap;
}
//将类似 "A1"、"B2" 这样的 Excel 样式坐标转换为数字行列索引（如 [0,0]、[1,1]）
export function excelCoordToIndices(excelCoord: string): number[] {
  // 分离字母部分和数字部分
  const matches = excelCoord.match(/^([A-Za-z]+)(\d+)$/);
  if (!matches) {
    throw new Error('无效的Excel坐标格式');
  }

  const colLetters = matches[1].toUpperCase();
  const rowNumber = parseInt(matches[2]);

  // 转换列字母为列索引(0-based)
  let colIndex = 0;
  for (let i = 0; i < colLetters.length; i++) {
    const charCode = colLetters.charCodeAt(i) - 65; // A=0, B=1, etc.
    colIndex = colIndex * 26 + (charCode + 1);
  }
  colIndex--; // 转换为0-based

  // 转换行号为行索引(0-based)
  const rowIndex = rowNumber - 1;

  return [rowIndex, colIndex];
}
//获取上个月日期
export function getLastMonthDate(time?: string) {
  const date = time ? new Date(time) : new Date();

  const currentMonth = date.getMonth();
  const currentYear = date.getFullYear();

  let lastMonth = currentMonth - 1;
  let year = currentYear;

  if (lastMonth < 0) {
    lastMonth = 11; // 12月
    year--;
  }
  return new Date(year, lastMonth, date.getDate());
}
