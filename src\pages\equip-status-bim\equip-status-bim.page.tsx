import { memo, useEffect, useRef, useState } from 'react';
import styles from './equip-status-bim.page.less';
import { LeftEquipInfo } from './components/left-equip-info/left-equip-info.component';
import { RightEquipInfo } from './components/right-equip-info/right-equip-info.component';
import { CenterEquipInfo } from './components/center-equip-info/center-equip-info.component';
import { socketConnect } from '@/utils/socket';
import { Client } from '@stomp/stompjs';
import { baseUnitTree } from '@/services/monitoring-point-annotation';
import { TreeOptions } from '../monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { LeftEquipModal } from './components/left-equip-modal/left-equip-modal.component';
import { PointOptions } from './equip-status-bim';
import { urlPrefix } from '@/utils/request';

//socket//生产：8017 url 游老板本地http://*************:8017
export const url =
  'http://' + window.location.hostname + ':8017' + urlPrefix + '/ws-service/register';
export let otherSocketClient: Client | undefined;
const DeviceStatusBim = () => {
  const [loading, setLoading] = useState<boolean>(true);
  //设备列表
  const [deviceList, setDeviceList] = useState<TreeOptions[]>([]);
  //socket实例
  const [socketClient, setSocketClient] = useState<Client>();
  //弹框控制
  const [modalController, setModalController] = useState<boolean>(false);
  //当前点击点位数据
  const [pointData, setPointData] = useState<PointOptions[]>([]);
  //点击标题
  const [title, setTitle] = useState<string>('');
  //单位
  const [unit, setUnit] = useState<string>('');
  useEffect(() => {
    socketConnectHandler();
    return () => {
      //关闭socket连接
      otherSocketClient && otherSocketClient.deactivate();
    };
  }, []);
  //socket连接处理
  function socketConnectHandler(): void {
    // 建立 WebSocket 连接并添加事件处理器
    socketConnect(url)
      .then((client) => {
        otherSocketClient = client;
        setSocketClient(client);
      })
      .catch((err) => {
        console.log('err', err);
      });
  }
  //获取设备图片
  function getDeviceImg(parentId: string): void {
    setLoading(true);
    baseUnitTree<TreeOptions[]>({ parentId })
      .then((res) => {
        if (res.code === '1') {
          setDeviceList(res.data);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //打开统计弹框
  function openModal(data: PointOptions[], title: string = '', unit: string): void {
    setPointData(data);
    setTitle(title);
    setUnit(unit);
    setModalController(true);
  }
  //关闭弹框
  function closeModal(): void {
    setModalController(false);
  }
  // 切换机组的tab
  function onTabChange(key: string): void {
    // 获取左侧对应机组的数据
    if (key) {
      getDeviceImg(key);
    }
  }
  return (
    <div className={styles['equip-status-bim-page-container']}>
      {/* 左侧设备信息 */}
      <LeftEquipInfo
        socketClient={socketClient}
        openModal={openModal}
        loading={loading}
        imgData={deviceList}
      />
      {/* 中间设备信息 */}
      <CenterEquipInfo
        socketClient={socketClient}
        openModal={openModal}
        onSelectTabKey={onTabChange}
      />
      {/* 右侧设备信息 */}
      {/* <RightEquipInfo loading={loading} imgData={[deviceList[4], deviceList[5], deviceList[6]]} /> */}
      {/* 弹框信息 */}
      <LeftEquipModal
        socketClient={socketClient}
        unit={unit}
        title={title}
        pointData={pointData}
        close={closeModal}
        open={modalController}
      />
    </div>
  );
};
export default memo(DeviceStatusBim);
