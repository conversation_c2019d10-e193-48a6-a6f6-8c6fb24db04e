import React, { FC, ReactNode, useEffect, useState } from 'react';
import styles from './summary-item.component.less';
import { Tabs } from 'antd';
interface Props {
  title: string;
  num: number;
  unit: string;
  icon: string;
}

const SummaryItem: FC<Props> = ({ title, icon, num, unit }) => {
  return (
    <div className={styles.summaryItem}>
      <div className={styles.left}>
        <img src={icon} />
      </div>
      <div className={styles.right}>
        <div className={styles.title}>{title}</div>
        <div className={styles.text}>
          <div className={styles.num}>{num ? num?.toFixed(2) : '-'}</div>
          <div className={styles.unit}>{unit}</div>
        </div>
      </div>
    </div>
  );
};

export default SummaryItem;
