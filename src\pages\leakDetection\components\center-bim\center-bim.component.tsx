import { ReactNode, memo } from 'react';
import styles from './center-bim.component.less';
import Icon from '@/assets/images/leakDetection/bim.png';
import { TableHeaderOptions, TableOptions } from './center-bim';
export const CenterBim = memo(() => {
  const tableList: TableOptions[] = [
    { id: 1, name: '1#水泵', status: 0, time1: 57, time2: 320, total1: 12, total2: 2 },
    { id: 2, name: '2#水泵', status: 0, time1: 50, time2: 335, total1: 17, total2: 1 },
    { id: 3, name: '3#水泵', status: 1, time1: 50, time2: 314, total1: 12, total2: 3 },
    { id: 4, name: '4#水泵', status: 2, time1: 19, time2: 39, total1: 6, total2: 1 },
    { id: 5, name: '5#水泵', status: 2, time1: 19, time2: 39, total1: 6, total2: 1 },
    { id: 6, name: '6#水泵', status: 2, time1: 19, time2: 39, total1: 6, total2: 1 },
  ];
  const TABLE_OPTIONS = [
    {
      label: '名称',
      value: 'name',
      render: ({ record }: TableHeaderOptions) => tableNameComponent(record),
    },
    {
      label: '本次运行时长',
      value: 'time1',
      render: ({ text }: TableHeaderOptions) => tableOtherComponent(text, 'h'),
    },
    {
      label: '累计运行时长',
      value: 'time2',
      render: ({ text }: TableHeaderOptions) => tableOtherComponent(text, 'h'),
    },
    {
      label: '运行次数',
      value: 'total1',
      render: ({ text }: TableHeaderOptions) => tableOtherComponent(text, '次'),
    },
    {
      label: '故障数',
      value: 'total2',
      render: ({ text }: TableHeaderOptions) => tableOtherComponent(text, '次'),
    },
  ];
  //表格其他字段模板
  function tableOtherComponent(text: string, unit: string): ReactNode {
    return (
      <span>
        <span className={styles['bim-paramters-table-value']}>{text}</span>
        <span className={styles['bim-paramters-table-unit']}>{unit}</span>
      </span>
    );
  }
  //表格name字段模板
  function tableNameComponent(record: TableOptions): ReactNode {
    const status = record.status === 0 ? '运行' : record.status === 1 ? '备用' : '停止';
    const statusStyle =
      record.status === 0 ? styles['bim-paramters-table-status-succes'] : undefined;
    return (
      <span className={styles['bim-paramters-table-button']}>
        <span className={styles['bim-paramters-table-name']}>{record.name}</span>
        <span className={statusStyle}>-{status}</span>
      </span>
    );
  }
  //bim参数表格模板
  function bimParamtersTableComponent(): ReactNode {
    const header = TABLE_OPTIONS.map((item) => <td key={item.value}>{item.label}</td>);
    return (
      <div className={styles['bim-paramters-table']}>
        <table>
          <thead>
            <tr>{header}</tr>
          </thead>
          <tbody>
            {tableList.map((item) => (
              <tr key={item.id}>
                {TABLE_OPTIONS.map((citem) => (
                  <td key={citem.value}>
                    {citem.render({ text: item[citem.value], record: item })}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }
  return (
    <section className={styles['center-bim']}>
      <div className={styles['bim-container']}>
        <img src={Icon} alt="" />
      </div>
      <div className={styles['bim-paramters']}>
        <div className={styles['bim-paramters-title']}>
          <span>水泵运行参数</span>
        </div>
        {bimParamtersTableComponent()}
      </div>
    </section>
  );
});
