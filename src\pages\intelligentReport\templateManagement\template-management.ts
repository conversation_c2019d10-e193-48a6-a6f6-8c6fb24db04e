export interface PageParams {
  pageNum: number;
  pageSize: number;
  startDate?: string;
  name: string;
  endDate?: string;
}
export interface TableOptions {
  name: string;
  type: string;
  remark: string;
  id: string;
  code: string;
  createTime: string;
  createUserId: string;
}
export const params: PageParams = {
  pageNum: 1,
  pageSize: 10,
  name: '',
  startDate: undefined,
  endDate: undefined,
};
//给基座发送产品更新消息
export function productUpdate(): void {
  window.parent.postMessage({
    type: 'product-update',
    url: window.location.pathname,
    id: Date.now(),
  });
}
