import { Client } from '@stomp/stompjs';
import { PointOptions } from '../../equip-status-bim';

export interface PropOptions {
  openModal: (pointData: PointOptions[]) => void;
  onSelectTabKey: (tabKey: string) => void;
  socketClient?: Client;
}
export interface PointParams {
  keyList: string[];
  selectBegin?: string;
  selectEnd?: string;
}
//模型id
//bim默认视角
export const homeCameraView = {
  position: [-51.001710916347704, -410.15387430303645, 612.5508940894038],
  target: [-187.81315095718696, -401.8783089005393, 570.0093875770043],
  up: [-0.2958913050440673, 0.017898122014912254, 0.9550539214241561],
  width: 143.49943303137655,
  height: 143.49943303137655,
};
//bim模型配置
export const defaultOptions = {
  toolbarVisible: {
    setting: false, // 设置按钮
    twoDDrawing: false, // 底部二维图纸钮
    floorPlan: false, // 楼层功能
    structureTree: false, // 模型结构树
    rightToolbar: false,
    bottomToolbar: false,
  },
  components: {
    compass: {
      visible: false,
    },
  },
};
//机组数据
export const deviceData = {
  name: '1号机组',
};
