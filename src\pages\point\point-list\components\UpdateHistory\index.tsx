import { Table, Badge, Drawer } from 'antd';
import { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/es/table';
import { getPointDiff, queryMeasurementPointDetails } from '@/pages/point/point.service';
import dayjs from 'dayjs';
import styles from './index.less';
import { Pointlist } from '@/pages/point/type';

interface Iprops {
  isHistoryModal: boolean;
  setIsHistoryModal: (is: boolean) => void;
}
interface PageInfo {
  pageNum: number;
  pageSize: number;
}

const setArrBgColor = (arr: any = []) => {
  if (arr.length > 0) {
    let a = '';
    let color = 'red';
    let newArr = arr.map((item: any) => {
      if (item.incrementalField === a) {
        return {
          ...item,
          color,
        };
      } else {
        a = item.incrementalField;
        if (color === 'red') {
          color = 'pink';
          return {
            ...item,
            color: 'pink',
          };
        } else {
          color = 'red';
          return {
            ...item,
            color: 'red',
          };
        }
      }
    });
    return newArr;
  }
};

// 通用表格分页配置的函数
const createPaginationConfig = (
  pageInfo: PageInfo,
  count: number,
  onPageChange: (pageNum: number, pageSize: number) => void,
) => {
  return {
    size: 'small' as const,
    current: pageInfo.pageNum,
    pageSize: pageInfo.pageSize,
    total: count,
    pageSizeOptions: [10, 20, 50, 100],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
    onChange: (pi: number, ps: number) => {
      onPageChange(pi, ps);
    },
  };
};
// 通用表格组件
const CommonTable = ({
  columns,
  dataSource,
  loading,
  rowClassName,
  pagination,
  rowKey = 'id',
}: {
  columns: ColumnsType<any>;
  dataSource: any[];
  loading?: boolean;
  rowClassName?: (record: any) => string;
  pagination: any;
  rowKey?: string | ((record: any) => string);
}) => {
  return (
    <Table
      size="small"
      columns={columns}
      dataSource={dataSource}
      rowClassName={rowClassName}
      loading={loading}
      rowKey={rowKey}
      scroll={{ y: window.innerHeight - 180 }}
      pagination={pagination}
    />
  );
};

const UpdateHistoryModal = (props: Iprops) => {
  const { isHistoryModal, setIsHistoryModal } = props;
  const [loading, setLoading] = useState(false);
  // 主表相关
  const [dataSource, setDataSource] = useState<Pointlist.SyncData['list']>([]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ pageNum: 1, pageSize: 10 });
  const [count, setCount] = useState(1);
  // 明细相关
  const [childrenDrawer, setChildrenDrawer] = useState(false);
  const [detailDataSource, setDetailDataSource] = useState<Pointlist.HistoricalDetails['list']>([]);
  const [detailCount, setDetailCount] = useState(0);
  const [detailPageInfo, setDetailPageInfo] = useState<PageInfo>({ pageNum: 1, pageSize: 10 });

  /** 列表表头配置 */
  const columns: ColumnsType<Pointlist.SyncData['list'][number]> = [
    {
      title: '时间',
      dataIndex: 'diffTime',
      render: (text) => {
        return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '类型',
      dataIndex: 'pointType',
    },
    {
      title: '新增测点数量',
      dataIndex: 'additionalPoint',
    },
    {
      title: '修改测点数量',
      dataIndex: 'modifyPoint',
    },
    {
      title: '删除测点数量',
      dataIndex: 'removePoint',
    },
    {
      title: '同步状态',
      dataIndex: 'version',
      render: (text) => {
        return (
          <Badge
            style={{ color: '#FFF' }}
            status={Number(text) === 1 ? 'success' : 'error'}
            text={Number(text) === 1 ? '成功' : '失败'}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => {
        return <a onClick={() => handleQueryDetail(record)}>查看明细</a>;
      },
    },
  ];

  const columnsDetail: ColumnsType<Pointlist.HistoricalDetails['list'][number]> = [
    {
      title: '名称',
      dataIndex: 'pointName',
      align: 'center',
    },
    {
      title: '编码',
      dataIndex: 'pointCode',
      align: 'center',
      width: 170,
    },
    {
      title: '类型',
      dataIndex: 'pointType',
      render: (text) => {
        switch (text) {
          case 'state':
            return '遥信';
          case 'analog':
            return '遥测';
          default:
            return '-';
        }
      },
      width: 80,
      align: 'center',
    },
    {
      title: '时间',
      dataIndex: 'entryTime',
      render: (text) => {
        return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
      width: 150,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'compilerState',
      render: (text) => {
        return (
          <Badge
            style={{ color: '#fff' }}
            status={text === '1' ? 'success' : text === '2' ? 'processing' : 'error'}
            text={text === '1' ? '新增' : text === '2' ? '修改' : '删除'}
          />
        );
      },
      width: 100,
      align: 'center',
    },
  ];
  /** 查询 */
  const submit = async (obj = {}) => {
    let query = {
      ...pageInfo,
      ...obj,
    };
    setLoading(true);
    const res = await getPointDiff(query);
    if (res.flag) {
      const colorList = setArrBgColor(res.data.list);
      const arr = colorList.sort((per: any, next: any) => next.diffTime - per.diffTime);
      setDataSource(arr);
      setCount(res.data.total);
    }
    setLoading(false);
  };

  /** 页面初始化 */
  useEffect(() => {
    isHistoryModal && submit();
  }, [isHistoryModal]);

  // 点击查询明细
  const handleQueryDetail = async (record: Pointlist.SyncData['list'][number]) => {
    const param = {
      compareId: record.incrementalField,
      pointType: record.pointType as 'analog' | 'state',
    };
    await handleDetail(param);
  };
  // 获取明细
  const handleDetail = async (params: {
    compareId: string;
    pointType: 'analog' | 'state';
    pageNum?: number;
    pageSize?: number;
  }) => {
    setLoading(true);
    const result = await queryMeasurementPointDetails(params);
    setChildrenDrawer(true);
    setDetailDataSource(result.data?.list || []);
    setDetailCount(Number(result?.data?.total) || 0);
    setLoading(false);
  };

  // 主表分页变化处理
  const handleMainPageChange = (pi: number, ps: number) => {
    setPageInfo({ pageNum: pi, pageSize: ps });
    submit({ pageNum: pi, pageSize: ps });
  };

  // 详情表分页变化处理
  const handleDetailPageChange = (pi: number, ps: number) => {
    setDetailPageInfo({ pageNum: pi, pageSize: ps });
    handleDetail({
      compareId: dataSource[0]?.incrementalField,
      pointType: dataSource[0]?.pointType as 'analog' | 'state',
      pageNum: pi,
      pageSize: ps,
    });
  };

  return (
    <Drawer
      open={isHistoryModal}
      onClose={() => setIsHistoryModal(false)}
      title="同步记录"
      destroyOnClose
      width={1200}
    >
      <CommonTable
        columns={columns}
        dataSource={dataSource}
        rowClassName={(record: any) => {
          return record.color === 'red' ? styles.classRed : styles.classPink;
        }}
        loading={loading}
        pagination={createPaginationConfig(pageInfo, count, handleMainPageChange)}
      />
      <Drawer
        width={1000}
        open={childrenDrawer}
        onClose={() => setChildrenDrawer(false)}
        title="同步明细"
        destroyOnClose
        closable={false} // 禁止关闭
      >
        <CommonTable
          columns={columnsDetail}
          dataSource={detailDataSource}
          loading={loading}
          pagination={createPaginationConfig(detailPageInfo, detailCount, handleDetailPageChange)}
        />
      </Drawer>
    </Drawer>
  );
};
export default UpdateHistoryModal;
