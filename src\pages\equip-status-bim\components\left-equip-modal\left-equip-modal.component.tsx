import { FC, ReactNode, memo, useEffect, useRef, useState } from 'react';
import styles from './left-equip-modal.component.less';
import { type DataOptions, type PropOptions, HistoryOptions } from './left-equip-modal';
import { TreeOptions as TreeOptionss } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { getPointHistoryData } from '@/services/equip-status-bim';
import { Form, Input } from 'antd';
import moment from 'moment';
import 'dayjs/locale/zh-cn';
import { CloseOutlined } from '@ant-design/icons';
import { PointModal } from './components/point-modal/point-modal.component';
import { useSyncCallback } from '@/hooks/useGetState';
import { LineChartModal } from '@/components/line-chart-modal/line-chart-modal.component';
import { modalSourceHead, subTheme, unsubTheme } from '../../equip-status-bim';
import { isJsonString } from '@/utils/utils';
import { rsaEncrypt } from '@/utils/jsencrypt.util';

let selectBegin = '';
let selectEnd = '';
let dataListSync: DataOptions[] = [];
let selectBeginDef = '';
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const LeftEquipModal: FC<PropOptions> = memo(
  ({ socketClient, unit, title, pointData, open, close }) => {
    //统计图数据加载控制
    const [loading, setLoading] = useState<boolean>(true);
    //对比测点弹框控制
    const [pointModalShow, setPointModalShow] = useState<boolean>(false);
    //统计图数据
    const [dataList, setDataList] = useState<DataOptions[]>([]);
    //右侧已选择对比测点列表
    const [chosePointList, setChosePointList] = useState<TreeOptionss[]>([]);
    //统计图弹框实例
    const lineChartRef = useRef<any>(null);
    //socket初始化
    const [socketInit, setSokcetInit] = useState<boolean>(false);
    const [forms] = Form.useForm();
    //同步化处理，防止视图加载完成后，没有获取到最新的hooks
    const getHistoryDataSync = useSyncCallback(getHistoryData);
    const subMsgSync = useSyncCallback(subMsg);
    useEffect(() => {
      if (pointData.length > 0) {
        modalDefDataHandler();
      }
    }, [pointData]);
    //打开弹框获取默认数据
    function modalDefDataHandler(): void {
      dataListSync = [];
      //默认获取前一小时内的数据
      const startDate = moment().subtract(60, 'minutes').format('YYYY-MM-DD HH:mm:ss');
      selectBeginDef = startDate;
      selectBegin = startDate;
      selectEnd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      getDefOneHourseData();
    }
    //默认数据,然后拼接ws数据
    function getDefOneHourseData(): void {
      setLoading(true);
      historyDataHttp()
        .then((res) => {
          if (res.code === '1') {
            const list = historyDataHandler(res.data);
            dataListSync = JSON.parse(JSON.stringify(list));
            if (Object.keys(dataListSync).length > 0) {
              setLoading(false);
            }
            subMsgSync(false);
          }
        })
        .catch((err) => {
          setLoading(false);
        });
    }
    //取消订阅
    function unsubMsg(ids?: string[]): void {
      if (selectBegin === '' || selectBeginDef === selectBegin) {
        let pointCodes = [];
        if (ids) {
          pointCodes = ids;
        } else {
          pointCodes = historyDataIdsHandler();
        }
        //消息体
        const msgBody = {
          //body只接受字符串数据
          body: JSON.stringify({
            pointCodes: pointCodes.join(','),
            userId,
            topicType: modalSourceHead,
          }),
          destination: unsubTheme,
          headers: {
            Authorization: rsaEncrypt(unsubTheme).toString(),
          },
        };
        // 发送消息
        socketClient && socketClient.publish(msgBody);
      }
    }
    //订阅测点数据
    function subMsg(isReSet: boolean = true): void {
      if (selectBegin === '' || selectBeginDef === selectBegin) {
        if (isReSet) {
          setLoading(true);
          setDataList([]);
          dataListSync = [];
        }
        const pointCodes = historyDataIdsHandler();
        //消息体
        const msgBody = {
          //body只接受字符串数据
          body: JSON.stringify({
            pointCodes: pointCodes.join(','),
            userId,
            topicType: modalSourceHead,
          }),
          destination: subTheme,
          headers: {
            Authorization: rsaEncrypt(subTheme).toString(),
          },
        };
        // 接受返回消息
        socketInit === false && messageReturn(modalSourceHead + userId);
        // 发送消息
        socketClient && socketClient.publish(msgBody);
        messageTimeout();
      }
    }
    //订阅超时监测
    function messageTimeout(): void {
      let time = setTimeout(() => {
        setLoading(false);
        clearTimeout(time);
      }, 8000);
    }
    //订阅消息返回
    function messageReturn(soucre: string) {
      if (socketClient) {
        socketClient.subscribe(soucre, function (message: any) {
          const socketData = message.body;
          if (isJsonString(socketData)) {
            setSokcetInit(true);
            const { key, value, time_stamp, name } = JSON.parse(socketData);
            dataListSync = dataListSync.concat({
              label: time_stamp.substr(0, 19),
              value: value,
              type: name.replace(/^.*_/, '') || name,
              key,
            });
            setDataList(dataListSync);
            setLoading(false);
          }
        });
      }
    }
    //历史数据请求id处理
    function historyDataIdsHandler(): string[] {
      if (chosePointList.length > 0) {
        return chosePointList.map((item) => item.pointCode);
      } else {
        return pointData.map((item) => item.id);
      }
    }
    //历史数据请求
    function historyDataHttp(): Promise<any> {
      let ids = historyDataIdsHandler();
      return getPointHistoryData({ keyList: ids, selectBegin, selectEnd });
    }
    //获取历史数据
    async function getHistoryData(): Promise<any> {
      setLoading(true);
      historyDataHttp()
        .then((res) => {
          if (res.code === '1') {
            historyDataHandler(res.data);
          }
          setLoading(false);
        })
        .catch((err) => {
          setLoading(false);
        });
    }
    //处理接口历史数据
    function historyDataHandler(response: any): DataOptions[] {
      const list = Object.keys(response);
      if (list.length > 0) {
        let data: DataOptions[] = [];
        list.forEach((item, index: number) => {
          const node = pointData.find((fitem) => fitem.id === item);
          const name = node ? node.name : '点' + (index + 1);
          response[Number(item)].forEach((citem: HistoryOptions, cindex: number) => {
            data.push({
              label: moment(citem.time).format('YY-MM-DD HH:mm:ss'),
              value: citem.value,
              type: name,
            });
          });
        });
        setDataList(data);
        return data;
      } else {
        setDataList([]);
        return [];
      }
    }
    //弹框关闭
    function closeHandler(): void {
      unsubMsg();
      forms.resetFields();
      setChosePointList([]);
      close();
    }
    //对比测点改变
    function pointChange(list: TreeOptionss[]): void {
      unsubMsg();
      setPointModalShow(false);
      setChosePointList(list);
      if (selectBegin && selectBeginDef !== selectBegin) {
        getHistoryDataSync();
      } else {
        subMsgSync();
      }
    }
    //删除对比测点
    function delPoint(code: string): void {
      const list = chosePointList.filter((item) => item.pointCode !== code);
      const node = chosePointList.find((item) => item.pointCode === code);
      unsubMsg([code]);
      delPointDataList(node);
      setChosePointList(list);
      if (selectBegin && selectBeginDef !== selectBegin) {
        getHistoryDataSync();
      } else if (list.length === 0) {
        modalDefDataHandler();
      }
    }
    //删除统计中已被删除测点的数据
    function delPointDataList(data?: TreeOptionss): void {
      if (data) {
        dataListSync = dataListSync.filter((item) => item.key !== data.pointCode);
        setDataList(dataListSync);
      }
    }
    //表单查询
    function query(params?: any): void {
      if (params && params.selectBegin && params.selectEnd) {
        unsubMsg();
        selectBegin = params.selectBegin;
        selectEnd = params.selectEnd;
        getHistoryData();
      } else {
        selectBegin = '';
        selectEnd = '';
        modalDefDataHandler();
      }
    }
    //右侧对比测点模板
    function pointListComponent(): ReactNode {
      return (
        <>
          <div className={styles['point-list-title']}>
            <span>对比测点</span>
            <type-button onClick={() => setPointModalShow(true)}>选择</type-button>
          </div>
          {chosePointList.map((item) => {
            return (
              <div key={item.pointCode} className={styles['point-list-item']}>
                <span title={item.pointName}>{item.pointName}</span>
                <CloseOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    delPoint(item.pointCode);
                  }}
                  className={styles['point-list-item-del']}
                />
              </div>
            );
          })}
        </>
      );
    }
    return (
      <>
        <LineChartModal
          queryFn={query}
          resetFn={query}
          pointComponent={pointListComponent()}
          chartData={dataList}
          width="1360px"
          unit={unit}
          loading={loading}
          height="600px"
          close={closeHandler}
          open={open}
          title={title}
          ref={lineChartRef}
        >
          {/* 对比测点选择弹框 */}
          <PointModal
            onOk={pointChange}
            close={() => setPointModalShow(false)}
            open={pointModalShow}
          ></PointModal>
        </LineChartModal>
      </>
    );
  },
);
