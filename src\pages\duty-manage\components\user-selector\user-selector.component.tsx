import { FC, useRef, useEffect, useState } from 'react';
import { Button } from 'antd';
import styles from './user-selector.component.less';

interface UserSelectorComponentProps {
  callback?: (users: string[]) => void;
}
const UserSelectorComponent: FC<UserSelectorComponentProps> = ({ callback }) => {
  const userRef = useRef<any>();
  const [users, setUsers] = useState<string[]>([]); // 用户id
  const [userOpen, setUserOpen] = useState<boolean>(false); // 是否打开

  useEffect(() => {
    if (userRef.current) {
      userRef.current?.addEventListener('select', userSelect);
      userRef.current?.addEventListener('close', userClose);
    }
    return () => {
      userRef.current?.removeEventListener('select', userSelect);
      userRef.current?.removeEventListener('close', userClose);
    };
  }, []);

  /**
   * 用户选择
   * @param val
   */
  function userSelect(val: any) {
    const ids = val.detail.map((item: any) => item.id);
    setUsers(ids);
    callback?.(ids);
    setUserOpen(false);
  }

  /**
   * 用户关闭
   */
  function userClose() {
    setUserOpen(false);
  }
  return (
    <div className={styles['user-selector-component']}>
      <Button onClick={() => setUserOpen(true)}>选择人员</Button>
      <user-selector open={userOpen} values={users} ref={userRef} titles="用户选择" />
    </div>
  );
};
export default UserSelectorComponent;
