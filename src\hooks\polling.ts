import { useState, useEffect, useRef, useCallback } from 'react';

interface UsePollingOptions<T> {
  initialData?: T;
  interval?: number;
  immediate?: boolean;
  autoStart?: boolean;
  retryCount?: number;
  retryInterval?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

/**
 * 通用轮询请求API的hooks
 * <AUTHOR>
 * @param fetchFn 请求函数，返回Promise
 * @param options 轮询配置选项
 * @returns 包含数据、加载状态、错误信息和控制方法的对象
 */
export function usePolling<T>(fetchFn: () => Promise<T>, options: UsePollingOptions<T> = {}) {
  const {
    initialData, // 初始数据
    interval = 5000, // 轮询间隔时间（毫秒）
    immediate = true, // 是否立即开始轮询
    autoStart = true, // 是否在组件挂载时自动开始轮询
    retryCount = 3, // 轮询失败时的重试次数
    retryInterval = 1000, // 轮询失败时的重试间隔（毫秒）
    onSuccess, // 请求成功的回调
    onError, // 请求失败的回调
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [isPolling, setIsPolling] = useState<boolean>(autoStart);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef<number>(0);
  const mountedRef = useRef<boolean>(false);

  // 清除定时器
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 执行请求
  const fetchData = useCallback(async () => {
    if (!isPolling) return;
    setLoading(true);
    setError(null);

    try {
      const result = await fetchFn();
      if (mountedRef.current) {
        setData(result);
        setLoading(false);
        retryCountRef.current = 0;
        onSuccess?.(result);
      }
    } catch (err) {
      if (mountedRef.current) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        setLoading(false);
        onError?.(error);

        // 处理重试逻辑
        if (retryCountRef.current < retryCount) {
          retryCountRef.current += 1;
          timerRef.current = setTimeout(() => {
            if (mountedRef.current && isPolling) {
              fetchData();
            }
          }, retryInterval);
          return;
        }
      }
    }

    // 设置下一次轮询
    if (mountedRef.current && isPolling) {
      timerRef.current = setTimeout(fetchData, interval);
    }
  }, [fetchFn, interval, isPolling, retryCount, retryInterval, onSuccess, onError]);

  // 开始轮询
  const startPolling = useCallback(() => {
    setIsPolling(true);
  }, []);

  // 停止轮询
  const stopPolling = useCallback(() => {
    setIsPolling(false);
    clearTimer();
  }, [clearTimer]);

  // 手动触发一次请求
  const refresh = useCallback(() => {
    clearTimer();
    fetchData();
  }, [clearTimer, fetchData]);

  // 处理轮询状态变化
  useEffect(() => {
    if (isPolling) {
      if (immediate) {
        fetchData();
      } else {
        timerRef.current = setTimeout(fetchData, interval);
      }
    }

    return () => clearTimer();
  }, [isPolling, fetchData, interval, immediate, clearTimer]);

  // 组件挂载和卸载处理
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      clearTimer();
    };
  }, [clearTimer]);

  return {
    data,
    loading,
    error,
    isPolling,
    startPolling,
    stopPolling,
    refresh,
  };
}
