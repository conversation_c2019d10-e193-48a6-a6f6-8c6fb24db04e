import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
import { ColorAttr } from '@antv/g2plot';

export interface PropOptions {
  ref?: any;
  loading?: boolean; //加载控制
  unit?: [string, string]; //统计图单位
  unitY?: number;
  padding?: number[] | 'auto';
  appendPadding?: number | number[] | undefined;
  geometryOptions?: any;
  sliderColor?: string; //滚动条颜色
  legend?: boolean;
  chartData: [DataOptions[], DataOptions[]]; //统计图数据
}
