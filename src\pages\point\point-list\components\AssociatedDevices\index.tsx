import { Modal, Table, Form, Button, Input, message } from 'antd';
import { useEffect, useState } from 'react';
import type { TableColumnsType } from 'antd';
import { getDeviceList, addDevice } from '../../../point.service';
import {
  addNode,
  findAndInsertRoot,
} from '@/pages/point/point-show/tab-content/topology-content/topology-content.service';

interface Iprops {
  current: any;
  isDevicesModal: boolean;
  setIsDevicesModal: (is: boolean) => void;
  onOk: () => void;
}

interface DataType {
  id: string;
  key: React.Key;
  name: string;
  age: number;
  address: string;
}

const AssociatedDevices = (props: Iprops) => {
  const { isDevicesModal, setIsDevicesModal, current, onOk } = props;
  const [form] = Form.useForm();
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState('');
  const [pageInfo, setPageInfo] = useState({ pageNum: 1, pageSize: 10 });
  const [checkeds, setCheckeds] = useState<any[]>([]);

  const columns: TableColumnsType<DataType> = [
    {
      title: '设备名称',
      dataIndex: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: '设备编码',
      dataIndex: 'kks',
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
    },
    {
      title: '设备类别',
      dataIndex: 'type',
    },
    {
      title: '专业类别',
      dataIndex: 'field',
    },
    {
      title: '安装地点',
      dataIndex: 'loctionInstall',
    },
  ];

  //获取列表数据
  const getDeviceListAPI = (obj = {}) => {
    let params = {
      ...form.getFieldsValue(),
      ...pageInfo,
      ...obj,
    };
    getDeviceList(params)
      .then((res) => {
        if (res?.status === 200) {
          setData(res.data.list);
          setTotal(res.data.total);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  //添加/关联设备
  const addDeviceAPI = (obj = {}) => {
    let params = {
      id: current?.id,
      pointCode: current?.pointCode,
      deviceId: Number(checkeds[0].id),
      ...obj,
    };
    addDevice(params)
      .then((res) => {
        if (res.status === 200) {
          message.success('关联成功');
          setIsDevicesModal(false);
          onOk();

          /* branch logic */
          // 查询（新增）测点节点
          findAndInsertRoot({
            businessId: Number(current.pointCode),
            deviceType: '电厂测点',
            name: current.pointName,
          }).then(({ data }) => {
            const currentUUID = data.nodeList.find(
              (node: any) => node.currentId === current.pointCode,
            )!.uuid;
            // 添加拓扑图节点
            addNode({
              currentId: params.deviceId, // 设备id
              relationUUID: currentUUID, // 中心节点id
              deviceType: '设备',
              name: checkeds[0].name, // 设备名称
              distance: 10,
            });
          });
        }
      })
      .catch((err) => {
        console.log(err, '添加失败');
      });
  };

  useEffect(() => {
    if (isDevicesModal) {
      getDeviceListAPI();
    }
  }, [isDevicesModal]);
  return (
    <Modal
      title="设备绑定"
      width="1200px"
      height="700px"
      open={isDevicesModal}
      onOk={() => {
        addDeviceAPI();
      }}
      onCancel={() => {
        setIsDevicesModal(false);
      }}
    >
      <Form
        layout="inline"
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        labelWrap
        colon={false}
      >
        <Form.Item name="name" label="搜索">
          <Input placeholder="请输入设备名称" />
        </Form.Item>
        <Form.Item name="specification" label="规格型号">
          <Input placeholder="请输入" />
        </Form.Item>
        <Form.Item name="type" label="设备类别">
          <Input placeholder="请输入" />
        </Form.Item>
        <Form.Item>
          <Button
            onClick={() => {
              getDeviceListAPI({ ...form.getFieldsValue() });
            }}
          >
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button
            onClick={() => {
              form.resetFields();
              getDeviceListAPI();
            }}
          >
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table<DataType>
        rowSelection={{
          type: 'radio',
          onChange: (_, dataList) => {
            setCheckeds(dataList as any[]);
            // addDeviceAPI({
            //   deviceId: Number(list[0]),
            // });
          },
        }}
        // scroll={{ y: window.innerHeight - 220 }}
        scroll={{ y: 500 }}
        rowKey={(record) => record.id}
        columns={columns}
        dataSource={data}
        pagination={{
          size: 'small',
          current: pageInfo.pageNum,
          pageSize: pageInfo.pageSize,
          total: Number(total),
          pageSizeOptions: [10, 20, 50, 100],
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: (pi, ps) => {
            setPageInfo({ pageNum: pi, pageSize: ps });
            getDeviceListAPI({ pageNum: pi, pageSize: ps });
          },
        }}
      />
    </Modal>
  );
};
export default AssociatedDevices;
