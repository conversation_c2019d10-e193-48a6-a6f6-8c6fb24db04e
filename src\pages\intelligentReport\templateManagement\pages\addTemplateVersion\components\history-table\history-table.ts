import {
  CellControllerOptions,
  DataTypeEnum,
  RegionsOptions,
} from '@/components/spread-table/spread-table';
import { UseImperativeOptions as TableUseImperativeOptions } from '@/components/spread-table/spread-table';
import { PlanTypeEnum } from '@/services/intelligentReport/report-plan';
export interface PropOptions {
  allTableList: RegionsOptions[]; //全部列表
  tableList: RegionsOptions[]; //当前tab列表
  otherTableList: CellControllerOptions[]; //包含其他列表得数据,用于校验重复
  tableListChange: React.Dispatch<React.SetStateAction<RegionsOptions[]>>;
  isEdit: boolean;
  spreadRef: React.MutableRefObject<TableUseImperativeOptions>;
  ref: any;
  inputFocusFn: (val: CellControllerOptions, type: DataTypeEnum.HISTORY) => void; //输入框聚焦
}

export interface SelectOptions {
  label: string;
  key: string;
}
export const planCycleList = [
  {
    label: '去年同期月',
    key: PlanTypeEnum.YEAR,
  },
  {
    label: '去年同期年',
    key: PlanTypeEnum.QUARTER,
  },
  {
    label: '上月',
    key: PlanTypeEnum.MONTH,
  },
];
export interface UseImperativeOptions {
  addHistoryRange: () => void;
  tableHistoryEvent: () => void;
}
