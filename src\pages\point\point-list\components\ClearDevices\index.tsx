import { Mo<PERSON>, <PERSON>, But<PERSON>, Popconfirm, message } from 'antd';
import { useEffect, useState } from 'react';
import type { TableColumnsType } from 'antd';
import { getPointDetails, deleteDevice } from '@/pages/point/point.service';
import {
  deleteEdge,
  findAndInsertRoot,
} from '@/pages/point/point-show/tab-content/topology-content/topology-content.service';

interface Iprops {
  current: any;
  clearModal: boolean;
  setClearModal: (is: boolean) => void;
  setIsDevicesModal: (is: boolean) => void;
  onOk: () => void;
}

interface DataType {
  id: string;
  key: React.Key;
  name: string;
  age: number;
  address: string;
}

const ClearDevices = (props: Iprops) => {
  const { clearModal, setClearModal, current, setIsDevicesModal, onOk } = props;
  const [data, setData] = useState<any[]>([]);
  const [checkeds, setCheckeds] = useState<string[]>([]);
  const columns: TableColumnsType<DataType> = [
    {
      title: '设备名称',
      dataIndex: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: '设备编码',
      dataIndex: 'kks',
    },
    {
      title: '规格型号',
      dataIndex: 'specification',
    },
    {
      title: '设备类别',
      dataIndex: 'type',
    },
    {
      title: '专业类别',
      dataIndex: 'field',
    },
    {
      title: '安装地点',
      dataIndex: 'loctionInstall',
    },
    {
      title: '操作',
      dataIndex: '',
      render: (_text, record) => {
        return (
          <Popconfirm
            title="提示"
            description="您确定要取消关联吗"
            onConfirm={() => deleteDeviceAPI({ deviceId: record?.id })}
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button danger>取消关联</Button>
          </Popconfirm>
        );
      },
    },
  ];

  // 获取列表数据
  const getDeviceListAPI = () => {
    getPointDetails(current?.id)
      .then((res) => {
        if (res?.status === 200) {
          setData(res.data.deviceResumes);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 取消关联设备/解除绑定
  const deleteDeviceAPI = (obj = {}) => {
    let params = {
      pointCode: current?.pointCode,
      deviceId: Number(checkeds[0]),
      ...obj,
    };

    deleteDevice(params)
      .then((res) => {
        if (res.status === 200) {
          message.success('解除关联成功');
          setClearModal(false);
          if (data.length === 1) {
            setIsDevicesModal(true);
          }
          onOk();

          /* branch logic */
          // 取消关联
          findAndInsertRoot({
            businessId: Number(current.pointCode),
            deviceType: '电厂测点',
            name: current.pointName,
          }).then(({ data }) => {
            const currentUUID = data.nodeList.find(
              (node: any) => node.currentId === current.pointCode,
            )!.uuid;
            const deviceUUID = data.nodeList.find(
              (node: any) => node.currentId === params.deviceId,
            )!.uuid;
            // 删除节点连线
            deleteEdge({
              startId: currentUUID,
              endId: deviceUUID,
            });
          });
        }
      })
      .catch((err) => {
        console.log(err, '解除失败');
      });
  };

  useEffect(() => {
    if (clearModal) {
      getDeviceListAPI();
    }
  }, [clearModal, current]);
  return (
    <Modal
      title="解除测点关联设备"
      width="1200px"
      open={clearModal}
      onOk={() => {
        deleteDeviceAPI();
      }}
      onCancel={() => {
        setClearModal(false);
      }}
      footer={null}
    >
      <Table<DataType>
        scroll={{ y: 500 }}
        rowKey={(record) => record.id}
        columns={columns}
        dataSource={data}
        pagination={false}
      />
    </Modal>
  );
};
export default ClearDevices;
