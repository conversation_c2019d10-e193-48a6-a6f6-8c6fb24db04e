import { memo } from 'react';
import styles from './chart-statistics.component.less';
import { IntervalDurationChart } from './components/Interval-duration-chart/Interval-duration-chart.component';
import { LiquidLevelChart } from './components/liquid-level-chart/liquid-level-chart.component';

export const ChartStatistics = memo(() => {
  return (
    <section className={styles['chart-statistics-container']}>
      <LiquidLevelChart></LiquidLevelChart>
      <IntervalDurationChart></IntervalDurationChart>
    </section>
  );
});
