import { FC, ReactNode, memo, useEffect, useRef, useState } from 'react';
import styles from './center-equip-info.component.less';
import { EquipTypeTab } from '../equip-type-tab/equip-type-tab.component';
import { getModelInfo } from '@/services/monitoring-point-annotation';
import { defaultOptions, homeCameraView } from './center-equip-info';
import { EquipOptions, TAB_LIST, TabOptinos, equipStatus } from '../equip-type-tab/equip-type-tab';
import { Statistic } from 'antd';
import { getDeviceStatus, getNodeTree, pointDataList } from '@/services/equip-status-bim';
import { getTreeSingleData, isJsonString } from '@/utils/utils';
import { centerSourceHead, subTheme, unsubTheme } from '../../equip-status-bim';
import { rsaEncrypt } from '@/utils/jsencrypt.util';

let bimDom: any = null;
let bimView: any = null; // 模型视图
let polylineSeg: any = null; //线条实例
let otherModel: any = null;
let equipStatusTimeout: NodeJS.Timeout; //机组状态数据计时器
let nodeId: string = ''; //节点id
let equipData: TabOptinos; //机组数据
let curIds: string[] = []; //当前监听id
const lineColor: number[] = [240, 130, 40, 1]; //线条颜色
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const CenterEquipInfo: FC<any> = memo(({ socketClient, openModal, onSelectTabKey }) => {
  const [tabData, setTabData] = useState<TabOptinos[]>(TAB_LIST); //构件对象
  const [lightModel, setLightModel] = useState<any>(); //构件对象
  const [tabKey, setTabKey] = useState<string>(''); //机组tab
  const [loading, setLoading] = useState<boolean>(true); //加载控制
  const [equipStatus, setEquipStatus] = useState<equipStatus[]>([]); //机组状态
  const [socketInit, setSokcetInit] = useState<boolean>(false); //socket初始化
  const parentRef = useRef<any>(null);
  useEffect(() => {
    getModelInfos();
    return () => {
      bimDom = null;
      bimView = null;
      unsubMsg();
      if (equipStatusTimeout) {
        clearInterval(equipStatusTimeout);
      }
    };
  }, []);
  useEffect(() => {
    if (bimView && lightModel) {
      drawPolyLine();
      equipStatusTimeoutHandler();
    }
  }, [lightModel]);
  //获取模型数据
  function getModelInfos(): void {
    getNodeTree({}).then((res) => {
      if (res.code === 200) {
        const data = getTreeSingleData(res.data.data, 'jizu', 'code');
        getModelData(data.id);
      }
    });
  }
  //设置bim转动状态
  function setBimRoateStatus(data: equipStatus) {
    if (data && data.status === 1) {
      //开始转动
      appointComponentRoate();
    } else {
      //停止转动
      bimView.animationManager.stop();
    }
  }
  //获取机组转动状态,发电态=1时开始转动
  function getEquipStatus(): void {
    const keyList = TAB_LIST.map((item) => item.startId);
    getDeviceStatus({
      keyList,
    }).then((res) => {
      if (res.code === '1') {
        const data: equipStatus[] = keyList.map((item, index) => {
          return {
            id: item,
            status: res.data[Number(item)], //转动状态
          };
        });
        const curStatus = data.find((item) => item.id === equipData?.startId);
        setEquipStatus(data);
        if (curStatus) {
          setBimRoateStatus(curStatus);
        }
      }
    });
  }
  //获取点位id
  function getPointIds(key?: string): string[] {
    let ids: string[] = [];
    let data: TabOptinos[] = [];
    if (key) {
      const node = TAB_LIST.find((item) => item.value === key);
      if (node) {
        data = [node];
      }
    } else {
      data = TAB_LIST;
    }
    data.forEach((item) => {
      item.leftEquipData.forEach((citem, index) => {
        citem.forEach((sitem) => {
          ids.push(sitem.id);
        });
      });
      item.rightEquipData.forEach((citem, index) => {
        citem.forEach((sitem) => {
          ids.push(sitem.id);
        });
      });
    });
    return ids;
  }
  //订阅消息返回
  function messageReturn(soucre: string, keys: string) {
    if (socketClient) {
      socketClient.subscribe(soucre, function (message: any) {
        const socketData = message.body;
        if (isJsonString(socketData)) {
          setSokcetInit(true);
          const list = tabData.map((item) => {
            const { key, value } = JSON.parse(socketData);
            const code = new RegExp(/\d+/g).exec(key);
            if (item.value == keys) {
              item.leftEquipData.some((citem) => {
                return citem.some((sitem) => {
                  if (code && sitem.id === code[0]) {
                    sitem.value = value;
                    return true;
                  }
                });
              });
              item.rightEquipData.some((citem) => {
                return citem.forEach((sitem) => {
                  if (code && sitem.id === code[0]) {
                    sitem.value = value;
                    return true;
                  }
                });
              });
            }
            return item;
          });
          setTabData(list);
        }
        setLoading(false);
      });
    }
  }
  //取消订阅
  function unsubMsg(): void {
    //消息体
    const msgBody = {
      //body只接受字符串数据
      body: JSON.stringify({
        pointCodes: curIds.join(','),
        userId,
        topicType: centerSourceHead,
      }),
      destination: unsubTheme,
      headers: {
        Authorization: rsaEncrypt(unsubTheme).toString(),
      },
    };
    // 发送消息
    socketClient && socketClient.publish(msgBody);
  }
  //订阅测点数据
  function subMsg(key: string): void {
    const pointCodes = getPointIds(key);
    curIds = pointCodes;
    //消息体
    const msgBody = {
      //body只接受字符串数据
      body: JSON.stringify({
        pointCodes: pointCodes.join(','),
        userId,
        topicType: centerSourceHead,
      }),
      destination: subTheme,
      headers: {
        Authorization: rsaEncrypt(subTheme).toString(),
      },
    };
    // 接受返回消息
    socketInit === false && messageReturn(centerSourceHead + userId, key);
    // 发送消息
    socketClient && socketClient.publish(msgBody);
  }
  //指定转动构件
  function appointComponentRoate(): void {
    modelRoateHandler(nodeId, '8326');
    modelRoateHandler(nodeId, '98');
    modelRoateHandler(nodeId, '97');
    modelRoateHandler(nodeId, '95');
    modelRoateHandler(nodeId, '99');
    modelRoateHandler(nodeId, '100');
    modelRoateHandler(nodeId, '101');
    modelRoateHandler(nodeId, '102');
  }
  //获取指定模型数据
  function getModelData(id: string): void {
    getModelInfo(id).then((res) => {
      if (res.code == '200') {
        nodeId = res.data.data[0].bimModelId;
        const ids = res.data.data.map((item: any) => item.bimModelId);
        modelViewCreate(ids);
      }
    });
  }
  //模型视图创建
  function modelViewCreate(ids: string[]): void {
    const bim = document.getElementById('bim-wrapper');
    if (bim) {
      bimDom = bim;
      bimDom.options = defaultOptions;
      bimDom.createdViewerRef = handleOnCreateTopBimRef;
      bimDom.modelIds = ids; //挂载模型到div上
      bimDom.modelOnLoaded = handleModelOnLoaded;
      bimDom.partOnMouseWheel = bomMouseWheel;
      bimDom.partOnRightMouseMove = bomMouseWheel;
    }
  }
  //绘制线条
  function drawPolyLine(): void {
    polylineSeg = bimView.utilitySegment.subsegment('polylineSeg');
    //线条：x1,y1,z1—x2,y2,z2
    polylineSeg.insertPolyline([-135, -411, 601, -145, -418, 598.5, -145, -433, 598.5]);
    polylineSeg.insertPolyline([-119, -411, 600.5, -110, -415, 597.5, -110, -425, 597.5]);
    polylineSeg.insertPolyline([-119, -410.5, 600, -110, -415, 592.5, -110, -425.5, 592.5]);
    polylineSeg.insertPolyline([-119, -409, 595.5, -110, -415, 585, -110, -426, 585]);
    polylineSeg.insertPolyline([-124, -407.5, 586.5, -110, -415, 577.5, -110, -427, 577.5]);

    polylineSeg.insertPolyline([-123, -405, 601.5, -123, -376, 601]);
    polylineSeg.insertPolyline([-123, -404.5, 596, -123, -396, 598, -123, -375.5, 597.8]);
    polylineSeg.insertPolyline([-123, -404.5, 593.5, -123, -396, 592.5, -123, -375, 592.3]);
    polylineSeg.insertPolyline([-123, -404.5, 588.5, -123, -396, 587, -123, -374.5, 586.7]);
    polylineSeg.insertPolyline([-123, -398, 583.3, -123, -374, 583]);
    polylineSeg.insertPolyline([-123, -404.5, 585, -123, -396, 579.5, -123, -373.5, 579.3]);
    polylineSeg.insertPolyline([-123, -402, 579, -123, -396, 575.8, -123, -373, 575.3]);
    polylineSeg.insertPolyline([-123, -404, 574, -123, -396, 572, -123, -372.5, 571.5]);
    polylineSeg.color.setLine(lineColor);
    bimView.updateDisplay();
  }
  //bim鼠标滚动事件
  function bomMouseWheel(segment: any, event: any) {
    //删除之前的线条
    polylineSeg.delete();
    bimView.updateDisplay();
  }
  //设置bim默认视角
  function setBimDefaultView(): void {
    bimView.camera.position = homeCameraView.position;
    bimView.camera.target = homeCameraView.target;
    bimView.camera.up = homeCameraView.up;
    bimView.camera.setField(homeCameraView.width, homeCameraView.height);
    bimView.updateDisplay();
    drawPolyLine();
  }
  // 加载完模型获取视图
  function handleOnCreateTopBimRef(viewer: any): void {
    bimView = viewer;
    setTimeout(() => {
      //设置延迟，等待构件显示出来再进行操作,不然操作无效
      setBimDefaultView();
    }, 1000);
  }
  //模型旋转处理,模型id，构建id
  function modelRoateHandler(modelId: string, componentId: string): void {
    let component = bimView.getComponent(modelId, componentId);
    //边界数据
    let bb = bimView.computeBoundingBox(component.segment);
    //旋转中心点
    let center = [(bb[0] + bb[3]) / 2, (bb[1] + bb[4]) / 2, (bb[2] + bb[5]) / 2];
    if (componentId === '8326' && otherModel) {
      //该构件情况，特殊处理
      bimView.animationManager.run([otherModel]);
      return;
    } else if (componentId === '8326') {
      //该构件情况，特殊处理
      bb = bimView.computeObjectViewBoundingBox(component);
      center = [
        (bb[0] + bb[3]) / 2 + 247.21 * 2,
        (bb[1] + bb[4]) / 2 + 809.78 * 2,
        (bb[2] + bb[5]) / 2,
      ];
    }
    //负值代表顺时针旋转，正值代表逆时针旋转
    let speedOfTime = -10;
    let roate = bimView.animationManager.segmentRotateLoop(
      bimView,
      component, //构建对象
      new Float64Array(center), //旋转点
      new Float64Array([0, 0, 1]), //旋转轴
      speedOfTime, //旋转一圈的耗时(秒)
    ); //
    if (componentId === '8326') {
      //该构件情况，特殊处理
      otherModel = roate;
    }
    bimView.animationManager.run([roate]);
  }
  // 获取模型对象
  function handleModelOnLoaded(trie: any, lightModels: any[]): void {
    setLightModel(lightModels);
  }
  //测点信息点击
  function equipInfoClickHandler(data: EquipOptions): void {
    openModal(
      [
        {
          id: data.id,
          name: data.name,
        },
      ],
      data.name,
      data.unit,
    );
  }
  //左测点信息模板
  function leftEquipInfoComponent(): ReactNode {
    const data = tabData.find((item) => item.value === tabKey);
    if (data) {
      return data.leftEquipData.map((item, index) => (
        <div key={index} className={styles['equip-info-modal']}>
          {item.map((citem) => {
            return (
              <div
                onClick={() => equipInfoClickHandler(citem)}
                title={citem.description}
                style={citem.style}
                key={citem.id}
                className={styles['equip-info-modal-item']}
              >
                <span>{citem.name}</span>
                <Statistic
                  valueStyle={{
                    color: '#fff',
                    fontSize: '14px',
                    width: '50px',
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                  precision={1}
                  suffix={citem.unit}
                  value={citem.value}
                />
              </div>
            );
          })}
        </div>
      ));
    } else {
      return <></>;
    }
  }
  //右测点信息模板
  function RightEquipInfoComponent(): ReactNode {
    const data = tabData.find((item) => item.value === tabKey);
    if (data) {
      return data.rightEquipData.map((item, index) => (
        <div key={index} className={styles['equip-info-modal']}>
          {item.map((citem) => {
            return (
              <div
                onClick={() => equipInfoClickHandler(citem)}
                title={citem.description}
                style={citem.style}
                key={citem.id}
                className={styles['equip-info-modal-item']}
              >
                <span>{citem.name}</span>
                <Statistic
                  valueStyle={{
                    color: '#fff',
                    fontSize: '14px',
                    width: '50px',
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                  // formatter={formatter}
                  suffix={citem.unit}
                  precision={1}
                  value={citem.value}
                />
              </div>
            );
          })}
        </div>
      ));
    } else {
      return <></>;
    }
  }
  //机组状态计时器处理
  function equipStatusTimeoutHandler() {
    getEquipStatus();
    if (equipStatusTimeout) {
      clearInterval(equipStatusTimeout);
    }
    equipStatusTimeout = setInterval(() => {
      getEquipStatus();
    }, 10000);
  }
  //每次机组初始化时接口获取一次最新数据,防止socket获取不到最新数据
  function getPointDataList(key: string): void {
    const keyList = getPointIds(key);
    pointDataList({ keyList }).then((res) => {
      if (res.code === '1' && res.data) {
        const list = tabData.map((item) => {
          if (item.value == key) {
            item.leftEquipData.forEach((citem) => {
              citem.forEach((sitem) => {
                sitem.value = res.data[Number(sitem.id)];
              });
            });
            item.rightEquipData.forEach((citem) => {
              citem.forEach((sitem) => {
                sitem.value = res.data[Number(sitem.id)];
              });
            });
          }
          return item;
        });
        setTabData(list);
      }
    });
  }
  //机组tab改变
  function deviceTabChange(key: string, data?: TabOptinos): void {
    setTabKey(key);
    onSelectTabKey(key);
    if (socketInit) {
      unsubMsg();
    }
    subMsg(key);
    getPointDataList(key);
    if (data) {
      equipData = data;
      const curStatus = equipStatus.find((item) => item.id === data.startId);
      if (curStatus) {
        setBimRoateStatus(curStatus);
      }
    }
  }
  return (
    <div ref={parentRef} className={styles['center-equip-info-component-container']}>
      {/* 左侧测点信息 */}
      <div className={styles['left-equip-info-modal']}>
        {!loading ? leftEquipInfoComponent() : null}
      </div>
      {/* m模型 */}
      <bim-air-plugin
        style={{
          width: '100%',
          height: '100%',
        }}
        id="bim-wrapper"
      />
      {/* 右侧测点信息 */}
      <div className={styles['right-equip-info-modal']}>
        {!loading ? RightEquipInfoComponent() : null}
      </div>
      {/* 机组切换tab */}
      <EquipTypeTab
        equipStatus={equipStatus}
        parentRef={parentRef}
        bimDefViewSet={setBimDefaultView}
        tabChange={deviceTabChange}
        socketClient={socketClient}
      />
    </div>
  );
});
