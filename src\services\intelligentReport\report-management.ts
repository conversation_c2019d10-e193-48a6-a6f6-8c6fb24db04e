//月报管理
import { request, jsonHeader } from '@/utils/request';
import { ResponseOptions } from '../monitoring-point-annotation';
import { PageParams } from '@/pages/intelligentReport/monthlyProductionReport/monthly-production-report';
import { PageOptions } from './template-management';
import { PlanTypeEnum } from './report-plan';
import { InfluxesEnums } from '@/components/spread-table/spread-table';
import { DateType } from '@/pages/intelligentReport/templateManagement/pages/addTemplateVersion/components/influxes-table/influxes-table';

//月报新增配置
export interface ReportAddOptions {
  id?: string;
  type: string; //类型
  isAudit: 0 | 1; //是否进入审批（0--暂存；1--提交）
  monthlyName: string; //月报名称
  year: string;
  month: string;
  monthlyData: string; //表格json
  processInstanceId?: string; //流程id，存在就要传
  taskId?: string; //任务id，存在就要传
}
//月报审核配置
export interface ReportExamineOptions {
  id?: string;
  isAudit: 1;
  variables: any; //审批参数,用taskId查询flowable获取
  processInstanceId?: string; //流程id，存在就要传
  taskId?: string; //任务id，存在就要传
  behavior?: string; //操作行为taskName
  opinion?: string; //意见
}
//查询计划数据配置
export interface PlanQueryOptions {
  attributeId: string;
  planCycle: PlanTypeEnum;
  planDate: string;
}
//查询报表历史数据配置
export interface ReportHistoryOptions {
  rowCount: number;
  columnCount: number;
  planCycle: PlanTypeEnum;
  date: string;
  sheetId: string;
  type: string;
}
//查询测点类型数据配置
export interface InfluxQueryOptions {
  key: string; //测点code
  startTime: string;
  endTime: string;
  dataType: InfluxesEnums;
  type: 'max' | 'min' | '';
  dateType: DateType;
}
//月报状态
export enum ReportStateEnum {
  save = '1',
  commit = '2',
  examine = '3',
  end = '4',
  pass = '5',
}
//月报状态枚举
export const StateMap = new Map([
  [ReportStateEnum.save, '填报'],
  [ReportStateEnum.commit, '待审核'],
  [ReportStateEnum.examine, '审核中'],
  [ReportStateEnum.end, '已审核'],
  [ReportStateEnum.pass, '已退回'],
]);
//分页查询月报
export function listPage<T>(params: PageParams): Promise<ResponseOptions<PageOptions<T>>> {
  return request.get('/MonthlyReport/listPage', { params });
}
//新增-月报
export function addReport(data: ReportAddOptions): Promise<ResponseOptions<any>> {
  return request.post('/MonthlyReport/insert', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//删除月报
export function delReport(id: string): Promise<ResponseOptions<any>> {
  return request.post(`/MonthlyReport/deleteByPrimaryKey/${id}`);
}
//修改月报
export function updateReport<T>(data: T): Promise<ResponseOptions<any>> {
  return request.post('/MonthlyReport/updateByPrimaryKey', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//获取审批参数
export function getNextTasks(id: string): Promise<ResponseOptions<any>> {
  return request.get(`/flowable/tasks/${id}/nextTasks`);
}
//月报详情
export function reportDetail<T>(id: string): Promise<ResponseOptions<T>> {
  return request.get(`/MonthlyReport/${id}`);
}
//查询最新发布模板版本
export function templateData(code: string): Promise<ResponseOptions<any>> {
  return request.get(`/TemplateManager/getTemplateData/${code}`);
}
//根据属性查询对应的计划报表中的值
export function planDataValue(data: PlanQueryOptions[]): Promise<ResponseOptions<any>> {
  return request.post('/PlanData/selectPlanDataValue', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//解析报表历史数据中坐标位置数据
export function reportAnalysisData(data: ReportHistoryOptions[]): Promise<ResponseOptions<any>> {
  return request.post('/PlanData/analysisData', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//获取测点类型值
export function selectNrInflux(data: InfluxQueryOptions[]): Promise<ResponseOptions<any>> {
  return request.post('/PlanData/selectNrInflux', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
