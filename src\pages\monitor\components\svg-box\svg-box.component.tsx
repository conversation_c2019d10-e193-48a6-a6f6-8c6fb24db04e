import { FC, useRef, useEffect, useState, Fragment } from 'react';
import svg from '../../svg/monitorSvg.svg';
import styles from './svg-box.component.less';
import Detail from '../motion-detail/components/detail/detail.component';
import { getHistory, getSelectFlux, getStateStatus } from '../../monitor.service';
import {
  circles,
  colorMap,
  greenColor,
  JzInfoMap,
  lines,
  MxInfoMap2,
  nF,
  redColor,
  sourceHead1,
  sourceHead3,
  ST,
  subTheme,
  SvgLabelIds,
  TextMap,
  unsubTheme,
  YjxInfoMap2,
} from '../../constant';
import { LineModal } from '../line-modal/line-modal';
import { motion } from 'framer-motion';
import MotionDetail from '../motion-detail/motion-detail.component';
import { findDictTree } from '@/pages/point/point.service';
import { Client } from '@stomp/stompjs';
import { PointUnitDictionary } from '@/pages/point/type';
import { SvgFormData } from '../../monitor.page';
import { rsaEncrypt } from '@/utils/jsencrypt.util';
interface Props {
  pageRef: any;
  client: Client | null;
  pointInfoDict: PointUnitDictionary[]; //点位信息字典
  svgFormData: SvgFormData[]; //svg表格的数据
  onModalTitleChange: (title: string) => void;
  onOpenChange: (open: boolean) => void;
}
interface FixedInfo {
  left: number;
  top: number;
  detailData: { key: string; label: string; value: string }[];
  name: string; //key
  isFixed: boolean;
  isWebSocket: boolean; //正在通过webSocket监听数据
}
let modalTf: boolean = false; //鼠标是否进入弹框
let timeOut: NodeJS.Timeout | null = null; //鼠标弹框显示控制计时器
let hasSub: boolean; //是否已经建立监听
let curWebPoints: string[] = []; //当前正在监听的悬浮窗点位codes
const SvgBox: FC<Props> = ({
  onModalTitleChange,
  onOpenChange,
  pageRef,
  pointInfoDict,
  svgFormData,
  client,
}) => {
  const svgRef = useRef<HTMLDivElement>(null);
  const [isInit, setIsInit] = useState(false);
  const [visible, setVisible] = useState(false);
  const [showName, setShowName] = useState(''); //当前hover的点位
  const showNameRef = useRef('');
  const isDragging = useRef(false);
  const dragStart = useRef<{ x: number; y: number } | null>(null);
  const fixedInfoListRef = useRef<FixedInfo[]>([]); //点位的信息，用于hover事件方法中数据的处理，避免state异步更新导致的旧数据影响
  const [fixedInfoList, setFixedInfoList] = useState<FixedInfo[]>([]); //点位的信息,用于页面渲染，避免ref.current引起的同步渲染卡顿

  //  #region 方法-svg初始化
  const fetchSvgContent = async () => {
    try {
      // 0。获取文件文本内容
      const response = await fetch(svg);
      const svgContent = await response.text();
      if (svgRef.current) {
        // 1.将SVG内容插入到svg标签中
        svgRef.current.innerHTML = svgContent;

        // 2.修改初始图形效果
        const tagNames = ['text', 'path', 'circle'];
        tagNames.forEach((item: string) => {
          const elements: any = svgRef.current?.getElementsByTagName(item);
          if (elements) {
            elements?.forEach((ele: any) => {
              ele.setAttribute('stroke-width', `0.6`);

              if (item !== 'text') {
                ele.setAttribute('fill', `transparent`);
              }

              if (item === 'text') {
                ele.setAttribute('font-size', `7`);
                ele.setAttribute('fill', `#fff`);
              }

              if (ele.attributes.class.value.includes('SOLID')) {
                ele.setAttribute('stroke', `#fff`);
              }
            });
          }
        });

        //将源文件中use引用的标签的stroke去掉，再通过脚本修改use标签的样式，修改线颜色为白色
        const elements: any = svgRef.current?.getElementsByTagName('use');
        if (elements) {
          elements?.forEach((ele: any) => {
            ele.setAttribute('stroke', `${redColor}`);
          });
        }
        // 3.调整数据框的位置
        const columnGroupId = [
          'columnGroup_yangjiaxian1',
          'columnGroup_yangjiaxian2',
          'columnGroup_muxian1',
          'columnGroup_muxian2',
          'columnGroup_bottom1',
          'columnGroup_bottom2',
          'columnGroup_bottom3',
        ];
        columnGroupId.forEach((columnGroupId) => {
          // 获取指定的 <g> 元素
          const group = document.getElementById(columnGroupId);
          // 定义偏移量
          const offsetX = -771;
          const offsetY = -992;
          if (group) {
            // 遍历 <g> 元素内的所有子元素
            group.querySelectorAll('rect, text').forEach((element: any) => {
              if (element.tagName === 'rect') {
                // 修改 rect 的 x 和 y 属性
                element.setAttribute('x', parseFloat(element.getAttribute('x')) + offsetX);
                element.setAttribute('y', parseFloat(element.getAttribute('y')) + offsetY);
                element.setAttribute('stroke-width', `0.5`);
              } else if (element.tagName === 'text') {
                // 修改 text 的 transform 属性
                const transform = element.getAttribute('transform');
                const matrixMatch = transform.match(/\(([^)]+)\)/); //正则获取transform的matrix的参数
                if (matrixMatch) {
                  const matrixParams = matrixMatch[1].split(' ').map(Number);
                  matrixParams[4] += offsetX; // 修改倒数第二个参数
                  matrixParams[5] += offsetY; // 修改最后一个参数
                  element.setAttribute('transform', `matrix(${matrixParams.join(', ')})`);
                  element.setAttribute('font-size', `7`);
                }
              }
            });
          }
        });

        setIsInit(true);
      }
    } catch (error) {
      console.error('Failed to fetch SVG content:', error);
    }
  };

  //  #region 方法-（可选)改变颜色，（可选)旋转use元素
  const rotateElement = (id: string, color: null | string = null, isRotate = false) => {
    // 获取 <use> 元素
    const useElement = document.getElementById(id);
    if (useElement) {
      if (color) {
        // 1.设置颜色
        useElement.setAttribute('stroke', `${color}`);
      }
      if (isRotate) {
        // 2.旋转
        // 获取 <use> 元素的 x 和 y 属性值
        const x = parseFloat(useElement.getAttribute('x') as string);
        const y = parseFloat(useElement.getAttribute('y') as string);
        // 获取 <use> 元素的边界框
        const bbox = (useElement as any).getBBox();
        // 计算中心点
        const centerX = x + bbox.width / 2;
        const centerY = y + bbox.height / 2;
        // 设置 transform 属性
        useElement.setAttribute(
          'transform',
          `translate(${x}, ${y}) rotate(90) translate(${-centerX}, ${-centerY})`,
        );
      }
    }
  };

  // #region 方法-根据内容修改text元素颜色
  const changeTextColor = (text: string, color = greenColor) => {
    // 获取所有 <text> 元素
    const textElements: any = svgRef.current?.getElementsByTagName('text');

    // 遍历所有 <text> 元素，找到匹配的元素
    textElements?.forEach((textElement: any) => {
      if (textElement.textContent === text) {
        textElement.setAttribute('fill', `${color}`);
        textElement.setAttribute('stroke', `${color}`);
        return;
      }
    });
  };
  //  #region 方法-修改线颜色
  const changeStrokeColor = (id: string, color = greenColor, changeFillColor = false) => {
    // 获取 <use> 元素
    const useElement = document.getElementById(id);
    if (useElement) {
      // 1.设置颜色
      if (!changeFillColor) {
        useElement.setAttribute('stroke', `${color}`);
      }
      if (changeFillColor) {
        useElement.setAttribute('fill', `${color}`);
      }
    }
  };
  //  #region 方法-修改文本与文本颜色
  const changeTextFill = (id: string, color = greenColor, newText = null) => {
    // 获取 <use> 元素
    const useElement = document.getElementById(id);
    if (useElement) {
      // 1.设置颜色
      useElement.setAttribute('fill', `${color}`);
      if (newText) {
        useElement.textContent = newText;
      }
    }
  };
  //  #region 方法-修改方形的填充色和线颜色
  const changeLineColor = (id: string, color = greenColor) => {
    // 获取 <use> 元素
    const useElement = document.getElementById(id);
    if (useElement) {
      // 1.设置颜色
      useElement.setAttribute('stroke', `${color}`);
      useElement.setAttribute('fill', `${color}`);
    }
  };

  //  #region 元素添加hover事件
  const addHoverAction = (id: string, elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.addEventListener('mouseover', (event: MouseEvent) => {
        timeOut && clearTimeout(timeOut);
        if (visible === false) {
          element.style.cursor = 'pointer';
          const rect = element.getBoundingClientRect();
          getDetailData(
            { name: id },
            {
              top: rect.top - 80, // 会出现在元素的右侧
              left: rect.left - 200,
              name: id,
            },
          );
          setVisible(true);
        }
      });

      element.addEventListener('mouseleave', () => {
        timeOut && clearTimeout(timeOut);
        //让鼠标可以移动到弹框内部
        timeOut = setTimeout(() => {
          if (modalTf === false) {
            const newList = fixedInfoListRef.current.map((item) => ({
              ...item,
              isWebSocket: item.name === showNameRef.current ? false : item.isWebSocket,
            }));
            console.log('悬浮窗信息列表===mouseleave===', newList);

            fixedInfoListRef.current = newList;
            setFixedInfoList(newList);
            element.style.cursor = 'default';
            setShowName('');
            showNameRef.current = '';
            setVisible(false);
          }
        }, 1000);
      });
    }
  };

  // #region 方法-处理svg数据展示
  const setSvgDataFn = (name: string, data: { alias: string; value: number }[]) => {
    if (name === '羊加Ⅰ线') {
      data?.forEach((item: any) => {
        if (YjxInfoMap2[item.alias]) {
          const textElement = document.getElementById(
            `columnGroup_yangjiaxian1_${YjxInfoMap2[item.alias]}`,
          );
          if (textElement) {
            textElement.textContent = (Math.round(item.value * 100) / 100).toString();
          }
        }
      });
    } else if (name === '羊加Ⅱ线') {
      data?.forEach((item: any) => {
        if (YjxInfoMap2[item.alias]) {
          const textElement = document.getElementById(
            `columnGroup_yangjiaxian2_${YjxInfoMap2[item.alias]}`,
          );
          if (textElement) {
            textElement.textContent = (Math.round(item.value * 100) / 100).toString();
          }
        }
      });
    } else if (name === '330kVⅠ母') {
      data?.forEach((item: any) => {
        if (MxInfoMap2[item.alias]) {
          const textElement = document.getElementById(
            `columnGroup_muxian1_${MxInfoMap2[item.alias]}`,
          );
          if (textElement) {
            textElement.textContent = (Math.round(item.value * 100) / 100).toString();
          }
        }
      });
    } else if (name === '330kVⅡ母') {
      data?.forEach((item: any) => {
        if (MxInfoMap2[item.alias]) {
          const textElement = document.getElementById(
            `columnGroup_muxian2_${MxInfoMap2[item.alias]}`,
          );
          if (textElement) {
            textElement.textContent = (Math.round(item.value * 100) / 100).toString();
          }
        }
      });
    } else if (['1号机组', '2号机组', '3号机组']?.includes(name)) {
      const id = name?.[0];
      data?.forEach((item: any) => {
        if (JzInfoMap[item.alias]) {
          const textElement = document.getElementById(
            `columnGroup_bottom${id}_${JzInfoMap[item.alias]}`,
          );
          if (textElement) {
            const str = (Math.round(item.value * 100) / 100).toString();
            textElement.textContent = item.alias === '导叶开度' ? str + '%' : str;
          }
        }
      });
    }
  };
  // #region socket-消息返回
  function messageReturn(soucre: string, client: Client | null = null) {
    if (client) {
      client.subscribe(soucre, function (message: any) {
        try {
          // 处理解析后的 JSON 数据
          const recv = JSON.parse(message.body);
          console.log('收到返回的消息(hover):', recv);
          fixedInfoListRef?.current?.forEach((item) => {
            item.detailData?.forEach((it) => {
              if (recv.key === it.key) {
                it.value = recv.value.toFixed(2);
              }
            });
          });
          setFixedInfoList(fixedInfoListRef?.current);
        } catch (error) {
          console.log('收到返回的消息(hover)不是有效的 JSON 格式:', message.body, error);
        }
      });
    }
  }
  // #region socket-发送消息
  function sendMsg(pointCodes: string, isSubscribe: boolean) {
    const userId = JSON.parse(localStorage.getItem('user') as string).id;
    //消息体
    const msgBody = {
      //body只接受字符串数据
      body: JSON.stringify({
        pointCodes,
        userId,
        topicType: sourceHead3,
      }),
      destination: isSubscribe ? subTheme : unsubTheme,
      headers: {
        Authorization: rsaEncrypt(isSubscribe ? subTheme : unsubTheme).toString(),
      },
    };
    console.log(`消息体(${isSubscribe ? 'hover订阅' : '取消hover订阅'})：`, msgBody);

    // 接受返回消息
    !hasSub && messageReturn(sourceHead3 + userId, client);
    hasSub = true;
    // 发送消息
    client && client.publish(msgBody);
  }

  // #region 接口-获取点位绑定数据
  const getDetailData = (params: { name: string }, other: any = {}) => {
    return new Promise((resolve, reject) => {
      getSelectFlux(params)
        .then((res) => {
          if (res.code === '1') {
            if (
              [
                '羊加Ⅰ线',
                '羊加Ⅱ线',
                '330kVⅠ母',
                '330kVⅡ母',
                '1号机组',
                '2号机组',
                '3号机组',
              ]?.includes(params.name)
            ) {
              setSvgDataFn(params.name, res.data);
            } else {
              const data = res.data?.map((item: any) => {
                return {
                  key: item.key,
                  label: item.alias,
                  value: Number(item.value),
                };
              }); // .filter((item: any) => item.value !== 0);
              const filtered = fixedInfoListRef.current
                ?.filter((item) => item.name !== other.name)
                ?.map((item) => ({
                  ...item,
                  isWebSocket: item.isFixed,
                })); //不是当前的hover的信息
              const curInfo = fixedInfoListRef.current?.find((item) => item.name === other.name); //当前hover的信息
              const info: FixedInfo = {
                ...other,
                isWebSocket: true,
                detailData: data,
                isFixed: curInfo?.isFixed || false, //当前信息如果已经存在就要保留isFixed状态,
              };
              //  更新数据
              const newInfoList = [...filtered, info];

              console.log('悬浮窗信息列表===getDetailData===', newInfoList);

              fixedInfoListRef.current = newInfoList;
              setFixedInfoList(newInfoList);
              setShowName(params.name); //标记当前hover的点位
              showNameRef.current = params.name;
            }
            resolve(res);
          } else {
            reject(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  // #region 接口-获取状态数据
  const getStateStatusData = (isInit: boolean) => {
    if (isInit) {
      // 方形默认状态
      lines?.forEach((item) => {
        const lineId = `line-${item}`;
        changeLineColor(lineId, colorMap[1]);
        changeTextColor(lineId, colorMap[1]);
      });
      // 机组默认状态
      nF?.forEach((item) => {
        ['1', '2', '3', '4'].forEach((num) => {
          changeStrokeColor(item + '-' + num, colorMap[3]);
        });
        changeTextFill(item + '-text', colorMap[3], TextMap[3]);
      });
    }

    getStateStatus().then((res) => {
      if (res.code === '1') {
        const resData = res.data;
        const resArr: { key: string; value: number }[] = Object.entries(resData).map(
          ([key, value]: any) => ({
            key,
            value,
          }),
        );
        resArr.forEach((item: { key: string; value: number }) => {
          // 圆圈
          if (circles?.includes(item.key)) {
            const useId = `use-${item.key}`;
            rotateElement(useId, colorMap[item.value], item.value === 0);
          }
          // 方形
          if (lines?.includes(item.key)) {
            const lineId = `line-${item.key}`;
            changeLineColor(lineId, colorMap[item.value]);
            changeTextColor(lineId, colorMap[item.value]);
          }
          // 1F\2F\3F
          if (nF?.includes(item.key)) {
            ['1', '2', '3', '4'].forEach((num) => {
              changeStrokeColor(item.key + '-' + num, colorMap[item.value]);
            });
            changeTextFill(item.key + '-text', colorMap[item.value], TextMap[item.value]);
          }
        });
      }
    });
  };
  // #region 事件-详情label点击
  const onLabelClick = (name: any, label: any, value: any, key: any) => {
    onModalTitleChange(`${name}-${label}-${key}`);
    onOpenChange(true);
  };
  // #region 事件-滚动缩放
  const handleWheel = (event: WheelEvent) => {
    if (svgRef.current) {
      const svg = svgRef.current;
      const { viewBox } = svg;
      const { x, y, width, height } = viewBox.baseVal;
      const zoomFactor = event.deltaY > 0 ? 1.1 : 0.9; // 缩放因子，向上滚动放大，向下滚动缩小

      // 计算当前 viewBox 的中心点
      const centerX = x + width / 2;
      const centerY = y + height / 2;

      // 计算新的 viewBox
      const newMinX = centerX + (x - centerX) * zoomFactor;
      const newMinY = centerY + (y - centerY) * zoomFactor;
      const newWidth = width * zoomFactor;
      const newHeight = height * zoomFactor;

      // 设置新的 viewBox
      svg.setAttribute('viewBox', `${newMinX} ${newMinY} ${newWidth} ${newHeight}`);
      // 阻止默认行为
      event.preventDefault();
    }
  };

  // #region 事件-鼠标中键按下
  const handleMouseDown = (event: MouseEvent) => {
    if (event.button === 1) {
      // 中键
      isDragging.current = true;
      dragStart.current = { x: event.clientX, y: event.clientY };
      // 阻止默认行为
      event.preventDefault();
    }
  };

  // #region 事件-鼠标拖拽
  const handleMouseMove = (event: MouseEvent) => {
    if (isDragging.current && svgRef.current) {
      const svg = svgRef.current;
      const { viewBox } = svg;
      const { x, y, width, height } = viewBox.baseVal;

      const deltaX = event.clientX - dragStart.current!.x;
      const deltaY = event.clientY - dragStart.current!.y;

      // 更新 viewBox
      const newMinX = x - deltaX;
      const newMinY = y - deltaY;

      svg.setAttribute('viewBox', `${newMinX} ${newMinY} ${width} ${height}`);

      // 更新拖拽起点
      dragStart.current = { x: event.clientX, y: event.clientY };
    }
  };

  // #region 事件-鼠标中键放开
  const handleMouseUp = (event: MouseEvent) => {
    if (event.button === 1) {
      // 中键
      isDragging.current = false;
      dragStart.current = null;
    }
  };
  // #region 事件-悬浮固定改变
  const onFixedChange = (name: string) => {
    const newFixedInfoList = fixedInfoListRef.current?.map((item) => {
      if (item.name === name) {
        !item.isFixed && setShowName('');
        !item.isFixed && (showNameRef.current = '');
        return { ...item, isFixed: !item.isFixed, isWebSocket: !item.isFixed };
      } else {
        return { ...item };
      }
    });
    console.log('悬浮窗信息列表===onFixedChange===', newFixedInfoList);
    fixedInfoListRef.current = newFixedInfoList;
    setFixedInfoList(newFixedInfoList);
  };
  // #region useEffect 初始化
  useEffect(() => {
    fetchSvgContent();
    getDetailData({ name: '330kVⅠ母' });
    getDetailData({ name: '330kVⅡ母' });
    getDetailData({ name: '羊加Ⅰ线' });
    getDetailData({ name: '羊加Ⅱ线' });
    getDetailData({ name: '1号机组' });
    getDetailData({ name: '2号机组' });
    getDetailData({ name: '3号机组' });

    // 添加鼠标滚轮事件监听器
    if (svgRef.current) {
      svgRef.current.addEventListener('wheel', handleWheel);
      svgRef.current.addEventListener('mousedown', handleMouseDown);
      svgRef.current.addEventListener('mousemove', handleMouseMove);
      svgRef.current.addEventListener('mouseup', handleMouseUp);
    }

    // 清理事件监听器
    return () => {
      modalTf = false;
      if (svgRef.current) {
        svgRef.current.removeEventListener('wheel', handleWheel);
        svgRef.current.removeEventListener('mousedown', handleMouseDown);
        svgRef.current.removeEventListener('mousemove', handleMouseMove);
        svgRef.current.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }, []);
  // #region useEffect 通过字典信息添加点击事件
  useEffect(() => {
    if (isInit && pointInfoDict?.length) {
      pointInfoDict?.forEach((item: any) => {
        const svgLabel = document.getElementById(item?.dictName);
        if (svgLabel) {
          svgLabel.addEventListener('click', () => {
            onModalTitleChange(`${item?.dictName}-${item?.dictValue}`);
            onOpenChange(true);
          });
          svgLabel.style.cursor = 'pointer';
        }
      });
    }
  }, [isInit, pointInfoDict]);
  // #region useEffect 显示实时的表格数据
  useEffect(() => {
    if (isInit && svgFormData?.length) {
      svgFormData?.forEach((item: SvgFormData) => {
        setSvgDataFn(item.name, item.data);
      });
    }
  }, [isInit, svgFormData]);
  // #region useEffect 修改样式
  useEffect(() => {
    let timer: NodeJS.Timeout;
    // svg初始化之后根据数据修改元素的样式
    if (isInit) {
      //开合闸数据：分闸0绿色线路交叉（圆圈旋转），合闸1红色线路联通（圆圈不做旋转），1F\2F\3F：停机态绿色2、发电态红色3、空载黄色4、空转紫色5
      getStateStatusData(true);
      timer = setInterval(() => {
        getStateStatusData(false);
      }, 20000);
      // 添加hover事件
      ST?.forEach((id: string) => {
        addHoverAction(id, `${id}-1`);
      });
      circles?.forEach((id: string) => {
        addHoverAction(id, `use-${id}`);
      });
      lines?.forEach((id: string) => {
        addHoverAction(id, `line-${id}`);
      });
    }
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [isInit]);
  // #region useEffect webSocket订阅与取消订阅
  useEffect(() => {
    const newWebPoints: string[] = [];
    fixedInfoList?.forEach((item) => {
      if (item?.isWebSocket && item.detailData?.length) {
        item.detailData?.forEach((it) => {
          newWebPoints.push(it.key);
        });
      }
    });
    // 找出新增的点位代码
    const addPoints = newWebPoints.filter((point) => !curWebPoints.includes(point));
    // 找出删除的点位代码
    const deletePoints = curWebPoints.filter((point) => !newWebPoints.includes(point));

    // 发送订阅消息
    if (addPoints.length > 0) {
      sendMsg(addPoints.join(','), true);
    }

    // 发送取消订阅消息
    if (deletePoints.length > 0) {
      sendMsg(deletePoints.join(','), false);
    }
    // 更新 curWebPoints
    curWebPoints = newWebPoints;
  }, [fixedInfoList]);

  // #region return
  return (
    <Fragment>
      <div className={styles.fixedContent} id="svgFixedContent">
        {/*<svg xmlns="http://www.w3.org/2000/svg" viewBox="-780 -820 660 450" ref={svgRef}></svg>*/}
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="-901 -819 909 620" ref={svgRef}></svg>
      </div>
      {fixedInfoList?.map((item: FixedInfo) => {
        return (
          <MotionDetail
            key={item.name}
            parentRef={pageRef}
            onFixedChange={onFixedChange}
            title={item.name}
            onLabelClick={onLabelClick}
            detailData={item.detailData}
            position={{
              left: item.left,
              top: item.top,
            }}
            onMouseLeave={() => {
              modalTf = false;
              setVisible(false);
            }}
            onMouseEnter={() => (modalTf = true)}
            isFixed={item.isFixed}
            visible={visible && showName === item.name}
          />
        );
      })}
    </Fragment>
  );
};

export default SvgBox;
