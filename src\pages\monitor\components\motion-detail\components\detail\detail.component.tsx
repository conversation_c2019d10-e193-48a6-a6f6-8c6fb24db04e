import EmptyComponent from '@/components/empty-component/empty-component';
import { Descriptions, Empty } from 'antd';
import styles from './detail.component.less';
import FixedIcon from '@/assets/images/fixed.png';
import UnFixedIcon from '@/assets/images/unfixed.png';
interface Props {
  data: { key: string; label: string; value: string }[];
  onLabelClick?: ((id: any, label: any, value: any, key: any) => void) | null;
  title: string;
  isFixed: boolean;
  onFixedChange: (id: string) => void;
}
const Detail: React.FC<Props> = ({
  data,
  onLabelClick = null,
  title = '',
  isFixed = false,
  onFixedChange,
}) => {
  return (
    <div className={styles.descriptionsBox}>
      <div className={styles.iconBox}>
        <div> {title}</div>
        <img
          src={isFixed ? FixedIcon : UnFixedIcon}
          style={{ cursor: 'pointer' }}
          onClick={() => onFixedChange(title)}
        ></img>
      </div>
      {data?.length > 0 ? (
        <Descriptions column={1} className={styles.descriptionsPanel}>
          {data?.map((ele: any) => {
            return (
              <Descriptions.Item
                key={ele.key}
                label={
                  <span
                    style={{ cursor: onLabelClick ? 'pointer' : 'text' }}
                    onClick={() =>
                      onLabelClick && onLabelClick(title, ele.label, ele.value, ele.key)
                    }
                  >
                    {ele.label}
                  </span>
                }
              >
                {ele.value}
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      ) : (
        <EmptyComponent />
      )}
    </div>
  );
};

export default Detail;
