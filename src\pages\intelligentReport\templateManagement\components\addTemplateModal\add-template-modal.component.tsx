import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { FC, memo, useEffect, useState } from 'react';
import { FormOptions, PropOptions } from './add-template-modal';
import { Form, Input, message } from 'antd';
import styles from './add-template-modal.component.less';
import { addTemplateBaseInfo } from '@/services/intelligentReport/template-management';
import { ProductOptions, addPrivilegeProduct } from '@/services/system';

const { TextArea } = Input;
export const AddTemModal: FC<PropOptions> = memo(({ parentMenuData, open, close, onOk }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm();
  useEffect(() => {
    if (open) {
      forms.setFieldValue('code', new Date().getTime());
    }
  }, [open]);
  function closeFn(): void {
    forms.resetFields();
    close();
  }
  //菜单参数处理
  function menuProductDataHandler(
    formData: FormOptions,
    parentMenuData: ProductOptions,
  ): ProductOptions {
    return {
      name: formData.name,
      code: formData.type,
      routingUrl: '/operations/monthlyProductionReport/' + formData.code,
      displayLocation: '_micro',
      checkPermission: 1,
      displayState: 1,
      deleteFlag: 0,
      sandbox: 1,
      preLoad: 1,
      injectToken: 0,
      activeRule: parentMenuData.activeRule,
      activeRule2: 'http://172.168.11.27:3020/',
      sortNo: 0,
      iconClass: null,
      memo: null,
      memu: true,
      props: '{}',
      parentId: parentMenuData.id || '',
      projectCode: parentMenuData.projectCode,
    };
  }
  function submit(): void {
    if (parentMenuData) {
      forms.validateFields().then((data: FormOptions) => {
        setLoading(true);
        data.type = 'operations-report-' + data.code;
        addTemplateBaseInfo(data)
          .then((res) => {
            if (res.code === '1') {
              addPrivilegeProduct(menuProductDataHandler(data, parentMenuData))
                .then((res) => {
                  if (res.code === '1') {
                    message.success('新增完成！');
                    closeFn();
                    onOk();
                  } else {
                    message.warning(res.message || '新增失败！');
                  }
                  setLoading(false);
                })
                .catch((err) => {
                  setLoading(false);
                });
            } else {
              message.warning(res.error || '新增失败！');
              setLoading(false);
            }
          })
          .catch((err) => {
            setLoading(false);
          });
      });
    } else {
      message.warning('菜单数据异常');
    }
  }
  return (
    <SvgModal confirmloading={loading} onOk={submit} close={closeFn} open={open} title="新增模板">
      <div className={styles['modal-container']}>
        <Form className="report-form" labelCol={{ span: 5 }} style={{ width: '100%' }} form={forms}>
          <Form.Item<FormOptions>
            rules={[{ required: true, message: '输入名称' }]}
            label="模板名称"
            name="name"
          >
            <Input placeholder="请输入..."></Input>
          </Form.Item>
          <Form.Item<FormOptions>
            rules={[{ required: true, message: '输入编码' }]}
            label="编码"
            name="code"
          >
            <Input placeholder="请输入..."></Input>
          </Form.Item>

          <Form.Item<FormOptions> label="备注" name="remark">
            <TextArea rows={4} placeholder="请输入..." />
          </Form.Item>
        </Form>
      </div>
    </SvgModal>
  );
});
