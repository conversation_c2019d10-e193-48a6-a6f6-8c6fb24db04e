import { FC, ReactNode, memo, useState } from 'react';
import { SvgModal } from '../svg-modal/svg-modal.component';
import { PropOptions } from './del-modal';
import styles from './del-modal.component.less';
import { InfoCircleOutlined } from '@ant-design/icons';
import { message } from 'antd';

export const DelModal: FC<PropOptions> = memo(
  ({ open, close, onOk, delFn, children, tipsText, title = '删除提示' }) => {
    const [loading, setLoading] = useState<boolean>(false);
    //提示模板
    function tipsComponent(): ReactNode {
      return (
        <div className={styles['del-tips-container']}>
          <InfoCircleOutlined className={styles.del} />
          <span className={styles.text}>{tipsText ? tipsText : '是否删除该数据？'}</span>
        </div>
      );
    }
    //提交处理
    function commit(): void {
      if (delFn) {
        setLoading(true);
        delFn()
          .then((res) => {
            if (res.code === '1') {
              message.success(res.message || '删除完成');
              close();
              onOk && onOk();
            } else {
              message.error(res.message || '删除失败');
            }
            setLoading(false);
          })
          .catch((err) => {
            message.error('系统异常');
            console.log(err);
            setLoading(false);
          });
      } else if (onOk) {
        onOk();
      } else {
        close();
      }
    }
    return (
      <SvgModal
        height="200px"
        confirmloading={loading}
        onOk={commit}
        close={close}
        open={open}
        title={title}
      >
        <div className={styles['del-modal-container']}>{children ? children : tipsComponent()}</div>
      </SvgModal>
    );
  },
);
