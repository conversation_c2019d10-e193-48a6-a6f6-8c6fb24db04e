import {
  FC,
  ReactNode,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { SvgModal } from '../svg-modal/svg-modal.component';
import { DataOptions, defaultEndDate, defaultStartDate, QueryParams } from './line-chart-modal';
import styles from './line-chart-modal.component.less';
import { LineChart } from '../line-chart/line-chart.component';
import { ConfigProvider, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import locale from 'antd/locale/zh_CN';
import { CloseOutlined } from '@ant-design/icons';
import {
  PointModal,
  PointModalOptions,
  RowsDataType,
} from './components/point-modal/point-modal.component';
import { ColumnsOptions } from '@/components/table-component/table';
import { ColumnsType } from 'antd/es/table';
interface PropOptions {
  chartData: DataOptions[]; //统计图数据
  onQuery: (params: QueryParams) => void; //查询方法(curList:已选择的点位列表)
  open: boolean; //展示控制
  close: () => void; //关闭方法
  ref?: any;
  loading?: boolean; //加载控制
  unit?: string; //统计图单位
  title: string; //弹框标题
  width?: string; //弹框宽度
  height?: string; //弹框高度
  // 对比点位弹窗组件
  pointList: RowsDataType[]; //点位列表
  columns: ColumnsType<ColumnsOptions>; //点位选择弹窗表格配置
  onPointModalSearch: (params: any) => void; //对比点位列表查询
  pointLoading?: boolean; //对比点位加载控制
}
dayjs.locale('zh-cn');
const { RangePicker } = DatePicker;

let selectBegin: string = ''; //历史记录开始日期
let selectEnd: string = ''; //历史记录结束日期
export const LineChartModal: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        pointList,
        columns,
        title,
        close,
        onQuery,
        onPointModalSearch,
        pointLoading = false,
        open,
        width = '1400px',
        height = '600px',
        chartData,
        loading = false,
        unit = '',
      },
      ref,
    ) => {
      const [isRealTime, setIsRealTime] = useState<boolean>(true);
      const [pointModalShow, setPointModalShow] = useState(false); //对比点位弹窗显示
      const [chosePointList, setChosePointList] = useState<RowsDataType[]>([]); //已选择的点位
      const lineRef = useRef<any>(null);
      const [forms] = Form.useForm();
      useImperativeHandle(ref, () => ({}));

      //关闭处理
      function closeHandler(): void {
        forms.resetFields();
        setChosePointList([]);
        setIsRealTime(true);
        dateSetDefault();
        close();
      }
      //日期恢复默认
      function dateSetDefault(): void {
        selectBegin = '';
        selectEnd = '';
      }
      //日期改变
      function dateChange(date: any, dateString: string | string[]): void {
        if (dateString && dateString[0]) {
          selectBegin = dayjs(dateString[0]).format('YYYY-MM-DD HH:mm:ss');
          selectEnd = dayjs(dateString[1]).format('YYYY-MM-DD HH:mm:ss');
        } else {
          dateSetDefault();
        }
      }

      //#region 事件-查询条件重置
      function reset(): void {
        setIsRealTime(true);
        forms.resetFields();
        dateSetDefault();
        // 调用webSocket实时数据
        onQuery({
          selectBegin,
          selectEnd,
          curList: chosePointList,
          addList: [],
          deleteList: [],
          isRealTime: true,
        });
      }

      // #region 事件-点位选择弹窗确认
      function onPointOk(curList: any[]): void {
        const addList = curList.filter(
          (item) => !chosePointList.some((prevItem) => prevItem.id === item.id),
        );
        const deleteList = chosePointList.filter(
          (prevItem) => !curList.some((item) => item.id === prevItem.id),
        );
        setPointModalShow(false);
        setChosePointList(curList);
        onQuery({ selectBegin, selectEnd, curList, isRealTime, addList, deleteList });
      }

      //#region 事件-删除对比点位
      function delPoint(code: string, deltePoint: any): void {
        const curList = chosePointList.filter((item) => item.id !== code);
        setChosePointList(curList);
        onQuery({
          selectBegin,
          selectEnd,
          curList,
          addList: [],
          deleteList: [deltePoint],
          isRealTime,
        });
      }

      useEffect(() => {
        if (open === false && lineRef.current) {
          lineRef.current.clear();
        }
        if (open === false) {
          setIsRealTime(true);
          dateSetDefault();
        }
        if (open && lineRef.current) {
          onQuery({
            selectBegin: '',
            selectEnd: '',
            curList: [],
            addList: [],
            deleteList: [],
            isRealTime: true,
          });
        }
        return () => {
          dateSetDefault();
        };
      }, [open]);
      return (
        <SvgModal
          width={width}
          height={height}
          close={closeHandler}
          isFooterBtn={false}
          open={open}
          title={title}
        >
          <div className={styles['modal-container']}>
            <div className={styles['chart-container-box']}>
              <div className="form-container">
                {/* 表单区域 */}
                <Form
                  form={forms}
                  layout="inline"
                  initialValues={{ layout: 'inline' }}
                  style={{ maxWidth: 'none', paddingLeft: '23px' }}
                >
                  <Form.Item label="日期" name="date">
                    <ConfigProvider locale={locale}>
                      <RangePicker
                        onChange={dateChange}
                        showTime={{ format: 'HH:mm' }}
                        format="YYYY-MM-DD HH:mm"
                      />
                    </ConfigProvider>
                  </Form.Item>
                  <Form.Item>
                    <type-button
                      loading={loading}
                      onClick={() =>
                        onQuery({
                          selectBegin,
                          selectEnd,
                          curList: chosePointList,
                          addList: [],
                          deleteList: [],
                          isRealTime: false,
                        })
                      }
                    >
                      查询
                    </type-button>
                  </Form.Item>
                  <Form.Item>
                    <type-button loading={loading} onClick={reset}>
                      重置
                    </type-button>
                  </Form.Item>
                </Form>
              </div>
              <div className={styles['chart-container']}>
                <LineChart
                  sliderFn={() => setIsRealTime(false)}
                  ref={lineRef}
                  chartData={chartData}
                  unit={unit}
                  loading={loading}
                />
              </div>
            </div>
            {/* 对比点位 */}
            <div className={styles['point-list']}>
              <div className={styles['point-list-title']}>
                <span>对比点位</span>
                <type-button onClick={() => setPointModalShow(true)}>选择</type-button>
              </div>
              {chosePointList.map((item) => {
                return (
                  <div key={item.key} className={styles['point-list-item']}>
                    <span title={`${item.name}-${item.alias}`}>{`${item.name}-${item.alias}`}</span>
                    <CloseOutlined
                      onClick={(e) => {
                        e.stopPropagation();
                        delPoint(item.id, item);
                      }}
                      className={styles['point-list-item-del']}
                    />
                  </div>
                );
              })}
            </div>
          </div>
          <PointModal
            onSearch={onPointModalSearch}
            pointList={pointList}
            loading={pointLoading}
            onOk={onPointOk}
            columns={columns}
            close={() => setPointModalShow(false)}
            open={pointModalShow}
            chosePointList={chosePointList}
          />
        </SvgModal>
      );
    },
  ),
);
