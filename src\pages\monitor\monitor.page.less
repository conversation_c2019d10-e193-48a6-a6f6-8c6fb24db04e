.monitorPage {
  width: 100%;
  // height: fit-content;
  height: 100%;

  .head {
    height: 50px;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;

    .headLeft {
      height: 100%;
      width: 350px;
      position: absolute;
      left: 20px;
      font-size: 15px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .headCenter {
      height: 100%;
      width: 450px;
      text-align: center;
      font-size: 25px;
      font-weight: bold;
      background: url('~@/assets/images/icon_banner_bg.png');
      background-position: bottom center;
      background-repeat: no-repeat;
    }

    .headRight {
      height: 100%;
      max-width: 450px;
      position: absolute;
      right: 20px;
    }
  }

  .topContent {
    height: 80px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 10px;
    padding: 0 10px;
    margin: 10px 0;
  }

  .svgContent {
    height: calc(100% - 50px - 80px - 20px);
    width: 100%;
    // overflow-y: scroll; //必须要有这个，不然svg的hover弹框的window.scrollY始终都是0
  }
}
