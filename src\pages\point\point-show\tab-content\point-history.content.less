.container {
  display: flex;
  gap: 10px;
  height: calc(100vh - 180px);
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-right: 1px solid #1a448d;
  padding-right: 10px;
  flex: 1;
}
.right-column {
  height: 100%;
  width: 400px;
  flex: 0 0 400px; // 固定宽度400px，不伸缩
}

.left-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #1a448d;
}

.left-bottom {
  flex: 1;
  display: flex;
  height: 100%;
}

.use {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 10px;
  :global {
    .ant-segmented {
      background-color: transparent;
      color: #fff;
    }
    .ant-segmented .ant-segmented-item {
      box-shadow: inset 0 0 12px #1577a5;
      border-radius: 0;
    }
    .ant-segmented .ant-segmented-item-selected {
      box-shadow: inset 0 0 8px 1px #409eff;
      background-color: transparent;
      color: #fff;
    }
    .ant-segmented
      .ant-segmented-item:hover:not(.ant-segmented-item-selected):not(
        .ant-segmented-item-disabled
      ) {
      color: #fff;
    }
  }
}

.table-container {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  scrollbar-gutter: stable;
}

// 在你的less文件中添加以下样式
.global-loading {
  position: relative;
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }

    .ant-spin {
      max-height: none;
    }

    .ant-spin-spinning {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.6);
    }
  }
}
