import { useState, useMemo, useCallback } from 'react';
import styles from './point-show.page.less';
import { Tabs } from 'antd';
import { DoubleRightOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import BaseInfo from './tab-content/base-info.content';
import Topology from './tab-content/topology-content/topology-content.page';
import PointHistory from './tab-content/point-history.content';
import { history } from 'umi';
import AssociatedDevices from '../point-list/components/AssociatedDevices/index';
import ClearDevices from '../point-list/components/ClearDevices/index';
import { getPointDetails, getPointValue, getPointRelationInfo } from '@/pages/point/point.service';
import { useMount } from 'ahooks';
import _ from 'lodash';
import { Pointlist } from '@/pages/point/type';
import DetailTop from '@/components/detail-top/detail.top.component';

const PointShow = () => {
  const params = history.location.query;
  const id = params?.id as string;
  const pointCode = params?.pointCode as string; // 测点编码
  const [details, setDetails] = useState<Pointlist.Details>();
  const [pointValue, setPointValue] = useState<Pointlist.PointValue>(); // 测点值
  const [pointRelationInfo, setPointRelationInfo] = useState<Pointlist.PointUnitConfig>(); // 测点绑定单位配置
  const [isDevicesModal, setIsDevicesModal] = useState<boolean>(false); // 关联设备
  const [clearModal, setClearModal] = useState<boolean>(false);
  // 统一处理API请求的函数
  const fetchData = useCallback(async (apiFunc: Function, params: any, setter: Function) => {
    try {
      const res = await apiFunc(params);
      if (res.code === '1') {
        setter(res.data);
        return res.data;
      }
      return null;
    } catch (error) {
      console.error('API请求失败:', error);
      return null;
    }
  }, []);

  // 获取测点值
  const getPointValueAPI = useCallback(
    () => fetchData(getPointValue, { pointCode }, setPointValue),
    [fetchData, pointCode],
  );

  // 获取测点详情数据
  const getPointDetailsAPI = useCallback(
    () => fetchData(getPointDetails, id, setDetails),
    [fetchData, id],
  );

  // 获取测点关联设备
  const getPointRelationInfoAPI = useCallback(
    () => fetchData(getPointRelationInfo, { pointCode }, setPointRelationInfo),
    [fetchData, pointCode],
  );

  useMount(async () => {
    if (id) {
      await getPointValueAPI();
      await getPointDetailsAPI();
      await getPointRelationInfoAPI();
    }
  });

  const tabItems: (TabsProps | any)['items'] = useMemo(
    () => [
      {
        key: 'basic',
        label: '基本信息',
        children: details ? (
          <BaseInfo
            current={details}
            pointValue={pointValue}
            getPointDetailsAPI={getPointDetailsAPI}
            setClearModal={setClearModal}
            setIsDevicesModal={setIsDevicesModal}
            pointRelationInfo={pointRelationInfo}
          />
        ) : null,
      },
      {
        key: 'topology',
        label: '拓扑关系',
        children: details ? <Topology rootInfoData={details} /> : null,
      },
      {
        key: 'pointHistory',
        label: '历史数据',
        children: details ? (
          <PointHistory hasHistory={!!pointValue?.time} details={details} />
        ) : null,
      },
    ],
    [details, pointValue],
  );

  return (
    <div className={styles['page-content']} style={{ height: 'calc(100vh - 60px)' }}>
      <DetailTop
        title="测点详情"
        backFunc={() =>
          history.push('/pointList', {
            ...history.location.state,
          })
        }
      />
      <div className={styles['page-body']}>
        <Tabs defaultActiveKey="basic" items={tabItems} tabBarExtraContent={null} />
      </div>
      <AssociatedDevices
        current={details}
        isDevicesModal={isDevicesModal}
        setIsDevicesModal={setIsDevicesModal}
        onOk={getPointDetailsAPI}
      />
      <ClearDevices
        setIsDevicesModal={setIsDevicesModal}
        current={details}
        clearModal={clearModal}
        setClearModal={setClearModal}
        onOk={getPointDetailsAPI}
      />
    </div>
  );
};
export default PointShow;
