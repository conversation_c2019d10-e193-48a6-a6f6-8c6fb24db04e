declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement;
  const url: string;
  export default url;
}
declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module '@antv/data-set';
declare module 'mockjs';
declare module 'react-fittext';
declare module 'bizcharts-plugin-slider';
declare module 'gs-bim-air';
declare module 'ztree';
declare module 'bpmn-js-properties-panel';
declare module '@bpmn-io/properties-panel';

declare namespace JSX {
  interface IntrinsicElements {
    'bim-air-plugin': any;
    'type-button': any;
    'svg-reader': any;
    'checked-tree': any;
    'upload-file': any;
    'user-selector': any;
    'process-viewer': any;
  }
}

declare namespace Global {
  export type Code = '0' | '1';
  export interface Response<T = any> {
    code: Code;
    data: T;
    error: string;
    message: string;
    path: string;
    status: number;
    timestamp: number;
    flag: boolean;
  }
  export interface Res<U = any> {
    flag: boolean;
    code: string;
    data: U;
    message: string;
  }
}
