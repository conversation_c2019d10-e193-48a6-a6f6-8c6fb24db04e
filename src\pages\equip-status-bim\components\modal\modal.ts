import { ReactNode } from 'react';

export interface PropOptions {
  width?: number;
  height?: number;
  direction: keyof typeof Direction; //方向
  TopOrBottom?: keyof typeof TopBottom; //置顶或者置底,默认居中
  show?: boolean; //是否展示
  children?: ReactNode;
}
export enum Direction {
  left = 'left',
  right = 'right',
}
export enum TopBottom {
  top = 'top',
  bottom = 'bottom',
}

export interface positionOptions {
  top: string;
  bottom: string;
  left: string;
  right: string;
}
