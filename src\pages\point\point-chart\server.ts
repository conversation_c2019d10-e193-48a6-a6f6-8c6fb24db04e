import {
  addOrCancelFavorite,
  deletePointAnalysisList,
  share,
  cancelShare,
  cancelShareWithMe,
  updatePointAnalysisList,
  getPointListHistoryData,
  getPointAnalysisDetail,
  addPointAnalysisList,
} from '@/pages/point/point.service';
import _ from 'lodash';
import { message } from 'antd';
import { PointAnalysis } from '@/pages/point/type';

/**
 * 柯里化通用请求处理函数
 * @param requestFn 请求函数
 * @param successMessage 成功消息
 * @param errorMessage 失败消息
 */
const createRequestHandler =
  <T, R>(
    requestFn: (params: T) => Promise<Global.Response<R>>,
    successMessage: string,
    errorMessage: string,
  ) =>
  async (params: T, callback?: (data: any) => void | Promise<void>) => {
    try {
      const result = await requestFn(params);
      if (_.isEqual(result.code, '1')) {
        successMessage && message.success(successMessage);
        callback?.(result.data);
      } else {
        errorMessage && message.error(errorMessage);
      }
    } catch (error) {
      message.error('接口异常');
      throw error;
    }
  };
// 新增
export const handleAdd = createRequestHandler<PointAnalysis.Add, string>(
  addPointAnalysisList,
  '新增成功',
  '新增失败',
);

// 删除
export const handleDelete = createRequestHandler<string, string>(
  deletePointAnalysisList,
  '删除成功',
  '删除失败',
);

// 收藏
export const handleCollect = createRequestHandler<PointAnalysis.Collect, string>(
  addOrCancelFavorite,
  '收藏成功',
  '收藏失败',
);

// 分享
export const handleShareToOther = createRequestHandler<PointAnalysis.Share, string>(
  share,
  '分享成功',
  '分享失败',
);

// 取消分享
export const handleCancelShare = createRequestHandler<PointAnalysis.CancelShare, string>(
  cancelShare,
  '取消分享成功',
  '取消分享失败',
);

// 取消分享给我
export const handleCancelShareWithMe = createRequestHandler<
  PointAnalysis.CancelShareWithMe,
  string
>(cancelShareWithMe, '取消分享成功', '取消分享失败');

// 保存
export const handleSave = createRequestHandler<PointAnalysis.Update, string>(
  updatePointAnalysisList,
  '保存成功',
  '保存失败',
);

// 获取测点分析详情
export const handleGetPointAnalysisDetail = createRequestHandler<string, PointAnalysis.Detail>(
  getPointAnalysisDetail,
  '',
  '获取测点分析详情失败',
);

// 获取历史数据
export const handleGetHistoryData = async (
  params: PointAnalysis.HistoryQuery,
  callback?: (data: PointAnalysis.History[]) => void | Promise<void>,
) => {
  try {
    const result = await getPointListHistoryData(params);
    if (!_.isEqual(result.code, '1')) {
      message.error('获取历史数据失败');
      return [];
    }
    if (_.isNull(result.data)) {
      message.warning('暂无历史数据');
      return [];
    }
    callback?.(result.data);
    return result.data;
  } catch (error) {
    message.error('获取历史数据失败');
    return [];
  }
};
