import { Badge, Empty, But<PERSON>, Modal, DatePicker, Form, Input, message, Popconfirm, Select, Space, Table } from 'antd';
import { Line } from '@antv/g2plot';
import { SearchOutlined, RedoOutlined } from '@ant-design/icons';
import * as React from 'react';
import { useEffect, useState, useRef } from 'react';
import type { ColumnsType } from 'antd/es/table';
import { getHistoryPointList } from '../../point.service';
import styles from './historyModal.less';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import dayjs from 'dayjs';

interface Iprops {
  id: string;
  isHistoryModal: boolean;
  setIsHistoryModal: (is: boolean) => void;
}

const HistoryModal: React.FC<Iprops> = (props) => {
  const { isHistoryModal, setIsHistoryModal, id } = props;
  const [chart, setChart] = useState<Line>();
  const chartRef = useRef<any>(null);

  const [dataSource, setDataSource] = useState<any[]>([]);

  /** 查询 */
  const submit = () => {
    //下方是mock数据
    // const data = [
    //   {
    //     time: 1736847240111,
    //     value: 2.1,
    //   },
    //   {
    //     time: 1736847340222,
    //     value: 2.2,
    //   },
    //   {
    //     time: 1736847440333,
    //     value: 2.3,
    //   },
    //   {
    //     time: 1736847540444,
    //     value: 2,
    //   },
    //   {
    //     time: 1736847640555,
    //     value: 2.8,
    //   },
    //   {
    //     time: 1736847740666,
    //     value: 2.6,
    //   },
    //   {
    //     time: 1736847840777,
    //     value: 2.8,
    //   },
    // ];
    // let arr = data.map((item: any) => {
    //   return {
    //     ...item,
    //     time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss'),
    //   };
    // });
    // setDataSource(arr);
    // return;

    let query = {
      keyList: [id],
    };

    //调查询接口
    getHistoryPointList(query)
      .then((res) => {
        if (res.flag) {
          let arr = res.data.map((item: any) => {
            return {
              ...item,
              time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss'),
            };
          });
          setDataSource(arr);
        }
      })
      .catch((err) => {});
  };

  useEffect(() => {
    if (isHistoryModal) {
      submit();
    } else if (chart) {
      chart.destroy();
    }
  }, [isHistoryModal]);
  useEffect(() => {
    if (dataSource.length > 0 && isHistoryModal) {
      chartInit();
    }
  }, [dataSource]);

  function chartInit() {
    const line: any = new Line(chartRef.current, {
      data: dataSource,
      xField: 'time',
      yField: 'value',
      point: {
        shapeField: 'square',
        sizeField: 4,
      },
      interaction: {
        tooltip: {
          marker: false,
        },
      },
      style: {
        lineWidth: 2,
      },
    });
    line.render();
    setChart(line);
  }

  return (
    <SvgModal
      width="1200px"
      height="600px"
      close={() => {
        setIsHistoryModal(false);
      }}
      isFooterBtn={false}
      open={isHistoryModal}
      title="测点历史记录"
    >
      <div ref={chartRef} style={{ width: '100%', height: '100%' }}>
        {dataSource.length === 0 ? (
          <div className={styles.myDivCenter}>
            <Empty />
          </div>
        ) : null}
      </div>
    </SvgModal>
  );
};
export default HistoryModal;
