import { request, jsonHeader } from '@/utils/request';
import { ResponseOptions } from '../monitoring-point-annotation';
import { PageOptions } from './template-management';
export interface ReportAttributeOptions {
  attributeName: string; //属性label
  code: string;
  createDate: string;
  createUserId: string;
  dataType: string;
  dataUnit: string; //单位
  id: string;
  isRequired: string; //是否必填
  planCycle: PlanTypeEnum;
}
export interface ReportAddOptions {
  attributeId: string;
  value: string;
}
export interface ReportUpdateOptions {
  submissionId: string;
  name: string;
  value: string;
}
export interface PageParams {
  pageNum: number;
  pageSize: number;
  planCycle: PlanTypeEnum;
  value: string;
  startDate?: string;
  endDate?: string;
}
export enum PlanTypeEnum {
  YEAR = 'year',
  MONTH = 'month',
  QUARTER = 'quarter',
}
//查询报表填写属性列表
export function planAttributeList(): Promise<ResponseOptions<ReportAttributeOptions[]>> {
  return request.get('/PlanAttribute/list');
}
//新增报表计划
export function planReportAdd(data: ReportAddOptions[]): Promise<ResponseOptions<any>> {
  return request.post('/PlanData/insert', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//更新报表计划
export function planReportUpdate(data: ReportUpdateOptions[]): Promise<ResponseOptions<any>> {
  return request.post('/PlanData/update', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//报表计划分页查询
export function listPage(params: PageParams): Promise<ResponseOptions<PageOptions<any>>> {
  return request.get('/PlanData/listPage', { params });
}
//删除报表计划
export function planReportDel(submissionId: string): Promise<ResponseOptions<any>> {
  return request.post(`/PlanData/${submissionId}/delete`);
}
//报表详情
export function reportDetail(submissionId: string): Promise<ResponseOptions<any>> {
  return request.get(`/PlanData/${submissionId}/view`);
}
