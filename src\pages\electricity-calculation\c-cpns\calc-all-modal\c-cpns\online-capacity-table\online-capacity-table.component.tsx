import { memo, useEffect, useState } from 'react';
import type { TableProps } from 'antd';
import { ConfigProvider, Table } from 'antd';
import type { FC, ReactNode } from 'react';

import { OnlineCapacityTableWrapper } from './online-capacity-table.style';

type RecordType = {
  key: string;
  capacity: string;
  direction: string;
  羊加I线: string;
  羊加II线: string;
  全厂: string;
};

interface IProps {
  children?: ReactNode;
  calcResult: {
    [key: string]: string;
  };
}

const DetailTable: FC<IProps> = (props) => {
  const { calcResult } = props;

  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  useEffect(() => {
    setDataSource([
      {
        key: '1',
        capacity: '期初电字',
        direction: '正向',
        羊加I线: calcResult['上网羊加I线正向期初电字'],
        羊加II线: calcResult['上网羊加II线正向期初电字'],
        全厂: '/',
      },
      {
        key: '2',
        capacity: '_',
        direction: '反向',
        羊加I线: calcResult['上网羊加I线反向期初电字'],
        羊加II线: calcResult['上网羊加II线反向期初电字'],
        全厂: '/',
      },
      {
        key: '3',
        capacity: '期末电字',
        direction: '正向',
        羊加I线: calcResult['上网羊加I线正向期末电字'],
        羊加II线: calcResult['上网羊加II线正向期末电字'],
        全厂: '/',
      },
      {
        key: '4',
        capacity: '_',
        direction: '反向',
        羊加I线: calcResult['上网羊加I线反向期初电字'],
        羊加II线: calcResult['上网羊加II线反向期初电字'],
        全厂: '/',
      },
      {
        key: '5',
        capacity: '电字差',
        direction: '正向',
        羊加I线: calcResult['上网羊加I线正向电字差'],
        羊加II线: calcResult['上网羊加II线正向电字差'],
        全厂: calcResult['上网正向电字差合计'],
      },
      {
        key: '6',
        capacity: '_',
        direction: '反向',
        羊加I线: calcResult['上网羊加I线反向电字差'],
        羊加II线: calcResult['上网羊加II线反向电字差'],
        全厂: calcResult['上网反向电字差合计'],
      },
      {
        key: '7',
        capacity: '电量',
        direction: '正向',
        羊加I线: calcResult['上网羊加I线正向电量'],
        羊加II线: calcResult['上网羊加II线正向电量'],
        全厂: calcResult['上网正向合计电量'],
      },
      {
        key: '8',
        capacity: '_',
        direction: '反向',
        羊加I线: calcResult['上网羊加I线反向电量'],
        羊加II线: calcResult['上网羊加II线反向电量'],
        全厂: calcResult['上网反向合计电量'],
      },
      {
        key: '9',
        capacity: '上网合计',
        direction: '_',
        羊加I线: calcResult['上网反向合计电量'],
        羊加II线: '_',
        全厂: '_',
      },
      {
        key: '10',
        capacity: '下网合计',
        direction: '_',
        羊加I线: calcResult['上网正向合计电量'],
        羊加II线: '_',
        全厂: '_',
      },
    ]);
  }, [calcResult]);

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'name',
      align: 'center',
      title: '名称',
      dataIndex: 'name',
      width: '11%',
      children: [
        {
          key: 'capacity',
          align: 'center',
          title: '电量',
          dataIndex: 'capacity',
          rowScope: 'row',
          onCell: (data: RecordType) => ({
            colSpan: ['上网合计', '下网合计'].includes(data.capacity) ? 2 : 1,
            rowSpan: ['期初电字', '期末电字', '电字差', '电量'].includes(data.capacity)
              ? 2
              : data.capacity === '_'
              ? 0
              : 1,
          }),
        },
      ],
    },
    {
      key: 'direction',
      align: 'center',
      title: '',
      dataIndex: 'direction',
      width: '11%',
      onCell: (data: RecordType) => ({
        colSpan: ['上网合计', '下网合计'].includes(data.capacity) ? 0 : 1,
      }),
    },
    {
      key: '羊加I线',
      align: 'center',
      title: '羊加I线',
      dataIndex: '羊加I线',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: ['上网合计', '下网合计'].includes(data.capacity) ? 3 : 1,
      }),
    },
    {
      key: '羊加II线',
      align: 'center',
      title: '羊加II线',
      dataIndex: '羊加II线',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: ['上网合计', '下网合计'].includes(data.capacity) ? 0 : 1,
      }),
    },
    {
      key: '全厂',
      align: 'center',
      title: '全厂',
      dataIndex: '全厂',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: ['上网合计', '下网合计'].includes(data.capacity) ? 0 : 1,
      }),
    },
  ];

  const tableHeaderRenderer = () => (
    <div style={{ position: 'relative' }}>
      <span>二、上网电量</span>
      <span
        style={{
          position: 'absolute',
          left: '0',
          right: '0',
          margin: 'auto',
          textAlign: 'center',
        }}
      >
        单位：万千瓦时
      </span>
      <span
        style={{
          position: 'absolute',
          right: '0',
        }}
      >
        倍率：990
      </span>
    </div>
  );

  return (
    <OnlineCapacityTableWrapper>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2f4f81',
            },
          },
        }}
      >
        <Table
          bordered
          size="small"
          title={tableHeaderRenderer}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </ConfigProvider>
    </OnlineCapacityTableWrapper>
  );
};

export default memo(DetailTable);
