.box {
  display: flex;
  margin: -16px -16px 0;
  padding: 0 16px;
  height: calc(100% + 16px * 1);
  height: calc(100vh - 200px);
  align-items: stretch;
  position: absolute;
  z-index: 2;
  width: 100%;

  .process {
    width: 40%;
  }

  .img {
    width: 60%;
    border-left: 1px solid rgba(65, 174, 251, 0.3);
  }

  .process,
  .img {
    padding: 0 10px 0px 25px;
  }

  .title {
    margin-top: 15px;
    font-size: 16px;
    color: #fff;
    margin-bottom: 15px;
  }

  .content {
    height: calc(100% - 60px);
    color: #fff;
    overflow: auto;

    .taskUserName {
      font-size: 14px;
      padding: 5px 6px;
      border: 1px solid #0d6ee4;
      border-radius: 5px;
      max-height: 56px;
      overflow-y: scroll;
      color: #01b7ff;

      span {
        color: #fff;
      }
    }
  }
}

.custom-react-node {
  width: 100px;
  height: 40px;
  background: #02aafa;
  border-radius: 10px;
  text-align: center;
  line-height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
