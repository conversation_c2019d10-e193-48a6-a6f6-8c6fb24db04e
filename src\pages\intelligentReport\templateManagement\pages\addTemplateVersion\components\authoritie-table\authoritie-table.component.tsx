import { AuthoritieOptions, DataTypeEnum } from '@/components/spread-table/spread-table';
import { ColumnsOptions } from '@/components/table-component/table';
import { TableComponent } from '@/components/table-component/table-component';
import { Input, Space, Tooltip, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { FC, ReactNode, forwardRef, memo, useImperativeHandle } from 'react';
import { PropOptions, UseImperativeOptions } from './authoritie-table';
import { CheckOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from '../../add-template-version.page.less';
import { hasIntersection, validateExcelPosition } from '@/utils/utils';

const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const AuthoritieTable: FC<PropOptions> = memo(
  forwardRef(
    ({ inputFocusFn, tableList, tableListChange, allTableList, isEdit, spreadRef }, ref) => {
      useImperativeHandle<any, UseImperativeOptions>(ref, () => ({
        verifyPermis,
        cellBackHandler,
        cellBackBatchHandler,
        addPermisRange,
        tablePermisEvent,
      }));
      //表格字段配置
      const columns: ColumnsType<ColumnsOptions> = [
        {
          title: '开始',
          dataIndex: 'startPosition',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => tableInput(record, 'startPosition'),
        },
        {
          title: '结束',
          dataIndex: 'endPosition',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => tableInput(record, 'endPosition'),
        },
        {
          title: '行数',
          dataIndex: 'rowCount',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '列数',
          dataIndex: 'colCount',
          align: 'center',
          ellipsis: true,
        },
        {
          title: '操作',
          render: (text: string, record: any) => controllerComponent(record),
          align: 'center',
          ellipsis: true,
        },
      ];
      //新增权限选区
      function addPermisRange(): void {
        const table = getWorkTableData();
        const data: AuthoritieOptions = {
          id: new Date().getTime().toString(),
          tableId: table.name(),
          tableName: table.name(),
          startPosition: '', //开始行
          endPosition: '', //开始列
          rowCount: 0, //行数
          colCount: 0, //列数
          editUserId: userId, //可操作用户id
          isEdit: true,
        };
        tableListChange((prev) => {
          return prev.concat(data);
        });
      }
      //表格权限选区事件
      function tablePermisEvent(): void {
        const data = spreadRef.current.getActiveCell();
        if (data) {
          const table = getWorkTableData();
          const permis: AuthoritieOptions = {
            id: new Date().getTime().toString(),
            tableId: table.name(),
            tableName: table.name(),
            startPosition: data.start, //开始
            endPosition: data.end, //结束
            rowCount: data.selection.rowCount, //行数
            colCount: data.selection.colCount, //列数
            editUserId: userId, //操作用户id
            isEdit: false,
          };
          tableListChange((prev) => {
            const verifyList = prev.filter((item) => item.tableId === permis.tableId);
            if (verifyPermis(permis, verifyList)) {
              const list = prev.concat(permis);
              cellBackHandler(permis);
              return list;
            } else {
              message.warning('权限选区有重复！');
              return prev;
            }
          });
        }
      }
      //Input聚焦处理
      function onFoucsHandler(data: AuthoritieOptions): void {
        if (data.isEdit) {
          inputFocusFn(data, DataTypeEnum.AUTHORITIE);
        }
      }
      //表格输入框
      function tableInput(data: AuthoritieOptions, field: string): ReactNode {
        if (data.isEdit) {
          return (
            <Input
              onFocus={() => onFoucsHandler(data)}
              onChange={(e) => tableInputChange(e, field, data)}
              value={data[field]}
            />
          );
        } else {
          return data[field];
        }
      }
      //表格输入框改变
      function tableInputChange(e: any, field: string, data: AuthoritieOptions): void {
        const list = allTableList.map((item) => {
          if (item.id === data.id) {
            item[field] = e.target.value.toUpperCase();
          }
          return item;
        });
        tableListChange(list);
      }
      //权限表格操作栏渲染
      function controllerComponent(data: AuthoritieOptions): ReactNode {
        const del = (
          <Tooltip title="删除">
            <DeleteOutlined onClick={() => delTableItem(data)} className={styles.icon} />
          </Tooltip>
        );
        const sure = (
          <Tooltip title="确定">
            <CheckOutlined onClick={() => sureTableData(data)} className={styles.icon} />
          </Tooltip>
        );
        return (
          <div className={styles['table-controller-container']}>
            {isEdit && (
              <Space size="middle">
                {data.isEdit && sure}
                {del}
              </Space>
            )}
          </div>
        );
      }
      //确定提交表格数据
      function sureTableData(data: AuthoritieOptions): void {
        // 校验两个输入框
        const row = validateExcelPosition(data.startPosition);
        const column = validateExcelPosition(data.endPosition);
        if (!row.valid || !column.valid) {
          message.warning('请输入有效的Excel位置，例如 "A1"、"B2"');
        } else if (row.column) {
          if (
            verifyPermis(
              data,
              tableList.filter((item) => item.id !== data.id),
            )
          ) {
            const table = getWorkTableData();
            //单元格区域数据
            const cellData = table.getRange(`${data.startPosition}:${data.endPosition}`);
            cellBackHandler(data);
            const list = allTableList.map((item) => {
              if (item.id === data.id) {
                item.isEdit = false;
                item.colCount = cellData.colCount;
                item.rowCount = cellData.rowCount;
              }
              return item;
            });
            tableListChange(list);
          } else {
            message.warning('选区重复');
          }
        }
      }
      //校验权限选区是否重复或者有交集
      function verifyPermis(data: AuthoritieOptions, list: AuthoritieOptions[]): boolean {
        const table = getWorkTableData();
        const { startPosition: startP, endPosition: endP } = data;
        return !list.some((item) => {
          const { startPosition: start, endPosition: end } = item;
          if (item.startPosition === data.startPosition && item.endPosition === data.endPosition) {
            return true;
          } else if (
            table &&
            table.getRange &&
            hasIntersection(table.getRange(`${startP}:${endP}`), table.getRange(`${start}:${end}`))
          ) {
            return true;
          } else {
            return false;
          }
        });
      }
      //删除表格子项
      function delTableItem(data: AuthoritieOptions): void {
        const list = allTableList.filter((item) => item.id !== data.id);
        tableListChange(list);
        if (!data.isEdit) {
          cellBackHandler(data, 1);
        }
      }
      //背景色修改
      function cellBackHandler(data: AuthoritieOptions, status: 0 | 1 = 0): void {
        spreadRef.current.cellBackChange(data, status, '#9995');
      }
      //背景色批量修改
      function cellBackBatchHandler(list: AuthoritieOptions[] = [], status?: 0 | 1): void {
        list.forEach((item) => {
          cellBackHandler(item, status);
        });
      }
      //获取当前工作表数据
      function getWorkTableData(): any {
        const sheetSnapshot = spreadRef.current.getWorkTableData();
        return sheetSnapshot;
      }
      return (
        <>
          <TableComponent isIndex={false} columns={columns} tableList={tableList}></TableComponent>
        </>
      );
    },
  ),
);
