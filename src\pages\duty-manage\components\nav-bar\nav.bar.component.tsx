import React from 'react';
import { DoubleRightOutlined } from '@ant-design/icons';
import { history } from 'umi';
import styles from './nav.bar.component.less';

interface NavBarComponentProps {
  title?: string; // 标题
  rightComponent?: React.ReactNode; // 右侧组件
  onBack?: () => void; // 返回按钮点击事件
  showBack?: boolean; // 是否显示返回按钮
}

export default function NavBarComponent({
  title = '值班日志详情',
  rightComponent,
  onBack,
  showBack = false,
}: NavBarComponentProps) {
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      history.goBack();
    }
  };

  return (
    <div className={styles.navBar}>
      <div className={styles.leftSection}>
        <h2 className={styles.title}>{title}</h2>
      </div>

      <div className={styles.rightSection}>
        {rightComponent && <div className={styles.customComponent}>{rightComponent}</div>}
        {showBack && (
          <div className={styles.backButton} onClick={handleBack}>
            <span className={styles.backButtonText}>返回</span>
            <DoubleRightOutlined />
          </div>
        )}
      </div>
    </div>
  );
}
