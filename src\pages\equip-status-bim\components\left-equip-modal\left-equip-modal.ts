import moment from 'moment';
import { PointOptions } from '../../equip-status-bim';
import { Client } from '@stomp/stompjs';

export interface PropOptions {
  pointData: PointOptions[];
  open: boolean;
  close: () => void; //关闭方法
  socketClient?: Client;
  title: string;
  unit: string; //单位
}
export interface DataOptions {
  label: string;
  value: string | number;
  type: string;
  color?: string;
  key?: string;
}
export interface SortOptions {
  label: string;
  top3: DataOptions[];
  min: DataOptions;
}
export interface HistoryOptions {
  time: number;
  value: number;
}

export const TEST_LIST: DataOptions[] = [
  // ...rondomHandler({total:12,min:44,max:49,name:"点5"}),
  // ...rondomHandler({total:12,min:43,max:48,name:"点4"}),
  // ...rondomHandler({total:12,min:42,max:47,name:"点3"}),
  // ...rondom<PERSON><PERSON><PERSON>({total:12,min:41,max:46,name:"点2"}),
  // ...rondomHandler({total:12,min:40,max:45,name:"点1"}),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 40 + index;
    if (total > 45) {
      total = 45 - (index - 5);
    }
    if (total < 40) {
      total = 40 + (index % 5);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点1',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 41 + index;
    if (total > 45) {
      total = 45 - (index - 1);
    }
    if (total < 41) {
      total = 41 + (index % 1);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点2',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 42 + index;
    if (total > 45) {
      total = 45 - (index - 3);
    }
    if (total < 42) {
      total = 42 + (index % 3);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点3',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 43 + index;
    if (total > 45) {
      total = 45 - (index - 2);
    }
    if (total < 43) {
      total = 43 + (index % 2);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点4',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 43 + index;
    if (total > 45) {
      total = 45 - (index - 2);
    }
    if (total < 43) {
      total = 43 + (index % 2);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点5',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 41 + index;
    if (total > 45) {
      total = 45 - (index - 4);
    }
    if (total < 41) {
      total = 41 + (index % 4);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点6',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 42 + index;
    if (total > 45) {
      total = 45 - (index - 3);
    }
    if (total < 42) {
      total = 42 + (index % 3);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点7',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 42 + index;
    if (total > 45) {
      total = 45 - (index - 3);
    }
    if (total < 42) {
      total = 42 + (index % 3);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点8',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 40 + index;
    if (total > 44) {
      total = 44 - (index - 4);
    }
    if (total < 40) {
      total = 40 + (index % 4);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点9',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 41 + index;
    if (total > 44) {
      total = 44 - (index - 3);
    }
    if (total < 41) {
      total = 41 + (index % 3);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点10',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 40 + index;
    if (total > 43) {
      total = 43 - (index - 3);
    }
    if (total < 40) {
      total = 40 + (index % 3);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点11',
    };
  }),
  ...new Array(12).fill(1).map((item, index) => {
    let total = 40 + index;
    if (total > 45) {
      total = 45 - (index - 5);
    }
    if (total < 40) {
      total = 40 + (index % 5);
    }
    return {
      label: `11/01 ${13 + index}:00`,
      value: total.toFixed(1),
      type: '点12',
    };
  }),
];

enum TopColoe {
  '#F05805',
  '#EF8E00',
  '#F8CA0F',
}
//数据分组排序
export function dataGroudSort(
  data: any[],
  { labels = 'label', types = 'type', value = 'value' },
): SortOptions[] {
  const groupedData = data.reduce((acc, item) => {
    if (!acc[item[labels]]) {
      acc[item[labels]] = [];
    }
    acc[item[labels]].push(item);
    return acc;
  }, {});
  // 找出每个时间点数据最高得前三个和最低的那个
  const result = Object.keys(groupedData).map((label) => {
    const values = groupedData[label].map((item: any) => ({
      ...item,
      value: item[value],
      type: item[types],
    }));

    // 按照 value 排序，从高到低
    values.sort((a: any, b: any) => parseFloat(b[value]) - parseFloat(a[value]));

    // 取出前三个最高值和最低值
    const top3 = values.slice(0, 3).map((item: DataOptions, index: number) => {
      return {
        ...item,
        color: TopColoe[index],
      };
    });
    const min = {
      value: values[values.length - 1].value,
      type: values[values.length - 1].type,
      label: values[values.length - 1].label,
      color: '#3FBC24',
    };

    return {
      label,
      top3,
      min,
    };
  });
  return JSON.parse(JSON.stringify(result));
}
