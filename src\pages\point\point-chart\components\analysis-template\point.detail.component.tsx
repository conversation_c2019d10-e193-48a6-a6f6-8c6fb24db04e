import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import {
  TableOutlined,
  SettingOutlined,
  CloseOutlined,
  DeleteOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import styles from './point.detail.component.less';
import { Tabs, Table } from 'antd';
import type { TabsProps } from 'antd';
import { getPointHistoryData, exportHistoryData } from '@/pages/point/point.service';
import { ITemplates } from '@/pages/point/point-chart/point-analysis.page';
import { SettingModal } from '../setting.modal/setting.modal.component';
import { usePointAnalysisContext } from '@/pages/point/point-chart/point-analysis.provider';
import classnames from 'classnames';
import { PointTable } from '../table.modal/point.table.component';
import { showLoading, hideLoading } from '@/utils/global-loading';
import useQueryParams from '@/hooks/useQueryParams';

interface PointDetailProps {
  pointItem: ITemplates['basePointDTOS'][0];
  points: ITemplates;
  handleClose: () => void;
  handleRemovePoint: (point: ITemplates['basePointDTOS'][0]) => void;
}
type TabKey = 'max' | 'min';
const PointDetailComponent: React.FC<PointDetailProps> = ({
  points,
  pointItem,
  handleClose,
  handleRemovePoint,
}) => {
  const { templates, saveSync, getTime } = usePointAnalysisContext();
  const { type } = useQueryParams();
  const isEdit = useMemo(() => type === 'edit', [type]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<TabKey>('max');
  const [tableData, setTableData] = useState<any[]>([]);
  const [disableClickOutside, setDisableClickOutside] = useState(false);
  const [maxAndMin, setMaxAndMin] = useState({ max: 0, min: 0 });

  // 最大限值
  const pointExceedsLimits = useMemo(
    () => pointItem?.pointExceedsLimits ?? points?.exceedsLimits,
    [pointItem, points],
  );
  const pointExceedsLimitsText = useMemo(
    () => pointExceedsLimits ?? '暂无设置',
    [pointExceedsLimits],
  );
  // 最小限值
  const pointlessThanLimits = useMemo(
    () => pointItem?.pointlessThanLimits ?? points?.lessThanLimits,
    [pointItem, points],
  );
  const pointlessThanLimitsText = useMemo(
    () => pointlessThanLimits ?? '暂无设置',
    [pointlessThanLimits],
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        !disableClickOutside
      ) {
        handleClose(); // 关闭
      }
    };
    document.addEventListener('mousedown', handleClickOutside); // 点击外部
    return () => {
      document.removeEventListener('mousedown', handleClickOutside); // 移除事件
    };
  }, [handleClose, disableClickOutside]);

  // 获取数据
  const getData = async (tab: TabKey = 'max') => {
    const { selectBegin, selectEnd } = getTime('selectBegin', 'selectEnd');
    const params = {
      key: pointItem?.pointCode || '',
      // 限值
      ...(tab === 'max'
        ? // 大于限值
          { ...(pointExceedsLimits != null ? { max: pointExceedsLimits } : {}) }
        : // 小于限值
          { ...(pointlessThanLimits != null ? { min: pointlessThanLimits } : {}) }),
      selectBegin,
      selectEnd,
    };
    const result = await getPointHistoryData(params);
    setTableData(result?.data || []);
    if (result.data && result.data.length) {
      const { max, min } = getMaxAndMin(result.data);
      setMaxAndMin({ max, min });
    }
  };

  useEffect(() => {
    getData(activeTab);
  }, [pointItem]);

  // 获取对象数组种最大和最小值
  const getMaxAndMin = useCallback((data: any[]) => {
    const max = Math.max(...data.map((item) => item.value)) || 0;
    const min = Math.min(...data.map((item) => item.value)) || 0;
    return { max, min };
  }, []);

  // 切换tab
  const handleChange = useCallback(
    async (key: string) => {
      setActiveTab(key as TabKey);
      await getData(key as TabKey);
    },
    [getData],
  );

  // 设置保存
  const handleSaveSetting = useCallback(
    async (params: any) => {
      setDisableClickOutside(false);
      const newTem = templates.map((template) => {
        if (template.ord === points.ord) {
          return {
            ...template,
            basePointDTOS: template.basePointDTOS.map((point) => {
              if (point.pointCode === pointItem?.pointCode) {
                return {
                  ...point,
                  pointExceedsLimits: params?.greaterThan,
                  pointlessThanLimits: params?.lessThan,
                };
              }
              return point;
            }),
          };
        }
        return template;
      });
      await saveSync(newTem);
    },
    [saveSync, templates, points, pointItem],
  );

  // 点击导出
  const handleClickExport = useCallback(async () => {
    const params = {
      basePointList: [pointItem],
      ...(getTime('selectBegin', 'selectEnd') as { selectBegin: string; selectEnd: string }),
    };
    showLoading();
    await exportHistoryData(params);
    hideLoading();
  }, [getTime, pointItem]);

  // 点击历史缺陷列表
  const handleClickHistory = useCallback(() => {
    setDisableClickOutside(true);
    PointTable.show({
      data: pointItem,
      closeCallback: () => setDisableClickOutside(false),
    });
  }, [pointItem]);

  // 点击设置
  const handleClickSetting = useCallback(() => {
    setDisableClickOutside(true);
    SettingModal.show({
      callback: handleSaveSetting,
      closeCallback: () => setDisableClickOutside(false),
      initialValues: pointItem,
    });
  }, [handleSaveSetting, pointItem]);

  // 关闭弹窗时重置状态
  const handleCloseWrapper = useCallback(() => {
    handleClose();
  }, [handleClose]);

  const TableComponent = () => (
    <div className={styles.tableWrapper}>
      <Table
        rowKey={(_record, index) => String(index) + activeTab}
        columns={[
          {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            render: (_text, _record, index) => index + 1,
          },
          {
            title: '值',
            dataIndex: 'value',
            key: 'value',
            width: 120,
          },
          {
            title: '时间',
            dataIndex: 'time',
            key: 'time',
          },
        ]}
        dataSource={tableData}
        pagination={{
          pageSize: 5,
          showSizeChanger: false,
          showQuickJumper: false,
          showTotal: (total) => `共 ${total} 条`,
        }}
        size="small"
      />
    </div>
  );

  const items: TabsProps['items'] = [
    {
      key: 'max',
      label: '大于限值',
      children: <TableComponent />,
    },
    {
      key: 'min',
      label: '小于限值',
      children: <TableComponent />,
    },
  ];

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>
        <h2 className={styles.title}>测点信息</h2>
        <div className={styles.icons}>
          {isEdit && (
            <>
              <SettingOutlined className={styles.icon} onClick={handleClickSetting} />
              <DeleteOutlined
                className={styles.icon}
                onClick={() => handleRemovePoint(pointItem)}
              />
            </>
          )}
          <ExportOutlined className={styles.icon} onClick={handleClickExport} />
          <TableOutlined className={styles.icon} onClick={handleClickHistory} />
          <CloseOutlined className={styles.icon} onClick={handleCloseWrapper} />
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.item}>
          <span className={styles.label}>测点名称：</span>
          <span className={styles.value}>{pointItem?.pointName}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>测点编码：</span>
          <span className={styles.value}>{pointItem?.pointCode}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>测点类型：</span>
          <span className={styles.value}>{pointItem?.pointType}</span>
        </div>
        <div className={classnames(styles.item)}>
          <div className={classnames(styles.item, styles.maxAndMin)}>
            <span className={styles.label}>最大值：</span>
            <span className={styles.value}>{maxAndMin.max}</span>
          </div>
          <div className={classnames(styles.item, styles.maxAndMin)}>
            <span className={styles.label}>最小值：</span>
            <span className={styles.value}>{maxAndMin.min}</span>
          </div>
        </div>
        <div className={classnames(styles.item)}>
          <div className={classnames(styles.item, styles.maxAndMin)}>
            <span className={styles.label}>设置最大限值：</span>
            <span className={styles.value}>{pointExceedsLimitsText}</span>
          </div>
          <div className={classnames(styles.item, styles.maxAndMin)}>
            <span className={styles.label}>设置最小限值：</span>
            <span className={styles.value}>{pointlessThanLimitsText}</span>
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <Tabs items={items} onChange={handleChange} />
      </div>
    </div>
  );
};

export default PointDetailComponent;
