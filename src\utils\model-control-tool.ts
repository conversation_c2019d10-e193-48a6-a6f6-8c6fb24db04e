import { deepForEachFn } from './data-processing-tools';

// 模型配置，默认工具栏全隐藏
export const defaultOptions = {
  toolbarVisible: {
    setting: false, // 设置按钮
    twoDDrawing: false, // 底部二维图纸钮
    floorPlan: false, // 楼层功能
    structureTree: false, // 模型结构树
    rightToolbar: false,
    bottomToolbar: false,
  },
  components: {
    compass: {
      visible: false,
    },
  },
};

// 传入模型id和构件id，获取构件对象
export const getSegmentObject = (modelId: any[], componentId: string, viewer: any) => {
  return (
    modelId
      ?.map((item: any) => {
        return viewer.getComponent(item, componentId);
      })
      .filter((item) => item !== null)?.[0] || null
  );
};

// 传入构件对象和视图对象，定位并高亮这个构件
export const handleLocateModel = (segmentObject: any[], viewer: any) => {
  if (segmentObject.length) {
    // 定位构件
    viewer.locateObject([...segmentObject]);
    // 高亮
    viewer.selectionManager.clearAllSelectionAndHighlight();
    viewer.selectionManager.selectAndHighlight([...segmentObject]);
    viewer.updateDisplay();
  } else {
    viewer.selectionManager.clearAllSelectionAndHighlight();
    viewer.updateDisplay();
  }
};

// 传入构件名称和视图对象，定位并高亮这个构件
export const handleLocateModelByName = (nodeName: string, viewer: any) => {
  if (nodeName) {
    let treeNode = viewer.structureTreeManager.getTreeNodesByName(nodeName);
    viewer.locateObject(treeNode.items[0]);

    viewer.selectionManager.clearAllSelectionAndHighlight();
    viewer.highlightManager.highlight([treeNode.items[0].segmentObject]);
    viewer.updateDisplay();
  } else {
    viewer.selectionManager.clearAllSelectionAndHighlight();
    viewer.updateDisplay();
  }
};

// 设置相机位置
export const setCamera = (viewer: any, aboutCamera: { position: any[]; target: any[]; up: any[]; width: number; height: number }) => {
  // 设置相机位置
  viewer.camera.position = aboutCamera.position;
  viewer.camera.target = aboutCamera.target;
  viewer.camera.up = aboutCamera.up;
  viewer.camera.setField(aboutCamera.width, aboutCamera.height);
  viewer.updateDisplay();
};

// 原生js生成模型标签
export const addLabel = (item: any) => {
  // 最外层盒子用于初步定位
  let div = document.createElement('div');
  div.style.position = 'absolute';
  // 图标
  let div2 = document.createElement('div');
  div.append(div2);
  div2.setAttribute('data-modelId', item.modelId);
  div2.setAttribute('data-componentId', item.componentId);
  div2.className = `modelIcon`;
  div2.id = `modelIcon-${item.modelId}-${item.componentId}`;
  const div2Style = {
    position: 'absolute',
    width: '32px',
    height: '69px',
    left: '-16px',
    top: '-66px',
    backgroundImage: `url(${item.icon})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  };
  for (let key in div2Style) {
    div2.style[key] = div2Style[key];
  }
  // 弹框
  let div3 = document.createElement('div');
  div.append(div3);
  let text = document.createTextNode(item.title);
  div3.appendChild(text);
  const div3Style = {
    position: 'absolute',
    width: 'fit-content',
    whiteSpace: 'nowrap',
    padding: '0 10px',
    lineHeight: '40px',
    height: '40px',
    left: '50%',
    transform: 'translate(-50%, 0)',
    top: '-110px',
    background: 'rgba(0,0,0,0.6)',
    border: '1px solid #0D7EAA',
    display: 'none',
  };
  for (let key in div3Style) {
    div3.style[key] = div3Style[key];
  }
  // 点击事件盒子
  let div4 = document.createElement('div');
  div2.append(div4);
  const div4Style = {
    position: 'absolute',
    width: '32px',
    height: '34px',
    left: '0px',
    top: '0px',
  };
  for (let key in div4Style) {
    div4.style[key] = div4Style[key];
  }
  // 点击图标显示隐藏弹框
  div4.addEventListener('click', () => {
    if (div3.style.display === 'none') {
      div3.style.display = 'block';
    } else {
      div3.style.display = 'none';
    }
  });
  return div;
};

// 树节点选中，放大并高亮模型构件
export const handleSelectAndLocateModel = (lightmodel: any, modelId: any, componentId: any, viewer: any) => {
  // 视角锁定模型结构并高亮
  const treeNodeObject = [
    lightmodel?.filter((item: any) => {
      return modelId === item.modelId;
    })?.[0]?.treeNodeObject,
  ]; //对应模型的树节点对象
  const selectComponent: any[] = []; // 获取选中节点的segmentObject
  // 递归遍历树节点对象，查询componentId相同节点的segmentObject
  deepForEachFn(
    treeNodeObject,
    (node) => {
      if (node.componentId === componentId) {
        selectComponent.push(node.segmentObject);
      }
    },
    'childNodes',
  );
  // 定位并高亮构件
  handleLocateModel(selectComponent, viewer);
};
