import { ReactNode, useEffect, useState } from 'react';
import styles from './report-look.page.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { history, useParams } from 'umi';
import { reportDetail } from '@/services/intelligentReport/report-plan';

const ReportLookPage = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>();
  const { id } = useParams<{ id: string }>();
  useEffect(() => {
    getDetail(id);
  }, []);
  //获取详情
  function getDetail(id: string): void {
    setLoading(true);
    reportDetail(id).then((res) => {
      if (res.code === '1' && res.data) {
        setDetail(res.data[0]);
      }
      setLoading(false);
    });
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button loading={loading} onClick={toEdit}>
          编辑
        </type-button>
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //到编辑页面
  function toEdit(): void {
    history.push('/reportYearPlanAdd/' + id);
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  //详情模板
  function detailComponent(): ReactNode {
    if (detail) {
      const fields = Object.keys(detail).filter(
        (item) => !['id', '计划周期', 'submission_id'].includes(item),
      );
      return (
        <div className={styles['base-info']}>
          {fields.map((item: string, index) => (
            <div key={index} className={styles['base-info-item']}>
              <span>{item}</span>
              <span>{detail[item]}</span>
            </div>
          ))}
        </div>
      );
    } else {
      return <></>;
    }
  }
  return (
    <div className={styles['report-look-page']}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          <span>查看报表计划</span>
        </div>
        {controllerComponent()}
      </section>
      <section className={styles.form}>{detailComponent()}</section>
    </div>
  );
};

export default ReportLookPage;
