import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';
import { rsaEncrypt } from './jsencrypt.util';

export function socketConnect(url: string, token?: string): Promise<Client> {
  return new Promise((reject, reslove) => {
    const socket = new SockJS(url); // 创建一个 SockJS 连接
    let stompClient = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        //请求头
        // Authorization: 'Bearer ' + rsaToken,
      },
      debug: function (str) {
        // console.log('debug', str); // 用于输出每个操作的调试信息
      },
      reconnectDelay: 5000, // 断开连接后自动重连的延迟时间（毫秒）
      heartbeatIncoming: 4000, // 设置接收心跳的间隔时间
      heartbeatOutgoing: 4000, // 设置发送心跳的间隔时间
      connectionTimeout: 10000, // 10秒后连接超时
      onConnect: function (frame) {
        //连接成功
        reject(stompClient);
      },
      onStompError: function (err) {
        console.error('Error headers:', err);
        //连接失败
        reslove(err);
      },
    });
    // 启动 Client连接
    stompClient.activate();
  });
}
