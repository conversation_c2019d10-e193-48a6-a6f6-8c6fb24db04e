.report-examine-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-container {
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid #28487b;
}
.controller-container {
  display: flex;
}
.back {
  cursor: pointer;
  margin-left: 20px;
  color: #0392d8;
  display: flex;
  align-items: center;
  span {
    color: #0392d8;
  }
}
.title {
  display: flex;
  align-items: center;
  font-size: 18px;
}
.excel {
  flex: 1;
}
.hidden {
  display: none;
}
.process-container {
  flex: 1;
}
:global {
  .report-examine-page {
    .ant-spin-container {
      height: 100%;
    }
    .ant-tabs-nav {
      padding: 10px 30px 0px;
    }
    .ant-tabs-tab-btn {
      font-size: 16px;
    }
  }
}
