import { memo, useEffect, useState } from 'react';
import type { TableProps } from 'antd';
import { ConfigProvider, Table } from 'antd';
import type { FC, ReactNode } from 'react';

import { ComprehensiveConsumptionTableWrapper } from './comprehensive-consumption-table.style';

type RecordType = {
  key: string;
  capacity: string;
  'capacity-radio': string;
};

interface IProps {
  children?: ReactNode;
  calcResult: {
    [key: string]: string;
  };
}

const ComprehensiveConsumptionTable: FC<IProps> = (props) => {
  const { calcResult } = props;

  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  useEffect(() => {
    setDataSource([
      {
        key: '1',
        capacity: calcResult['综合厂用电量'] ?? '-',
        'capacity-radio': calcResult['综合厂用电率'] ?? '-',
      },
    ]);
  }, [calcResult]);

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'capacity',
      align: 'center',
      title: '综合厂用电量',
      dataIndex: 'capacity',
      width: '50%',
    },
    {
      key: 'capacity-radio',
      align: 'center',
      title: '综合厂用电率（%）',
      dataIndex: 'capacity-radio',
      width: '50%',
    },
  ];

  const tableHeaderRenderer = () => (
    <div style={{ position: 'relative' }}>
      <span>四、综合厂用电量</span>
      <span
        style={{
          position: 'absolute',
          left: '0',
          right: '0',
          margin: 'auto',
          textAlign: 'center',
        }}
      >
        单位：万千瓦时
      </span>
    </div>
  );

  return (
    <ComprehensiveConsumptionTableWrapper>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2f4f81',
            },
          },
        }}
      >
        <Table
          bordered
          size="small"
          title={tableHeaderRenderer}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </ConfigProvider>
    </ComprehensiveConsumptionTableWrapper>
  );
};

export default memo(ComprehensiveConsumptionTable);
