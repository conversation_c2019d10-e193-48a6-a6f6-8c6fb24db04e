import type { EChartsOption } from 'echarts';
import dayjs from 'dayjs';
import { ITemplate, HistoryData } from './point-analysis.provider';
interface LineData {
  name: string;
  data: number[];
}

interface GenerateOptionsParams {
  xAxisData: string[]; // X轴数据
  lineDataList: LineData[]; // 每条线的数据
}

// 预设颜色
const PRESET_COLORS = ['#0AE4FF', '#D4FF83', '#EF8E00', '#EF8E00'];

// 生成随机颜色
const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color;
  let isUnique = false;

  while (!isUnique) {
    color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    // 确保生成的颜色不在预设颜色中
    isUnique = !PRESET_COLORS.includes(color);
  }
  return color;
};

// 获取线条颜色
const getLineColor = (index: number) => {
  if (index < PRESET_COLORS.length) {
    return PRESET_COLORS[index];
  }
  return generateRandomColor();
};

// 生成折线图配置
export const generateLineChartOptions = ({
  xAxisData, // X轴数据
  lineDataList, // 每条线的数据
}: GenerateOptionsParams): EChartsOption => {
  return {
    tooltip: {
      trigger: 'axis', // 触发类型
      axisPointer: {
        type: 'line', // 指示器类型
      },
    },
    legend: {
      data: lineDataList.map((item) => item.name),
      textStyle: {
        color: '#FFFFFF', // 设置图例文字颜色为白色
      },
      right: '5%', // 距离右侧的距离
      itemGap: 8, // 图例项之间的间距
    },
    grid: {
      left: '1%',
      right: '5%',
      bottom: '0',
      containLabel: true, // 是否包含标签
    },
    xAxis: {
      type: 'category', // 类型
      boundaryGap: false, // 是否边界补全
      data: xAxisData, // X轴数据
      axisLine: {
        lineStyle: {
          color: '#6CA1DD', // 设置X轴线条颜色
        },
      },
      axisLabel: {
        color: '#D4EBFD', // 设置X轴文字颜色
      },
    },
    yAxis: {
      type: 'value', // 类型
      splitLine: {
        lineStyle: {
          type: 'dashed', // 设置Y轴分割线为虚线
          color: '#6CA1DD', // 设置Y轴分割线颜色
        },
      },
      axisLabel: {
        color: '#D4EBFD', // 设置Y轴文字颜色
      },
    },
    series: lineDataList.map((line, index) => ({
      name: line.name, // 线条名称
      type: 'line', // 线条类型
      data: line.data, // 线条数据
      smooth: true, // 是否平滑
      itemStyle: {
        opacity: 0, // 折线点透明度
      },
      lineStyle: {
        color: getLineColor(index), // 线条颜色
      },
    })),
  };
};

// 获取当前时间的前30分钟
export const getHalfHourTime = (): [dayjs.Dayjs, dayjs.Dayjs] => {
  const now = dayjs();
  const halfHour = now.subtract(30, 'minutes');
  return [halfHour, now];
};
