import {
  FC,
  MouseEvent,
  ReactNode,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import styles from './center-bim.component.less';
import { getNodeTree } from '@/services/equip-status-bim';
import { deepLeafFn, findNodesByCodes } from '@/utils/utils';
import { getModelInfo } from '@/services/monitoring-point-annotation';
import { defaultOptions } from '@/pages/equip-status-bim/components/center-equip-info/center-equip-info';
import {
  PropOptions,
  defComponentIds,
  defModelCodes,
  homeCameraView,
  valStyle,
} from './center-bim';
import { Statistic } from 'antd';
import { UnitOptions } from '../unit-tab/unit-tab';

let bimView: any = null; // 模型视图
export const CenterBim: FC<PropOptions> = memo(
  forwardRef(({ openModal, socketClient, pointData }, ref) => {
    const [lightModel, setLightModel] = useState<any[]>(); //构件对象
    const [view, setView] = useState<any>(); //模型视图实例
    const [BimAir, setBimAir] = useState<any>(); //模型视图
    //bim默认视图加载是否完成
    const [defBim, setDefBim] = useState<boolean>(false);
    //线条控制
    const [lineController, setLineController] = useState<boolean>(true);
    const bimRef = useRef<any>();
    useEffect(() => {
      if (bimRef.current) {
        bimRef.current.options = defaultOptions;
        bimRef.current.createdViewerRef = handleOnCreateTopBimRef;
      }
      return () => {
        bimRef.current = {};
        bimView = null;
      };
    }, []);
    useEffect(() => {
      if (pointData && BimAir == undefined) {
        getModelInfos();
      }
    }, [pointData]);
    useImperativeHandle(
      ref,
      () => ({
        setBimDefaultView,
        setLineController,
      }),
      [view],
    );
    useEffect(() => {
      if (lightModel && view) {
        setTimeout(() => {
          setLightModelsLation(lightModel);
        }, 1000);
      }
    }, [view, lightModel]);
    //获取模型树
    function getModelInfos(): void {
      getNodeTree({}).then((res) => {
        if (res.code === 200) {
          const bimList = findNodesByCodes({
            tree: res.data.data,
            codes: defModelCodes,
            key: 'code',
          });
          const ids = bimList.map((item) => item.id);
          getModelData(ids);
        }
      });
    }
    //获取指定模型数据
    function getModelData(ids: string[]): void {
      const fetchs = ids.map((item) => getModelInfo(item));
      Promise.allSettled(fetchs).then((res) => {
        let vals: string[] = [];
        res.forEach((item) => {
          if (item.status === 'fulfilled' && item.value.data.code === 200) {
            const bimModelIds = item.value.data.data.map((item: any) => item.bimModelId);
            vals = vals.concat(bimModelIds);
          }
        });
        vals.length > 0 && modelViewCreate(vals);
      });
    }
    //模型视图创建
    function modelViewCreate(ids: string[]): void {
      if (bimRef.current) {
        bimRef.current.modelIds = ids; //挂载模型到div上
        bimRef.current.modelOnLoaded = (trie: any, lightModels: any[]) => {
          handleModelOnLoaded(trie, lightModels, pointData);
        };
        //鼠标滚轮事件
        bimRef.current.partOnMouseWheel = bomMouseWheel;
        //鼠标右键拖动事件
        bimRef.current.partOnRightMouseMove = bomMouseWheel;
      }
    }
    // 加载完模型获取视图
    function handleOnCreateTopBimRef(viewer: any, BimAir: any): void {
      setView(viewer);
      setBimAir(BimAir);
      bimView = viewer;
      bimView.onLButtonUp.add(modelPartOnClick);
    }
    //设置构件隔离
    function setLightModelsLation(lightModels: any[]): void {
      lightModels.forEach((item) => {
        deepLeafFn(
          [item.treeNodeObject],
          (item) => {
            if (defComponentIds.includes(item.componentId)) {
              bimView.isolationManager.isolation([item]);
            }
          },
          'childNodes',
        );
      });
      bimView.updateDisplay();
    }
    //模型构件点击事件
    function modelPartOnClick(sender: any, event: any): void {
      let segmentObject = event.viewer.selectionManager.segmentObject;
    }
    // 获取模型对象
    function handleModelOnLoaded(trie: any, lightModels: any[], pointDatas?: UnitOptions): void {
      setLightModel(lightModels);
      setTimeout(() => {
        //设置延迟，等构件渲染执行完成后执行
        setBimDefaultView(pointDatas);
        setDefBim(true);
      }, 2000);
    }
    //bim鼠标滚动事件
    function bomMouseWheel(segment: any, event: any) {
      //隐藏之前的线条
      setLineController(false);
    }
    //设置bim默认视角
    function setBimDefaultView(pointDatas?: UnitOptions): void {
      const viewData = homeCameraView.find((item) => item.key === pointDatas?.value);
      if (viewData && bimView) {
        bimView.camera.position = viewData.position;
        bimView.camera.target = viewData.target;
        bimView.camera.up = viewData.up;
        bimView.camera.setField(viewData.width, viewData.height);
        bimView.updateDisplay();
      }
    }
    //线条模板
    function lineComponent({
      x,
      y,
      w,
      r = '0',
    }: {
      x: string;
      y: string;
      w: number;
      r?: string;
    }): ReactNode {
      return (
        <span
          style={{
            bottom: y,
            left: x,
            width: w + 'px',
            transform: `rotate(${r}deg)`,
          }}
          className={styles['point-line']}
        ></span>
      );
    }
    //油位测点模板
    function oilComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '8%',
              left: '64%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.oilList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="oilList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '-3%', w: 181 })}
                {lineComponent({ x: '-74px', y: '-50%', w: 101, r: '-62' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //高温测点模板
    function highPressureComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '9%',
              left: '77%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.highPressureList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="highPressureList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '-3%', w: 232 })}
                {lineComponent({ x: '-212px', y: '-30%', w: 220, r: '-20' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //低压测点模板
    function lowPressureComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '6%',
              left: '43%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.lowPressureList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="lowPressureList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '-3%', w: 232 })}
                {lineComponent({ x: '229px', y: '-13%', w: 85, r: '19' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //U电压测点模板
    function UComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '78%',
              left: '20.5%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.UList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="UList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '103%', w: 220 })}
                {lineComponent({ x: '196px', y: '122%', w: 58, r: '-81' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //I电流测点模板
    function IComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '78%',
              left: '37%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.IList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="IList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '103%', w: 215 })}
                {lineComponent({ x: '-117px', y: '161%', w: 175, r: '70' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //夹件电流测点模板
    function clampComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '77%',
              left: '57%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.clampList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="clampList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '103%', w: 209 })}
                {lineComponent({ x: '-27px', y: '149%', w: 50, r: '86' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //铁芯测点模板
    function ironCoreComponent(): ReactNode {
      if (pointData) {
        return (
          <div
            style={{
              top: '53%',
              left: '79%',
            }}
            className={styles['point-container']}
          >
            {pointData.pointList.ironCoreList.map((item) => (
              <div key={item.value} className={styles['point-container-item']}>
                {/* 点击事件遮罩元素 */}
                <div
                  data-key={item.value}
                  data-field="ironCoreList"
                  className={styles['click-mask']}
                ></div>
                <span>{item.label}</span>
                <Statistic
                  valueStyle={valStyle}
                  precision={1}
                  suffix={item.unit}
                  value={item.price || '—'}
                />
              </div>
            ))}
            {lineController ? (
              <>
                {lineComponent({ x: '0', y: '103%', w: 217 })}
                {lineComponent({ x: '-69px', y: '98%', w: 70, r: '-12' })}
              </>
            ) : null}
          </div>
        );
      } else {
        return <></>;
      }
    }
    //测点点击处理
    function pointClickHandler(e: MouseEvent): void {
      const target: any = e.target;
      const key = target.getAttribute('data-key');
      const field = target.getAttribute('data-field');
      if (key && field && pointData) {
        const data = pointData.pointList[field].find((item) => {
          if (item.value === key) {
            return true;
          } else {
            return false;
          }
        });
        if (data) {
          openModal(
            [
              {
                id: data.value,
                name: data.label,
              },
            ],
            data.label,
            data.unit,
          );
        }
      }
    }
    //全部测点模板
    function allPointComponent(): ReactNode {
      return view && defBim ? (
        <section className={styles.mask} onClick={pointClickHandler}>
          {lowPressureComponent()}
          {oilComponent()}
          {highPressureComponent()}
          {UComponent()}
          {IComponent()}
          {clampComponent()}
          {ironCoreComponent()}
        </section>
      ) : (
        <></>
      );
    }
    function roam() {
      const commandshowFirstPersonRoam = new BimAir.Command.CommandShowFirstPersonRoam();
      view.process.commandManager.execute(commandshowFirstPersonRoam);
      commandshowFirstPersonRoam.speed = 50;
      commandshowFirstPersonRoam.onEnd.add(() => {});
    }
    return (
      <section className={styles['center-bim']}>
        {/* <button style={{
          position: 'fixed',
          zIndex: 999,
          top: '100px',
          left: '300px'
        }} onClick={roam}>开启漫游</button> */}
        {/* <button style={{
          position: 'fixed',
          zIndex: 999,
          top: '100px',
          left: '370px'
        }} onClick={() => { console.log("camera", bimView.camera) }}>获取位置</button>
        <button style={{
          position: 'fixed',
          zIndex: 999,
          top: '100px',
          left: '440px'
        }} onClick={() => {  bimView.updateDisplay() }}>更新</button> */}
        {allPointComponent()}
        <bim-air-plugin
          style={{
            width: '100%',
            height: '100%',
          }}
          ref={bimRef}
          id="main-bim-wrapper"
        />
      </section>
    );
  }),
);
