import { TreeOptions } from '../monitoring-point/monitoring-point';

export interface PropOptions {
  nodeData: TreeOptions; //父节点数据
  pointData: TreeOptions; //测点节点数据
  taggChange: () => void; //添加标注
  delChange: () => void; //删除标注
}
//标注点配置
export interface TaggOptions {
  x: number;
  y: number;
  id?: string; //点位id
  name: string; //点位名称
}
//标注点请求参数
export interface TaggParams {
  xcoordinate: number; //x坐标
  ycoordinate: number; //y坐标
  id: string; //节点id
  parentId?: string;
}
