import { useEffect, useState } from 'react';

/**
 * @param container antd Table容器
 * @param deps dependencies依赖数组
 * @returns antd Table可滚动区域高度
 * @restrictions
 * 1. container选择器存在, 且包含对应antd Table
 * 2. antd Table包裹层（最外层）(.ant-table-wrapper), 须给定高度
 */
export default function useTableScrollerHeight(
  container: string | React.RefObject<HTMLDivElement>,
  deps: any[] = [],
) {
  const [tabelScrollerHeight, setTabelScrollerHeight] = useState<number>(0);

  const effectFn = () => {
    let containerEl: HTMLDivElement | null = null;
    if (typeof container === 'string') {
      containerEl = document.querySelector(container);
      if (!containerEl) throw new Error(`找不到包含antd Table的选择器为"${container}"容器`);
    } else {
      // 传入的React Ref对象未指向包含antd Table的DOM节点(<HTMLDivElement>)
      if (!container.current) return;

      containerEl = container.current;
    }

    // pagination
    let paginationHeightWithMargin = null;
    const paginationEl = containerEl.querySelector('ul.ant-pagination');
    if (paginationEl) {
      const paginationMarginTop = parseFloat(
        window.getComputedStyle(paginationEl).getPropertyValue('margin-top'),
      );
      const paginationMarginBot = parseFloat(
        window.getComputedStyle(paginationEl).getPropertyValue('margin-bottom'),
      );
      paginationHeightWithMargin =
        paginationEl.clientHeight + paginationMarginTop + paginationMarginBot;
    }
    // table-wrapper
    const tableWrapperHeight = containerEl.querySelector('.ant-table-wrapper')!.clientHeight;
    // table-header
    const tableHeaderHeight = containerEl.querySelector('.ant-table-header')!.clientHeight;
    // table-border-top
    const tableEl = containerEl.querySelector('.ant-table')!;
    const tableBorderTopWidth = parseFloat(
      window.getComputedStyle(tableEl).getPropertyValue('border-top-width'),
    );
    // table-container-border-top - under bordered table
    const tableContainerEl = containerEl.querySelector('.ant-table-container')!;
    const tableContainerBorderTopWidth = parseFloat(
      window.getComputedStyle(tableContainerEl).getPropertyValue('border-top-width'),
    );

    setTabelScrollerHeight(
      tableWrapperHeight -
        (paginationHeightWithMargin ?? 0) -
        tableHeaderHeight -
        tableBorderTopWidth -
        tableContainerBorderTopWidth,
    );
  };

  useEffect(effectFn, deps);

  useEffect(() => {
    window.addEventListener('resize', effectFn);

    return () => {
      window.removeEventListener('resize', effectFn);
    };
  }, []);

  return tabelScrollerHeight;
}
