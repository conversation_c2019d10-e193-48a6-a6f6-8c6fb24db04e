.container {
  background-color: rgba(10, 37, 80, 0.8);
  padding: 16px;
  border-radius: 4px;
  width: 450px;
  position: absolute;
  left: calc(100% - 450px - 160px - 10px);
  top: 12px;
  z-index: 99;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  color: #fff;
  margin: 0;
  font-size: 18px;
}

.icons {
  display: flex;
  gap: 10px;
}

.icon {
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #0ad0ee;
  }
}

.content {
  background-color: transparent;
}

.item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  &.maxAndMin {
    flex: 1;
    justify-content: start;
    border-bottom: none;
    padding: 0;
    .label {
      margin-right: 12px;
    }
  }
  &:last-child {
    border-bottom: none;
  }
}

.label {
  color: #0ad0ee;
}

.value {
  color: #fff;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.footer {
  :global {
    .ant-tabs-nav {
      margin-bottom: 8px;
    }

    .ant-tabs-tab {
      color: rgba(255, 255, 255, 0.65);

      &.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #0ad0ee;
      }
    }

    .ant-table {
      background: transparent;

      .ant-table-thead > tr > th {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.65);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .ant-table-tbody > tr > td {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      .ant-table-tbody > tr:hover > td {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
}
