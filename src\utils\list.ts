export type Option<T> = {
  label: string;
  value: any;
  [key: string]: any;
} & T;
export type Options<T = any> = Option<T>[];

export interface GenerateListOptions<
  T extends object = object,
  Label<PERSON>ey extends keyof T = keyof T,
  Value<PERSON>ey extends keyof T = keyof T,
  <PERSON><PERSON><PERSON> extends keyof T = keyof T,
> extends Record<string, any> {
  labelKey?: LabelKey;
  valueKey?: ValueKey;
  childrenKey?: ChildrenKey;
}

export function generateList<T extends object = object>(list?: T[], options?: GenerateListOptions<T>): Options<T> {
  if (!list) return [];

  const labelKey = options?.labelKey || ('name' as keyof T);
  const valueKey = options?.valueKey || ('id' as keyof T);
  const childrenKey = options?.childrenKey || ('children' as keyof T);

  return list.map((item) => {
    const label = item[labelKey];
    let value = item[valueKey] as any;
    if (options?.digitalize) {
      value = +value;
    }

    const children = item[childrenKey] as unknown as T[];
    return {
      ...item,
      label,
      value,
      children: children?.length ? generateList(children, options) : undefined,
    };
  }) as Options<T>;
}
