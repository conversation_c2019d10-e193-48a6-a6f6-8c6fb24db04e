.modal-container {
  display: flex;
  height: 100%;
}

.chart-container-box {
  flex: 1;
}

.point-list {
  flex: 0 0 200px;
  overflow-y: auto;
  border-left: 1px solid rgba(30, 137, 244, 1);
  box-sizing: content-box;
  padding: 5px 10px;
  color: #fff;
}

.chart-container {
  width: 100%;
  margin-top: 5px;
  height: calc(100% - 50px);
}

.point-list-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: normal;
}

.point-list-item {
  cursor: pointer;
  margin-top: 5px;
  padding: 7px 17px 2px 7px;
  position: relative;
  border-radius: 2px;
  font-size: 14px;

  &:hover {
    background: rgba(43, 105, 178, 1);
  }

  span:first-child {
    width: 175px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-del {
    position: absolute;
    right: 5px;
    top: 13px;
    font-size: 12px;
    color: #fff;
    transition: transform 0.3s;

    &:hover {
      transform: rotate(90deg);
    }
  }
}
