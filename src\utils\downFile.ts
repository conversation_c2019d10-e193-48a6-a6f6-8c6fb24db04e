/**
 * 下载二进制文件方法
 * @export
 * @param {Blob} bolb 数据，文件二进制，缺省值：''
 * @param {string} fileName 数据，文件名称，缺省值：''
 * <AUTHOR> 2023/09/26
 * @class downFile
 * @extends {Function}
 */
export function downFile(bolb: Blob, fileName: string) {
  const downloadURL = window.URL.createObjectURL(bolb);
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = downloadURL;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(downloadURL);
}
