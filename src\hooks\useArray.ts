import { useState } from 'react';
/**
 * @description 数组操作
 * <AUTHOR>
 */
export const useArray = <T>(initialArray: T[]) => {
  const [value, setValue] = useState(initialArray);
  return {
    value,
    setValue,
    add: (item: T) => setValue([...value, item]), // 新值
    clear: () => setValue([]), // 清空数组
    // 删除
    removeIndex: (index: number) => {
      const copy = [...value];
      copy.splice(index, 1);
      setValue(copy);
    },
  };
};
