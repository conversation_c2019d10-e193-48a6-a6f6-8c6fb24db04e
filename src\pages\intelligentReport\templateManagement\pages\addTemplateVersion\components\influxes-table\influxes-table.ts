import {
  InfluxesOptions,
  DataTypeEnum,
  CellControllerOptions,
  InfluxesEnums,
} from '@/components/spread-table/spread-table';
import { UseImperativeOptions as TableUseImperativeOptions } from '@/components/spread-table/spread-table';

export interface PropOptions {
  allTableList: InfluxesOptions[]; //全部列表
  tableList: InfluxesOptions[]; //当前tab列表
  collapseChange: React.Dispatch<React.SetStateAction<string[]>>; //折叠面板设置
  infoTabChange: React.Dispatch<React.SetStateAction<string>>; //信息tab切换
  otherTableList: CellControllerOptions[]; //包含其他列表得数据,用于校验重复
  tableListChange: React.Dispatch<React.SetStateAction<InfluxesOptions[]>>;
  isEdit: boolean;
  spreadRef: React.MutableRefObject<TableUseImperativeOptions>;
  ref: any;
  inputFocusFn: (val: CellControllerOptions, type: DataTypeEnum.INFLUXES) => void; //输入框聚焦
}
export interface UseImperativeOptions {
  cellImgHandler: (data: InfluxesOptions, status: 0 | 1) => void;
  cellImgBatchHandler: (list: InfluxesOptions[], status: 0 | 1) => void;
  addPointRange: () => void;
  tablePointEvent: () => void;
}
export interface SelectOptions {
  label: string;
  key: string;
}
export enum DateType {
  YEAR = 'year',
  QUARTER = 'quarter',
  MONTH = 'month',
}
export const typeList = [
  { label: '最大值', key: InfluxesEnums.MAX },
  { label: '最小值', key: InfluxesEnums.MIN },
  { label: '平均值', key: InfluxesEnums.AVERAGE },
  { label: '累积值', key: InfluxesEnums.SUM },
  { label: '瞬时值', key: InfluxesEnums.INSTANTANEOUS },
  { label: '遥信开关次数', key: InfluxesEnums.SWITCH },
];

export const dateList = [
  { label: '月数据', key: DateType.MONTH },
  { label: '季度数据', key: DateType.QUARTER },
  { label: '年数据', key: DateType.YEAR },
];
