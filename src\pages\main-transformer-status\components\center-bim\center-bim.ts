import { UnitListData, UnitOptions } from '../unit-tab/unit-tab';
import { Client } from '@stomp/stompjs';
import { PointOptions } from '../../../equip-status-bim/equip-status-bim';

export interface PropOptions {
  pointData?: UnitOptions;
  ref: any;
  socketClient?: Client;
  openModal: (pointData: PointOptions[], title: string, unit: string) => void;
}
//默认要加载的模型code'UME',
export const defModelCodes = [
  '3cf',
  '2cf',
  '1cf',
  '20BA',
  '10BA',
  '30BA',
  '10BAT',
  '3#BAT',
  '20BAT',
];
//默认要隔离的构件id
export const defComponentIds = [
  '154',
  '162',
  '152',
  '148',
  '147',
  '32',
  '81',
  '43',
  '80',
  '33',
  '79',
  '31',
  '120',
  '1513',
  '1645',
  '1507',
  '1612',
  '70',
  '4',
  '139',
  '745',
  '1556',
  '1584',
  '1603',
  '1514',
  '1607',
  '1563',
  '1570',
  '1573',
  '1582',
  '1610',
  '1644',
  '1580',
  '1579',
  '1512',
  '1613',
  '1643',
  '1577',
  '1576',
  '1511',
  '141',
  '18',
  '65',
  '129',
  '130',
  '145',
  '71',
  '44',
  '26',
  '25',
  '144',
  '74',
  '45',
  '23',
  '22',
  '68',
  '143',
  '46',
  '5',
  '9',
  '6',
  '20',
  '142',
  '47',
  '17',
  '64',
  '131',
  '681',
  '680',
  '679',
  '678',
  '1590',
  '1589',
  '1588',
  '1587',
  '149',
  '150',
  '703',
  '670',
  '673',
  '1515',
  '606',
  '709',
  '671',
  '698',
  '607',
  '751',
  '701',
  '608',
  '654',
  '664',
  '661',
  '752',
  '704',
  '609',
  '676',
  '675',
  '753',
  '708',
  '1555',
  '749',
  '695',
  '668',
  '694',
  '667',
  '750',
  '1585',
  '1604',
  '1646',
];
//机组bim默认视角
export const homeCameraView = [
  {
    key: UnitListData[0].value,
    position: [48.14563321147508, 409.92739147680396, 624.6465666478297],
    target: [592.116601231447, -339.4006263500043, 388.4543264733004],
    up: [0.14520157495178962, -0.20001730743093726, 0.9689734667985486],
    width: 955.5241339099654,
    height: 955.5241339099654,
  },
  {
    key: UnitListData[1].value,
    position: [71.61379193263846, 426.96398521611286, 624.6465666478297],
    target: [615.5847599526104, -322.3640326106954, 388.4543264733004],
    up: [0.14520157495178962, -0.20001730743093726, 0.9689734667985486],
    width: 955.5241339099654,
    height: 955.5241339099654,
  },
  {
    key: UnitListData[2].value,
    position: [95.08195065380185, 444.00057895542176, 624.6465666478297],
    target: [639.0529186737738, -305.3274388713865, 388.4543264733004],
    up: [0.14520157495178962, -0.20001730743093726, 0.9689734667985486],
    width: 955.5241339099654,
    height: 955.5241339099654,
  },
];
//数字样式
export const valStyle = {
  color: '#fff',
  fontSize: '14px',
  width: '50px',
  display: 'flex',
  justifyContent: 'space-between',
};
