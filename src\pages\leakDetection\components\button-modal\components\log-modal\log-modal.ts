export interface PropOptions {
  open: boolean;
  close: () => void;
}
export const inputW = { width: '250px' };
export interface PageParams {
  pageNum: number;
  pageSize: number;
  startDate?: string;
  name: string;
  endDate?: string;
}
export interface TableOptions {
  name: string;
  type: string;
  remark: string;
  id: string;
  code: string;
  createTime: string;
  createUserId: string;
}
export const params: PageParams = {
  pageNum: 1,
  pageSize: 10,
  name: '',
  startDate: undefined,
  endDate: undefined,
};
