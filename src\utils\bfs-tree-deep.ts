import { cloneDeep } from 'lodash';

/**
 * 广度优先查找指定深度数结构节点值集合
 * @export
 * @param {any[]} source 树结构数据
 * @param {string} key 取出属性key值，默认为id
 * @param {string} childKey 子节点属性key值，默认为children
 * @param {number} deep 遍历深度，默认为3
 * @returns
 */
export function bfsTreeDeep(
  source: any[],
  key: string = 'id',
  childKey: string = 'children',
  deep: number = 3,
) {
  if (!(source instanceof Array) || typeof deep !== 'number') {
    throw Error('憨批传参');
  }
  const result: any = [];
  const stack = cloneDeep(source);
  let count = 0;

  while (stack.length > 0 && count < deep) {
    [...stack].forEach((element) => {
      stack.shift();
      result.push(element[key]);
      if (element[childKey] instanceof Array && element[childKey].length) {
        stack.push(...element[childKey]);
      }
    });
    count += 1;
  }

  return result;
}

/* 更正isLeaf属性 */
export function correctIsLeaf(nodes: any[]) {
  for (const node of nodes) {
    node.isLeaf = node.children.length === 0;
    correctIsLeaf(node.children);
  }
}
