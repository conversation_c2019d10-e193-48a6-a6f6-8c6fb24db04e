import React, { memo, useEffect, useState } from 'react';
import type { ReactNode, FC } from 'react';

import { SummaryBarWrapper } from './summary-bar.style';
import SummaryItem from './c-cpns/summary-item/summary-item.component';
import electricIcon from '@/assets/images/icon_electric.png';
import flowIcon from '@/assets/images/icon_flow.png';
import timeIcon from '@/assets/images/icon_time.png';
import waterlevelIcon from '@/assets/images/icon_waterlevel.png';
import { getWaterDayStatistic } from '../../epuip-status-overview.service';

interface IProps {
  children?: ReactNode;
}

const SummaryBar: FC<IProps> = () => {
  const [waterDayStatistic, setWaterDayStatistic] = useState<any>(null);

  const initData = () => {
    getWaterDayStatistic().then((res) => {
      setWaterDayStatistic(res.data);
    });
  };

  useEffect(() => {
    initData();
  }, []);

  return (
    <SummaryBarWrapper>
      <SummaryItem
        key="连续安全生产"
        title="连续安全生产"
        num={'2368'.split('').map((digit) => (
          <span
            style={{
              display: 'inline-block',
              width: '15%',
              fontSize: '30px',
              fontWeight: 'bold',
              marginRight: '2%',
              textAlign: 'center',
              background:
                'linear-gradient(90deg, rgba(21,161,216,0.1) 0%, rgba(21,161,216,0.05) 100%)',
            }}
          >
            {digit}
          </span>
        ))}
        unit="天"
        icon={timeIcon}
      />
      <SummaryItem
        key="发电总量"
        title="发电总量"
        num={(2797.97).toFixed(2)}
        unit="亿KW.h"
        icon={electricIcon}
      />
      <SummaryItem
        key="入库流量"
        title="入库流量"
        num={Number(waterDayStatistic?.inFlowNum ?? 0).toFixed(2)}
        unit="m³/s"
        icon={flowIcon}
      />
      <SummaryItem
        key="出库流量"
        title="出库流量"
        num={Number(waterDayStatistic?.outFlowNum ?? 0).toFixed(2)}
        unit="m³/s"
        icon={flowIcon}
      />
      <SummaryItem
        key="上游水位"
        title="上游水位"
        num={Number(waterDayStatistic?.upperLevelNum ?? 0).toFixed(2)}
        unit="m"
        icon={waterlevelIcon}
      />
      <SummaryItem
        key="下游水位"
        title="下游水位"
        num={Number(waterDayStatistic?.lowerLevelNum ?? 0).toFixed(2)}
        unit="m"
        icon={waterlevelIcon}
      />
    </SummaryBarWrapper>
  );
};

export default memo(SummaryBar);
