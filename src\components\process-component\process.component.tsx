import { FC, useEffect, useRef } from 'react';
import ProcessListComponent from './process-list.component';
import styles from './process.component.less';

interface Props {
  details?: any;
}
export interface IToolTip {
  userTaskId: string; // 用户任务元素id
  approver: string; // 处理人
  comment: string; // 处理意见
}

const ProcessComponent: FC<Props> = ({ details }) => {
  const processRef = useRef<any>(null);
  const workflowApprovalRecordList = details?.workflowApprovalRecordList || [];
  useEffect(() => {
    if (details && details.workflowApprovalRecordList && processRef.current) {
      processRef.current.toolTips = details.workflowApprovalRecordList.map((item: any) => ({
        userTaskId: item.taskDefKey,
        approver: item.approverName,
        comment: item.opinion,
      }));
    }
  }, [details]);
  return (
    <div className={styles.box}>
      <div className={styles.process}>
        <div className={styles.title}>处理流程：</div>
        <div className={styles.content}>
          <ProcessListComponent data={workflowApprovalRecordList} />
          {details?.taskUserName ? (
            <div className={styles.taskUserName}>
              <span>当前处理人：</span> {details?.taskUserName}
            </div>
          ) : (
            ''
          )}
        </div>
      </div>
      <div className={styles.img}>
        <div className={styles.title}>流程图：</div>
        <div className={styles.content}>
          <process-viewer
            ref={processRef}
            processInstanceId={details?.processInstanceId}
            modelId={details?.modelId}
          />
        </div>
      </div>
    </div>
  );
};

export default ProcessComponent;
