import { FC, ReactNode, memo, useEffect, useState } from 'react';
import styles from './right-equip-info.component.less';
import style from '../../equip-status-bim.page.less';
import testImg from '@/assets/images/tset3.png';
import { RightEquipModal } from '../right-equip-modal/right-equip-modal.component';
import { PropOptions } from './right-equip-info';
import { urlPrefix } from '@/utils/request';
import { Empty, Spin, Tooltip } from 'antd';
import { TreeOptions } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { TaggOptions } from '@/pages/monitoring-point-annotation/components/bim-tagging/bim-tagging';
import { baseUnitTree } from '@/services/monitoring-point-annotation';
import { PosImg } from '@/pages/monitoring-point-annotation/components/bim-tagging/bim-tagging.component';
import { ModalComponent } from '../modal/modal.component';

export const RightEquipInfo: FC<PropOptions> = memo(({ loading, imgData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  //设备图片列表
  const [imgList, setImgList] = useState<TreeOptions[]>([]);
  //标注点列表
  const [taggList, setTaggList] = useState<TreeOptions[]>([]);
  useEffect(() => {
    if (imgData) {
      getTaggList();
    }
  }, [imgData]);
  //获取标注点
  function getTaggList() {
    //过滤出undefined数据
    const list = imgData.filter((item) => item && item.id);
    setImgList(list);
    if (list.length > 0) {
      //请求数组
      const fetchList = list.map((item) => baseUnitTree<TreeOptions[]>({ parentId: item.id }));
      Promise.allSettled(fetchList).then((res) => {
        //过滤出请求成功的数据
        let fulList: any[] = res.filter((item) => item.status === 'fulfilled');
        const taggLists: TreeOptions[] = [];
        fulList.forEach((item) => {
          if (item.value.data) {
            taggLists.push(...item.value.data);
          }
        });
        setTaggList(taggLists);
      });
    }
  }
  //空数据占位模板
  function emptyComponent(): ReactNode {
    return (
      <div>
        <Empty description={<span>暂无设备图片</span>} image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  //图片列表模板展示
  function imgListComponent(): ReactNode {
    return imgList.map((item) => (
      <div className={styles['img-list-item']} key={item.id}>
        <img onClick={() => setModalOpen(true)} src={urlPrefix + '/Attachment/downloadAttachment/' + item.imgId} />
        {taggComponent(item.id)}
      </div>
    ));
  }
  //内容展示模板
  function containerShowComponent(): ReactNode {
    if (loading) {
      return <></>;
    } else if (imgList.length > 0) {
      return <>{imgListComponent()}</>;
    } else {
      return emptyComponent();
    }
  }
  //标注点模板
  function taggComponent(parentId: string): ReactNode {
    const list = taggList.filter((item) => item.parentId === parentId);
    return list.map((item, index: number) => {
      return (
        <Tooltip key={index} placement="top" title={item.name}>
          <div
            style={{
              left: item.xcoordinate - 10,
              top: item.ycoordinate - 12,
            }}
            className={styles.tagg}
          >
            <PosImg />
          </div>
        </Tooltip>
      );
    });
  }
  return (
    <>
      {/* 右侧信息 */}
      <ModalComponent direction="right">
        <div className={styles['right-equip-info-container']}>
          <Spin spinning={loading} size="large">
            {containerShowComponent()}
          </Spin>
        </div>
      </ModalComponent>

      {/* 右侧弹框信息 */}
      <RightEquipModal close={() => setModalOpen(false)} open={modalOpen} />
    </>
  );
});
