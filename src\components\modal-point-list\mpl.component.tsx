import React, { useState, useImperativeHandle, forwardRef, memo } from 'react';
import styles from './mpl.component.less';
import { Button, Space, Table, Form, Input, message, Select } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { Pointlist } from '@/pages/point/type';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { useAntdTable } from 'ahooks';
import { getPointList } from '@/pages/point/point.service';
import _ from 'lodash';
import { useSelector } from 'umi';

interface IProps {
  callback: (param: Pointlist.List[], type: string) => void;
}
interface IRef {
  openModal: (type: 'create' | 'edit', checkRows?: Pointlist.List[]) => void;
}
// 表单配置项
const columns: ColumnsType<Pointlist.List> = [
  {
    key: '_',
    align: 'center',
    title: '序号',
    dataIndex: '_',
    render: (_, __, index) => index + 1,
    width: 100,
  },
  {
    title: '名称',
    dataIndex: 'pointName',
    align: 'center',
    ellipsis: true, // 超出显示省略号
    sorter: (a, b) => a.pointName.localeCompare(b.pointName),
  },
  {
    title: '编码',
    align: 'center',
    dataIndex: 'pointCode',
    width: 150,
    sorter: (a, b) => a.pointCode.localeCompare(b.pointCode),
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'pointType',
    width: 100,
  },
];

const LegendListModal = forwardRef<IRef, IProps>((props, ref) => {
  const { callback } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState<boolean>(false); // 新建图例弹窗显隐
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 勾选的行id
  const [selectedRows, setSelectedRows] = useState<Pointlist.List[]>([]); // 勾选的内容
  const [disabledRowKeys, setDisabledRowKeys] = useState<React.Key[]>([]); // 禁止取消勾选的行id
  const devicesList = useSelector((state: any) => state?.point?.devices || []); // 测点类型
  const [ready, setReady] = useState(false);

  // 暴露给父组件
  useImperativeHandle(ref, () => ({
    openModal,
  }));

  // 显示弹窗
  const openModal = async (type: 'create' | 'edit', checkRows: Pointlist.List[] = []) => {
    setVisible(true);
    setReady(true);
    if (_.isEqual(type, 'edit') && _.size(checkRows) > 0) {
      const initialKeys = checkRows.map((_) => _.pointCode);
      setSelectedRowKeys(initialKeys); // 回显勾选
      setDisabledRowKeys(initialKeys); // 设置禁止取消勾选的行
    }
  };
  // 获取列表数据
  const getTableData = (
    { current, pageSize }: { current: number; pageSize: number }, // 分页参数
    formDatas: Pick<Pointlist.Query, 'pointName' | 'pointCode'>, // 查询条件参数
  ) => {
    if (!ready) {
      return Promise.resolve({ total: 0, list: [] });
    }
    return getPointList({
      ...formDatas,
      pageNum: current,
      pageSize,
      orderColumn: 'point_name',
      orderCondition: 'desc',
    }).then((result) => {
      return {
        total: Number(result?.data.total) ?? 0, // 总条数
        list: result?.data.list ?? [], // 数据列表
      };
    });
  };
  const {
    tableProps: { dataSource, loading, onChange, pagination },
    search, // 事件
  } = useAntdTable(getTableData, {
    form,
    defaultPageSize: 10, // 初始化数量
    ready, // 是否准备好
  });

  // 多选
  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: Pointlist.List[]) => {
    setSelectedRowKeys(newSelectedRowKeys); // 勾选的行
    setSelectedRows(selectedRows); // 勾选的行的内容
  };

  // 关闭弹窗
  const handleClose = () => {
    setVisible(false);
    setReady(false);
    setSelectedRowKeys([]); // 清空勾选行的id
    setSelectedRows([]); // 清空勾选的行
    setDisabledRowKeys([]); // 清空禁止取消勾选的行
  };

  // 确认选择
  const handleOk = async () => {
    if (_.size(selectedRowKeys) === 0) {
      message.error('请勾选测点图例');
      return;
    }
    const addPointType = await form.getFieldValue('pointType');
    callback?.(selectedRows, addPointType); // 执行传入回调
    handleClose(); // 关闭回调
  };

  return (
    <DragModal
      title="选择图例"
      open={visible}
      onCancel={handleClose}
      width={1080}
      centered // 垂直居中展示 Modal
      footer={false}
    >
      <Form
        layout="inline"
        form={form}
        labelWrap // 自动换行
        colon={false} // label 后面是否跟冒号
        initialValues={{
          pointType: devicesList[0]?.dictCode || '',
        }}
      >
        <Form.Item name="pointCode" label="编码">
          <Input type="text" placeholder="请输入" />
        </Form.Item>
        <Form.Item name="pointName" label="名称">
          <Input type="text" placeholder="请输入" />
        </Form.Item>
        <Form.Item name="pointType" label="类型">
          <Select
            placeholder="请选择"
            options={devicesList}
            style={{ width: 150 }}
            fieldNames={{
              label: 'dictName',
              value: 'dictCode',
            }}
            onChange={() => search.submit()}
          />
        </Form.Item>
        <Form.Item label="">
          <Space size={10}>
            <Button onClick={search.submit}>查询</Button>
            <Button onClick={search.reset}>重置</Button>
            <Button type="primary" onClick={handleOk}>
              确认选择
            </Button>
          </Space>
        </Form.Item>
      </Form>
      <div className={styles['table-container']}>
        <Table<Pointlist.List>
          size="small" // 表格大小
          columns={columns} // 列
          dataSource={dataSource} // 数据
          loading={loading} // 加载
          onChange={onChange} // 分页
          rowKey={(_) => _.pointCode} // 行key
          rowSelection={{
            selectedRowKeys,
            onChange: onSelectChange,
            preserveSelectedRowKeys: true, // 跨页勾选
            getCheckboxProps: (record) => ({
              disabled: disabledRowKeys.includes(record.pointCode), // 如果行在禁止取消勾选的列表中，则禁用取消勾选
            }),
          }}
          pagination={{
            ...pagination,
            size: 'small',
            pageSizeOptions: [10, 20, 50, 100],
            showQuickJumper: true, // 是否可以快速跳转至某页
            showSizeChanger: true, // 是否可以改变 pageSize
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </div>
    </DragModal>
  );
});
export default memo(LegendListModal);
