import {
  FC,
  ReactNode,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { SvgModal } from '../svg-modal/svg-modal.component';
import { PropOptions, defaultEndDate, defaultStartDate } from './line-chart-modal';
import styles from './line-chart-modal.component.less';
import { LineChart } from '../line-chart/line-chart.component';
import { ConfigProvider, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import locale from 'antd/locale/zh_CN';

dayjs.locale('zh-cn');
const { RangePicker } = DatePicker;
let selectBegin: string = ''; //历史记录开始日期
let selectEnd: string = ''; //历史记录结束日期
export const LineChartModal: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        title,
        close,
        queryFn,
        resetFn,
        open,
        width,
        height,
        children,
        formComponent = null,
        pointComponent,
        chartData,
        loading = false,
        unit = '',
      },
      ref,
    ) => {
      const lineRef = useRef<any>(null);
      const [forms] = Form.useForm();
      useImperativeHandle(ref, () => ({}));
      useEffect(() => {
        if (open === false && lineRef.current) {
          lineRef.current.clear();
        }
      }, [open]);
      //关闭处理
      function closeHandler(): void {
        forms.resetFields();
        close();
      }
      //日期改变
      function dateChange(date: any, dateString: string | string[]): void {
        if (dateString && dateString[0]) {
          selectBegin = dateString[0];
          selectEnd = dateString[1];
        }
      }
      //统计数据查询
      function query(): void {
        queryFn({
          ...forms.getFieldsValue(),
          selectBegin,
          selectEnd,
        });
      }
      //统计数据重置
      function reset(): void {
        forms.resetFields();
        resetFn();
      }
      //组件默认表单
      function defFormComponent(): ReactNode {
        return (
          <Form
            form={forms}
            layout="inline"
            initialValues={{ layout: 'inline' }}
            style={{ maxWidth: 'none', paddingLeft: '23px' }}
          >
            <Form.Item label="日期" name="date">
              <ConfigProvider locale={locale}>
                <RangePicker
                  showTime={{ format: 'HH:mm:00' }}
                  format="YYYY-MM-DD HH:mm:00"
                  onChange={dateChange}
                />
              </ConfigProvider>
            </Form.Item>
            {formComponent}
            <Form.Item>
              <type-button loading={loading} onClick={query}>
                查询
              </type-button>
            </Form.Item>
            <Form.Item>
              <type-button loading={loading} onClick={reset}>
                重置
              </type-button>
            </Form.Item>
          </Form>
        );
      }
      return (
        <SvgModal
          width={width}
          height={height}
          close={closeHandler}
          isFooterBtn={false}
          open={open}
          title={title}
        >
          <div className={styles['modal-container']}>
            <div className={styles['chart-container-box']}>
              <div className="form-container">
                {/* 表单区域 */}
                {defFormComponent()}
              </div>
              <div className={styles['chart-container']}>
                <LineChart
                  ref={lineRef}
                  chartData={chartData}
                  unit={unit}
                  loading={loading}
                ></LineChart>
              </div>
            </div>
            {/* 对比测点 */}
            <div className={styles['point-list']}>{pointComponent}</div>
          </div>
          {children}
        </SvgModal>
      );
    },
  ),
);
