import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { ReactNode, FC, ElementRef } from 'react';
import { shallowEqual } from 'react-redux';
import { useSelector } from 'umi';
import { message } from 'antd';
import type { DatePickerProps } from 'antd';
import dayjs from 'dayjs';

import BaseForm from '@/components/base-form/base-form.component';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { ModalContentWrapper } from './calc-single-modal.style';
import { calc } from '../../electricity-calculation.service';
import { EElementType } from '@/components/base-form/base-form.types';
import type { IBaseFormConfig } from '@/components/base-form/base-form.types';
import type {
  IRootState,
  IState,
} from '@/pages/electricity-calculation/models/electricityCalculation';

interface IProps {
  children?: ReactNode;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

// 内部使用的时间刻度类型
type TimeScaleType = 'year' | 'month' | 'day' | 'quarter';

const CalcSingleModal: FC<IProps> = (props) => {
  const { visible, setVisible } = props;

  const [messageApi, contextHolder] = message.useMessage();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [currentTimeScale, setCurrentTimeScale] = useState<TimeScaleType>('year');
  // 添加state来存储计算结果
  const [calcResult, setCalcResult] = useState<{ [key: string]: any } | null>(null);

  const baseFormRef = useRef<ElementRef<typeof BaseForm>>(null);

  const { record } = useSelector<IRootState, Pick<IState, 'record'>>(
    ({ electricityCalculation: state }) => ({
      record: state.record,
    }),
    shallowEqual,
  );

  // 当模态框显示时初始化表单
  useEffect(() => {
    if (visible && baseFormRef.current) {
      // 设置时间刻度默认值为"年"
      baseFormRef.current.baseFormInstance.setFieldsValue({ timeScale: 'year' });
      setCurrentTimeScale('year');
      // 重置计算结果
      setCalcResult(null);
    }
  }, [visible]);

  // 处理时间刻度变化
  const handleTimeScaleChange = useCallback((value: TimeScaleType) => {
    setCurrentTimeScale(value);
    // 当时间刻度变化时，重置计算时间值
    if (baseFormRef.current) {
      baseFormRef.current.baseFormInstance.setFieldsValue({
        calcTime: null,
        startCalcTime: null,
        endCalcTime: null,
      });
    }
    // 重置计算结果
    setCalcResult(null);
  }, []);

  const handleModalOnConfirm = useCallback(async () => {
    setConfirmLoading(true);
    try {
      // 表单验证
      await baseFormRef.current?.baseFormInstance.validateFields();

      // 获取表单值
      const values = baseFormRef.current?.baseFormInstance.getFieldsValue();
      const { timeScale, calcTime, startCalcTime, endCalcTime } = values;

      let startTime: dayjs.Dayjs;
      let endTime: dayjs.Dayjs;

      if (timeScale === 'quarter') {
        // 对于"刻"时间刻度，使用用户选择的开始和结束时间
        if (!startCalcTime || !endCalcTime) {
          messageApi.error('请选择开始和结束时间');
          return;
        }

        startTime = dayjs(startCalcTime);
        endTime = dayjs(endCalcTime);

        // 验证开始时间必须早于结束时间
        if (startTime.isAfter(endTime)) {
          messageApi.error('开始时间必须早于结束时间');
          return;
        }
      } else {
        // 对于其他时间刻度，使用单个计算时间
        if (!calcTime) {
          messageApi.error('请选择计算时间');
          return;
        }

        // 根据时间刻度计算开始时间和结束时间
        switch (timeScale) {
          case 'year':
            // 年份的第一秒到下一年的第一秒（左闭右开）
            startTime = dayjs(calcTime).startOf('year');
            endTime = dayjs(calcTime).add(1, 'year').startOf('year');
            break;
          case 'month':
            // 月份的第一秒到下一月的第一秒（左闭右开）
            startTime = dayjs(calcTime).startOf('month');
            endTime = dayjs(calcTime).add(1, 'month').startOf('month');
            break;
          case 'day':
            // 天的第一秒到下一天的第一秒（左闭右开）
            startTime = dayjs(calcTime).startOf('day');
            endTime = dayjs(calcTime).add(1, 'day').startOf('day');
            break;
          default:
            messageApi.error('未知的时间刻度类型');
            return;
        }
      }

      // 格式化为 YYYY-MM-DD HH:mm:ss 字符串
      const startTimeStr = startTime.format('YYYY-MM-DD HH:mm:ss');
      const endTimeStr = endTime.format('YYYY-MM-DD HH:mm:ss');

      // 调用计算接口
      const {
        code,
        data,
        message: msg,
      } = await calc({
        code: record.code,
        startTime: startTimeStr,
        endTime: endTimeStr,
      });

      if (code === '1') {
        messageApi.success('计算成功');
        // 将返回的数据保存到state中
        setCalcResult(data);
      } else {
        messageApi.error(msg);
        setCalcResult(null);
      }
    } catch (error: any) {
      console.error('提交表单失败:', error);
      messageApi.error(error.message || '提交失败，请检查表单');
      setCalcResult(null);
    } finally {
      setConfirmLoading(false);
    }
  }, [record, messageApi]);

  const handleModalOnCancel = useCallback(() => {
    baseFormRef.current?.baseFormInstance.resetFields();
    setCalcResult(null);
    setVisible(false);
  }, [setVisible]);

  // 将时间刻度转换为DatePicker需要的PickerMode类型
  const getPickerMode = (timeScale: TimeScaleType): DatePickerProps['picker'] => {
    if (timeScale === 'year') return 'year';
    if (timeScale === 'month') return 'month';
    if (timeScale === 'quarter') return 'date'; // 对于"刻"使用date模式，而不是quarter
    // 对于'day'使用'date'
    return 'date';
  };

  // 根据时间刻度获取日期格式
  const getDateFormat = (timeScale: TimeScaleType): string => {
    switch (timeScale) {
      case 'year':
        return 'YYYY';
      case 'month':
        return 'YYYY-MM';
      case 'day':
        return 'YYYY-MM-DD';
      case 'quarter':
        return 'YYYY-MM-DD HH:mm';
      default:
        return 'YYYY-MM-DD';
    }
  };

  // 根据时间刻度获取placeholder文本
  const getPlaceholder = (timeScale: TimeScaleType): string => {
    switch (timeScale) {
      case 'year':
        return '请选择年份';
      case 'month':
        return '请选择月份';
      case 'day':
        return '请选择日期';
      case 'quarter':
        return '请选择时间';
      default:
        return '请选择';
    }
  };

  // 获取分钟禁用选项的配置
  const getDisabledMinutes = () => {
    // 只允许选择0、15、30、45分钟
    const minutes = [];
    for (let i = 0; i < 60; i++) {
      if (i % 15 !== 0) {
        minutes.push(i);
      }
    }
    return minutes;
  };

  // 获取时间选择的配置
  const getTimePickerConfig = () => {
    return {
      format: 'HH:mm',
      minuteStep: 15, // 设置分钟步长为15，即一刻钟
      hideDisabledOptions: true,
      disabledMinutes: () => getDisabledMinutes(),
    } as any;
  };

  // 渲染计算结果
  const renderCalcResult = () => {
    if (!calcResult) return null;

    // 获取结果对象的第一个键值对
    const key = record.name;
    const value = calcResult[key];

    return (
      <div
        style={{
          marginTop: '20px',
          padding: '15px',
          border: '1px solid #113f79',
          borderRadius: '4px',
          background: '#113f79',
        }}
      >
        <h3 style={{ fontSize: '16px', marginBottom: '10px', color: '#0ae4ff' }}>计算结果</h3>
        <div style={{ display: 'flex', color: '#fff' }}>
          <span style={{ fontWeight: 'bold', marginRight: '10px' }}>{key}:</span>
          <span>{value} 万千瓦时</span>
        </div>
      </div>
    );
  };

  // 生成表单配置
  const baseFormConfig = useMemo<IBaseFormConfig>(() => {
    const formConfig: IBaseFormConfig = {
      layout: 'inline',
      initialValues: { timeScale: 'year' },
      formPorps: { labelCol: { span: 4 }, wrapperCol: { span: 20 } },
      elements: [
        {
          key: 'input_time_scale',
          type: EElementType.RADIO,
          label: '时间刻度',
          field: 'timeScale',
          rules: [{ required: true, message: '请选择时间刻度！' }],
          options: [
            { label: '年', value: 'year' },
            { label: '月', value: 'month' },
            { label: '日', value: 'day' },
            { label: '刻（15分钟）', value: 'quarter' },
          ],
          formItemProps: { style: { width: '95%' } },
          props: {
            onChange: (e: any) => handleTimeScaleChange(e.target.value),
          },
        },
      ],
    };

    // 根据时间刻度类型决定显示单个DatePicker还是两个DatePicker
    if (currentTimeScale === 'quarter') {
      // 对于"刻"时间刻度，显示开始和结束时间选择
      formConfig.elements.push(
        {
          key: 'input_start_calc_time',
          type: EElementType.DATEPICKER,
          label: '开始时间',
          field: 'startCalcTime',
          rules: [{ required: true, message: '请选择开始时间！' }],
          props: {
            picker: 'date',
            placeholder: '请选择开始时间',
            showTime: getTimePickerConfig(),
            format: getDateFormat(currentTimeScale),
          },
          formItemProps: { style: { width: '95%' } },
        },
        {
          key: 'input_end_calc_time',
          type: EElementType.DATEPICKER,
          label: '结束时间',
          field: 'endCalcTime',
          rules: [{ required: true, message: '请选择结束时间！' }],
          props: {
            picker: 'date',
            placeholder: '请选择结束时间',
            showTime: getTimePickerConfig(),
            format: getDateFormat(currentTimeScale),
          },
          formItemProps: { style: { width: '95%' } },
        },
      );
    } else {
      // 对于其他时间刻度，显示单个DatePicker
      formConfig.elements.push({
        key: 'input_calc_time',
        type: EElementType.DATEPICKER,
        label: '计算时间',
        field: 'calcTime',
        rules: [{ required: true, message: '请选择计算时间！' }],
        props: {
          picker: getPickerMode(currentTimeScale),
          placeholder: getPlaceholder(currentTimeScale),
          showTime: false,
          format: getDateFormat(currentTimeScale),
        },
        formItemProps: { style: { width: '95%' } },
      });
    }

    return formConfig;
  }, [currentTimeScale, handleTimeScaleChange]);

  return (
    <DragModal
      title={`测点电量计算（${record?.name}）`}
      width={800}
      open={visible}
      cancelButtonProps={{ type: 'primary' }}
      confirmLoading={confirmLoading}
      okText={'计算'}
      cancelText={'关闭'}
      onOk={handleModalOnConfirm}
      onCancel={handleModalOnCancel}
      centered
    >
      <ModalContentWrapper>
        {contextHolder}
        <BaseForm ref={baseFormRef} config={baseFormConfig} />
        {renderCalcResult()}
      </ModalContentWrapper>
    </DragModal>
  );
};

export default memo(CalcSingleModal);
