import { request, jsonHeader, formHeader } from '@/utils/request';
import { ResponseOptions } from './monitoring-point-annotation';
import { parseParamFromData } from '@/utils/utils';
interface FilePathOptions {
  id: string; //文件目录id
  attachmentContentList: FileOptions[]; //文件目录下的文件列表
}
export interface FileOptions {
  id: string; //文件id
  type: string; //文件类型
  name: string; //文件名称
  url: string; //文件地址
  userId: string; //文件上传人id
  uploadDate: number; //上传时间戳
  inquireImager: string;
  format?: string; //后缀名
  pictureUrl?: string;
  fileId?: string;
}
export interface ProductOptions {
  name: string; //名称
  code: string; //编码
  routingUrl: string; //跳转地址
  displayLocation: '_micro'; //交互方式-微应用
  checkPermission: 1; //权限验证
  displayState: 1; //显示Sidebar
  deleteFlag: 0; //是否隐藏
  sandbox: 1; //沙箱隔离
  preLoad: 1; //预加载
  injectToken: 0; //注入Token
  activeRule: '/micro_operations/'; //资源地址
  activeRule2: string; //开发环境地址
  sortNo: number; //排序
  iconClass: null;
  memo: null;
  memu: true;
  props: string;
  parentId: string;
  id?: string;
  idPath?: string;
  projectCode: 1; //模块项目编码
}
export async function attachmentUpload(data: object): Promise<ResponseOptions<FilePathOptions>> {
  return request.post(`/Attachment/upload`, {
    data: parseParamFromData(data),
    headers: formHeader,
  });
}
//新增菜单
export async function addPrivilegeProduct(data: ProductOptions): Promise<ResponseOptions<any>> {
  return request.post(`/PrivilegeProduct/`, { data: JSON.stringify(data), headers: jsonHeader });
}
//更新菜单
export async function updatePrivilegeProduct(data: ProductOptions): Promise<ResponseOptions<any>> {
  return request.post(`/PrivilegeProduct/${data.id}`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//删除菜单
export async function delPrivilegeProduct(id: string): Promise<ResponseOptions<any>> {
  return request.delete(`/PrivilegeProduct/${id}?recState=1`);
}
//查询菜单列表
export async function privilegeProductList(
  code: string,
): Promise<ResponseOptions<ProductOptions[]>> {
  return request.get(`/PrivilegeProduct/list?code=${code}`);
}
