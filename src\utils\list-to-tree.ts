import { cloneDeep } from 'lodash';

/**
 * 将列表数据以HashTable方式转换为书结构数据
 *
 * @param list 列表数据
 * @param childKey 子节点使用Key值，缺省为children
 * @returns
 */
export function listToTree(list: any[], childKey: string = 'children', keyName: string = 'id', parentKey: string = 'parentId'): any[] {
  if (!list.length) {
    return [];
  }
  const cacheList = cloneDeep(list).map((el) => ({ ...el, title: el.name, text: el.name, key: el.id }));
  const hashTable: { [key: string]: any } = {};
  cacheList.forEach((item) => {
    const treeNode = item;
    hashTable[item[keyName]] = treeNode;
  });

  const data: any[] = [];
  cacheList.forEach((item) => {
    const parent: any = hashTable[item[parentKey]];
    if (parent) {
      parent[childKey] = parent[childKey] || [];
      parent[childKey].push(item);
    } else {
      data.push(item);
    }
  });

  return data;
}
