import { DateType } from '@/pages/intelligentReport/templateManagement/pages/addTemplateVersion/components/influxes-table/influxes-table';
import { PlanTypeEnum } from '@/services/intelligentReport/report-plan';

export interface PropOptions {
  createInit: (designer: any) => void; //创建初始化方法
  cellClick?: (data: CellPositionOptions) => void; //单元格点击事件
  workTableChange?: (data: WorkTableChangeOptions) => void; //工作表切换
  workTableNameChange?: (data: WorkTableNameChangeOptions) => void; //工作表名称切换
  fileImportEnd?: (status: 0 | 1) => void; //文件导入结束方法1-成功，0-失败
  isWookTable?: boolean; //是否只创建工作薄
  isRibbonCollapse?: boolean; //是否折叠菜单
  isHiddenFileMenu?: boolean; //是否隐藏文件菜单
  disabled?: boolean; //编辑控制
  cellMenu?: RightMenuOptions[];
  messageOptions?: MessageOptions; //异常操作消息提示
  ref?: any;
}
//工作表切换配置
export interface WorkTableChangeOptions {
  newSheet?: any;
  oldSheet: any;
}
//工作表名称配置
export interface WorkTableNameChangeOptions {
  newValue: any;
  oldValue: any;
  sheet: any;
}
//右键单元格菜单配置
export interface RightMenuOptions {
  text: string;
  commandName: string;
  iconClass: string;
  visibleContext: string;
  execute: (event: any) => void;
}
//单元格操作配置
export interface CellControllerOptions {
  [key: string]: any;
  id?: string;
  tableId: string; //工作表id
  tableName: string; //工作表名称
  startPosition: string; //开始
  endPosition: string; //结束
  isEdit: boolean; //是否可以编辑
  tableType?: DataTypeEnum;
}
export enum DataTypeEnum {
  INFLUXES = 'influxes',
  AUTHORITIE = 'authoritie',
  PLAN = 'plan',
  HISTORY = 'history',
}
//填充数据表格配置
export interface RegionsOptions extends CellControllerOptions {
  templateId?: string;
  versionId?: string;
  rowCount: number; //行数
  colCount: number; //列数
  editUserId: string; //可操作用户id;
  planCycle: PlanTypeEnum; //计划周期
  attributeId: string; //计划属性id
  type: '0' | '1'; //填充类型0--计划绑定；1--上月/去年
}
//工作表权限配置
export interface AuthoritieOptions extends CellControllerOptions {
  templateId?: string;
  versionId?: string;
  rowCount: number; //行数
  colCount: number; //列数
  editUserId: string; //可操作用户id;
}
//测点类型值配置
export interface InfluxesOptions extends CellControllerOptions {
  templateId?: string;
  versionId?: string;
  rowCount: number; //行数
  colCount: number; //列数
  editUserId: string; //可操作用户id;
  measureId: string; //测点id
  measureName: string; //测点名称
  dataType: string; //测点pointType
  type: InfluxesEnums;
  value: 'max' | 'min' | '';
  dateType: DateType; //日期类型
}
export enum InfluxesEnums {
  MAX_OR_MIN = 'MAX_OR_MIN', //最大最小值
  MAX = 'MAX', //最大值
  MIN = 'MIN', //最小值
  AVERAGE = 'AVERAGE', //平均值
  SUM = 'SUM', //累积值
  INSTANTANEOUS = 'INSTANTANEOUS', //瞬时值
  SWITCH = 'SWITCH', //遥信开关次数
}
//单元格设置配置
export interface CellOptions {
  position: string;
  tableId: string;
  value: string | number;
}
//单元格数据返回配置
export interface ActiveCellOptions {
  cell: any; //单元格对象
  start: string; //开始坐标
  end: string; //结束坐标
  selection: CellRangeOptions;
}
//单元格区域配置
export interface CellRangeOptions {
  row: number; //行索引
  col: number; //列索引
  rowCount: number; //行数
  colCount: number; //列数
}
//单元格位置配置
export interface CellPositionOptions {
  row: number; //行索引
  col: number; //列索引
  position: string; //定位
}
export interface MessageOptions {
  [key: string]: any;
  disabledEditCell: string; //禁止编辑下编辑单元格提示
  disabledAddTable: string; //禁止编辑下新增表格提示
}
export const MessageEnum: MessageOptions = {
  disabledEditCell: '请进入编辑状态，再对表格进行编辑！',
  disabledAddTable: '请进入编辑状态，再新增工作表！',
};
//需要隐藏的右键菜单项name
export const hiddenMenuList = [
  'unprotectSheet',
  'gc.spread.contextMenu.hideSheet',
  'sheetTag',
  'showTabColor',
  'protectSheet',
  'gc.spread.contextMenu.changeSheetTabPosition',
  'gc.spread.contextMenu.deleteSheet',
  'gc.spread.contextMenu.insertSheet',
  'sheetTabMoveOrCopy',
];

export interface UseImperativeOptions {
  GC: any; //表格组件工具类
  designerInit: any; //表格实例
  verticalPositionTop: () => void; //表格滚动条回到置顶位置
  importFile: () => void; //导入文件
  exportFile: (name?: string) => Promise<any>; //导出文件
  liftWorkTableProtected: (isProtected: boolean) => void; //解除工作表保护
  getAllWorkTable: () => any[]; //获取所有工作表
  cellImgChange: (data: CellControllerOptions, status: 0 | 1, img: string) => void; //单元格背景图修改,0-设置，1-清除
  cellBackChange: (
    data: CellControllerOptions,
    status: 0 | 1,
    color?: string,
    isAuth?: boolean,
  ) => void; //单元格背景色修改,0-设置，1-清除
  importJSON: (json: string) => void; //导入JSON数据
  cellIsMerged: (sheet: any, selection: CellRangeOptions) => boolean; //判断区域是否合并单元格
  setActiveSheet: (name: string) => void; //根据表单名称切换活跃工作表
  setTableCellValue: (list: CellOptions[]) => void; //设置指定工作表的单元格数据
  setActiveTableIndex: (index: number) => void; //设置当前工作表索引
  hiddenTableMenu: () => void; //隐藏菜单栏
  tableCheckRangeEditHandler: (
    checkRange: CellControllerOptions[],
    isProtected: boolean, //是否锁定表单
  ) => void; //指定范围单元格允许编辑
  getWorkData: () => any; //获取工作薄数据
  getWorkTableData: (name?: string) => any; //获取工作表数据,默认为当前激活工作表，可以传入其他工作表name
  getActiveCell: () => ActiveCellOptions | undefined; //获取激活单元格
  getCellRange: (position: string) => CellRangeOptions; //获取指定单元格
}
