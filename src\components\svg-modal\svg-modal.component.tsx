import { FC, memo, useRef } from 'react';
import type { PropOptions } from './svg-modal';

export const SvgModal: FC<PropOptions> = memo(
  ({
    height = '480px',
    width = '500px',
    title,
    open,
    children,
    close,
    confirmText = '确定',
    cancelText = '关闭',
    maskClosable = false,
    isFooterBtn = true,
    onOk,
    confirmloading = false,
  }) => {
    const svgRef = useRef<any>(null);
    if (svgRef.current) {
      svgRef.current.closeFn = closeFn;
      if (onOk) {
        svgRef.current.onOkFn = onOk;
      }
    }
    function closeFn() {
      if (close) {
        close();
      }
    }
    return (
      <svg-reader
        confirmtext={confirmText}
        canceltext={cancelText}
        confirmloading={confirmloading}
        height={height}
        width={width}
        maskclosable={maskClosable}
        ref={svgRef}
        visble={open}
        titles={title}
        isfooterbtn={isFooterBtn}
      >
        {children}
      </svg-reader>
    );
  },
);
