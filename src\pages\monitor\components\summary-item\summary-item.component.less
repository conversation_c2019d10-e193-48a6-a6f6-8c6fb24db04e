.summaryItem {
  height: 100%;
  flex: 1;
  border-radius: 16px;
  background: rgba(40, 130, 232, 0.2);
  display: flex;

  .left {
    width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;

    & > img {
      height: 45%;
      width: fit-content;
    }
  }

  .right {
    flex: 1;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;

    & > .title {
      font-size: 18px;
      font-weight: bold;

      &:hover {
        cursor: pointer;
      }
    }

    & > .text {
      margin-top: 6px;
      display: flex;
      font-size: 18px;
      align-items: baseline;

      & > .num {
        color: #0ae4ff;
        font-weight: bold;
        margin: 0 10px 0 0;
      }

      & > .unit {
        font-size: 14px;
      }
    }
  }
}
