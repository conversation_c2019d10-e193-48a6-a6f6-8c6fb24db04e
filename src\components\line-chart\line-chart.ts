import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
import { ColorAttr } from '@antv/g2plot';

export interface PropOptions {
  ref?: any;
  loading?: boolean; //加载控制
  stepType?: 'hv' | 'vh' | 'hvh' | 'vhv';
  unit?: string; //统计图单位
  unitY?: number;
  padding?: number[];
  color?: ColorAttr; //颜色面板
  sliderColor?: string; //滚动条颜色
  legend?: boolean;
  chartData: DataOptions[]; //统计图数据
  smooth?: boolean;
  isRealTime?: boolean; //是否需要实时请求数据
  realTime?: number; //实时间隔
  realTimeFn?: () => Promise<any>; //实时方法
  sliderFn?: () => void; //缩率图拖动事件
}
