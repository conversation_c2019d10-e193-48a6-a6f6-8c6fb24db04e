import { ReactNode, useEffect, useRef, useState } from 'react';
import styles from './report-examine.page.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { Spin, Tabs, message } from 'antd';
import { ExamineResult, TabItems, lookTooltip } from './report-examine';
import ProcessComponent from '@/components/process-component/process.component';
import { ExamineModal } from './components/examineModal/examine-modal.component';
import { SpreadTable } from '@/components/spread-table/spread-table.component';
import { history, useLocation, useParams } from 'umi';
import { PageType } from '../../monthly-production-report';
import { DetailOptions } from '../report-edit/report-edit';
import {
  ReportStateEnum,
  getNextTasks,
  reportDetail,
} from '@/services/intelligentReport/report-management';
import { useSyncCallback } from '@/hooks/useGetState';
import { UseImperativeOptions } from '@/components/spread-table/spread-table';

const ReportExaminePage = () => {
  const [examineModalShow, setExamineModalShow] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [designer, setDesigner] = useState<any>();
  const [activeKey, setActiveKey] = useState<string>(() => {
    if (TabItems) {
      return TabItems[0].key;
    } else {
      return '';
    }
  });
  //月报详情
  const [detail, setDetail] = useState<DetailOptions>();
  //behavior流程参数
  const [tasksParams, setTasksParams] = useState<ExamineResult[]>([]);
  const spreadRef = useRef<UseImperativeOptions>({} as UseImperativeOptions);
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const { type } = (location.state as any) || { type: PageType.EXAMINE };
  const requestAfterHandlerSync = useSyncCallback(requestAfterHandler);
  //创建表格
  function createTable(designer: any): void {
    setDesigner(designer);
    setLoading(true);
    reportDetail<DetailOptions>(id)
      .then((res) => {
        if (res.code === '1') {
          setDetail(res.data);
          requestAfterHandlerSync();
          if (res.data.taskId && type === PageType.EXAMINE) {
            getFlowableNextTasks(res.data.taskId);
          }
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //获取审批流程参数
  function getFlowableNextTasks(id: string): void {
    getNextTasks(id).then((res) => {
      if (res.code === '1') {
        setTasksParams(res.data);
      }
    });
  }
  //导入数据
  function createWorkTable(data: string): void {
    spreadRef.current.importJSON(data);
    spreadRef.current.verticalPositionTop();
  }
  //获取数据后的表格处理
  function requestAfterHandler(): void {
    if (detail) {
      createWorkTable(detail.monthlyData);
      spreadRef.current.setActiveTableIndex(0);
    }
  }
  //导出表格
  function exportTable(): void {
    setLoading(true);
    spreadRef.current
      .exportFile(detail?.monthlyName)
      .then((res) => {
        message.success('导出成功');
        setLoading(false);
      })
      .catch((err) => {
        message.warning('导出失败');
        setLoading(false);
      });
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    const isExamine = type === PageType.EXAMINE && tasksParams.length > 0;
    const exameStyle = 'border-top-right-radius:0px;border-bottom-right-radius:0px';
    const exportStyle = 'border-top-left-radius:0px;border-bottom-left-radius:0px';
    return (
      <div className={styles['controller-container']}>
        {isExamine && (
          <type-button styles={exameStyle} loading={loading} onClick={openExamineModal}>
            {detail?.taskName || '报表审核'}
          </type-button>
        )}
        <type-button styles={isExamine ? exportStyle : ''} onClick={exportTable} loading={loading}>
          导出
        </type-button>
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //审批完成
  function examineOk(): void {
    setExamineModalShow(false);
    history.goBack();
  }
  //打开审核弹框
  function openExamineModal(): void {
    setExamineModalShow(true);
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  //流程图模板
  function processComponent(): ReactNode {
    return (
      <section
        style={{
          display: activeKey === '2' ? 'block' : 'none',
        }}
        className={styles['process-container']}
      >
        <ProcessComponent details={detail}></ProcessComponent>
      </section>
    );
  }
  //tab改变
  function onTabsChange(key: string) {
    setActiveKey(key);
  }
  return (
    <div className={`${styles['report-examine-page']} report-examine-page`}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          {detail && (
            <span>
              {detail.monthlyName} ,
              <span style={{ marginLeft: '10px' }}>{`${detail.year}-${detail.month}`}</span>
            </span>
          )}
        </div>
        {controllerComponent()}
      </section>
      <section className="tab-container">
        <Tabs activeKey={activeKey} items={TabItems} onChange={onTabsChange} />
      </section>
      {/* 这里使用display控制是因为表格组件这样不会重新创建，节约显示时间和性能 */}
      <Spin
        spinning={loading}
        wrapperClassName={`${styles.excel} ${activeKey !== '1' ? styles.hidden : null}`}
      >
        <SpreadTable
          isWookTable={true}
          disabled={false}
          messageOptions={lookTooltip}
          ref={spreadRef}
          createInit={createTable}
        ></SpreadTable>
      </Spin>
      {/* 流程图 */}
      {processComponent()}
      <ExamineModal
        detail={detail}
        tasksParams={tasksParams}
        onOk={examineOk}
        open={examineModalShow}
        close={() => setExamineModalShow(false)}
      ></ExamineModal>
    </div>
  );
};

export default ReportExaminePage;
