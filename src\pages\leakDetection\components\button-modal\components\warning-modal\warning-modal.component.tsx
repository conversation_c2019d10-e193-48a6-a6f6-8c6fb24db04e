import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { FC, memo, useEffect, useState } from 'react';
import {
  PageParams,
  PropOptions,
  TableOptions,
  inputW,
  params as paramsOptions,
} from './warning-modal';
import { DatePicker, Form, Select } from 'antd';
import styles from './warning-modal.component.less';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';

let params: PageParams = JSON.parse(JSON.stringify(paramsOptions));
export const WarningModal: FC<PropOptions> = memo(({ open, close }) => {
  const [loading, setLoading] = useState<boolean>(false);
  //表格数据
  const [tableList, setTableList] = useState<TableOptions[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [forms] = Form.useForm();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '状态',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '报警原因',
      dataIndex: 'code',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '运行时长（分）',
      dataIndex: 'createTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '间隔时长（分）',
      dataIndex: 'remark',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '报警时间',
      dataIndex: 'time',
      align: 'center',
      ellipsis: true,
    },
  ];
  useEffect(() => {
    if (open) {
      forms.resetFields();
      params = JSON.parse(JSON.stringify(paramsOptions));
    }
  }, [open]);
  function closeFn(): void {
    forms.resetFields();
    close();
  }
  //获取列表数据
  function getPageData(): void {}
  //页码改变
  function pageChange(page: number, pageSize: number): void {
    params.pageNum = page;
    params.pageSize = pageSize;
    getPageData();
  }
  return (
    <SvgModal
      width="1200px"
      height="700px"
      isFooterBtn={false}
      close={closeFn}
      open={open}
      title="渗漏排水运行报警记录"
    >
      <div className={styles['modal-container']}>
        <Form
          layout="inline"
          className={styles['search-form']}
          style={{ width: '100%' }}
          form={forms}
        >
          <Form.Item<PageParams> label="状态" name="name">
            <Select style={inputW} options={[]} placeholder="请选择..."></Select>
          </Form.Item>
          <Form.Item<PageParams> label="时间" name="startDate" style={{ marginLeft: '30px' }}>
            <DatePicker style={inputW} placeholder="请选择..." />
          </Form.Item>
          <Form.Item>
            <type-button loading={loading}>确定</type-button>
          </Form.Item>
          <Form.Item>
            <type-button loading={loading}>重置</type-button>
          </Form.Item>
        </Form>
        <div className={styles['tips-container']}>
          <span>周期内单次运行时长平均值：297分；单次间隔时长平均值：1269分</span>
        </div>
        <div className={styles['table-container']}>
          <TableComponent
            scroll={{ y: 380 }}
            params={{
              pageSize: params.pageSize,
              pageChange,
              total,
              current: params.pageNum,
            }}
            loading={loading}
            columns={columns}
            tableList={tableList}
          ></TableComponent>
        </div>
      </div>
    </SvgModal>
  );
});
