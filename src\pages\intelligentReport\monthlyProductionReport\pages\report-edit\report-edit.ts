import { ReportStateEnum } from '@/services/intelligentReport/report-management';

export interface DetailOptions {
  id: string;
  monthlyName: string; //月报名称
  monthlyData: string; //报表json数据
  processInstanceId: string; //流程id
  year: string;
  month: string;
  state: ReportStateEnum; //状态
  taskId?: string; //任务id
  taskName?: string; //流程按钮名称
  type: string; //类型
  workflowApprovalRecordList: any[]; //处理流程数组
}
export const editTooltip = {
  disabledEditCell: '没有权限对该区域进行操作！',
  disabledAddTable: '没有权限新增工作表！',
};
export const lookTooltip = {
  disabledEditCell: '请进入编辑页面进行编辑！',
  disabledAddTable: '没有权限新增工作表！',
};
