import React, { memo, useEffect, useMemo, useState } from 'react';
import type { ReactNode, FC } from 'react';
import { useSelector, useDispatch } from 'umi';
import { shallowEqual } from 'react-redux';

import { TopologyContentWrapper } from './topology-content.style';
import QueryForm from './c-cpns/query-form/query-form.component';
import EchartDiagram from './c-cpns/echart-diagram/echart-diagram.component';
import DetailPanel from './c-cpns/detail-panel/detail-panel.component';
import {
  findAndInsertRoot,
  findDictTree,
  getMonitoringPointList,
} from './topology-content.service';
import { correctIsLeaf } from '@/utils/bfs-tree-deep';
import type { IRootState, IState } from './models/TopologyContent';
import {
  equipPointNodeTemplate,
  monitoringPointNodeTemplate,
  rootNodeTemplate,
} from './config/constants';
import EquipDetailPanel from './c-cpns/equip-detail-panel/equip-detail-panel.component';

export function traverse(dictCode: string, nodes: any[]) {
  for (const node of nodes) {
    if (node.dictCode === dictCode) return node;
    if (node.children?.length) {
      const result: any = traverse(dictCode, node.children);
      if (result) return result;
    }
  }
  return null;
}

export function traverseLeaf(nodes: any[]) {
  const leaves: any[] = [];
  for (const node of nodes) {
    if (node.children.length) leaves.push(...traverseLeaf(node.children));
    else leaves.push(node);
  }
  return leaves;
}

interface IProps {
  children?: ReactNode;
  rootInfoData: any;
}

const TopologyContent: FC<IProps> = (props) => {
  const { rootInfoData } = props;

  const [rootUUID, setRootUUID] = useState<string | null>(null);
  const [nodes, setNodes] = useState<any[]>([]);
  const [edges, setEdges] = useState<any[]>([]);
  const [relationList, setRelationList] = useState<any[]>([]);
  const [currentNodeInfo, setCurrentNodeInfo] = useState<any>(null);
  const [selectedBusinessId, setSelectedBusinessId] = useState<any>(null);

  const { queryInfo } = useSelector<IRootState, Pick<IState, 'queryInfo'>>(
    ({ TopologyContent: state }) => ({
      queryInfo: state.queryInfo,
    }),
    shallowEqual,
  );

  const dispatch = useDispatch();

  useEffect(() => {
    collectDeviceInfo();
  }, []);

  useEffect(() => {
    // 获取节点信息
    getDetailData();
  }, [rootInfoData]);

  // 接口-获取节点信息与连线信息
  const getDetailData = () => {
    findAndInsertRoot({
      businessId: Number(rootInfoData.pointCode),
      deviceType: '电厂测点',
      name: rootInfoData.pointName,
    }).then((res) => {
      let root = null,
        rootNode = null;
      const newNodes = res.data.nodeList
        .map((node: any) => {
          let template = null;
          // 根测点
          if (node.currentId === rootInfoData.pointCode) {
            template = JSON.parse(JSON.stringify(rootNodeTemplate));
            template.id = node.uuid;
            template.data.text = node.name;
            template.data.id = node.currentId;
            template.distance = node.distance;
            root = node;
            rootNode = template;
          }
          // 测点
          else if (node.deviceType === '电厂测点') {
            template = JSON.parse(JSON.stringify(monitoringPointNodeTemplate));
            template.id = node.uuid;
            template.data.text = node.name;
            template.data.id = node.currentId;
            template.distance = node.distance;
          }
          // 设备
          else if (node.deviceType === '设备') {
            template = JSON.parse(JSON.stringify(equipPointNodeTemplate));
            template.id = node.uuid;
            template.data.text = node.name;
            template.data.id = node.currentId;
            template.distance = node.distance;
          }

          return template;
        })
        .filter((node: any) => node); // 过滤其他类型的测点（null）

      const newEdges = res.data.relationList.map((relation: any) => {
        const { startId, endId, distance } = relation;
        return {
          id: `${startId}_${endId}`,
          startId,
          endId,
          distance,
          selectable: false, // 设置连线不可被选中
        };
      });

      setRootUUID(root!.uuid); // 设置中心点uuid
      setNodes(newNodes);
      setEdges(newEdges);
      setRelationList(res.data.relationList);
      // 初始化操作表单所选节点为根测点
      setCurrentNodeInfo({
        id: rootNode!.id,
        type: '当前测点',
      });
      setSelectedBusinessId(rootInfoData.pointCode);
    });
  };

  const collectDeviceInfo = () => {
    // 监测设备名称
    findDictTree('monitor_devices').then((res) => {
      const nodes = traverseLeaf(res.data);
      // 修正isLeaf
      correctIsLeaf(nodes);
      dispatch({ type: 'TopologyContent/changeMonitoringDeviceTree', payload: nodes });
    });
  };

  return (
    <TopologyContentWrapper>
      {/* <QueryForm /> */}
      <EchartDiagram
        nodes={nodes}
        edges={edges}
        setSelectedBusinessId={setSelectedBusinessId}
        setCurrentNodeInfo={setCurrentNodeInfo}
      />
      <DetailPanel
        nodes={nodes}
        edges={edges}
        visible={currentNodeInfo && ['测点', '当前测点'].includes(currentNodeInfo.type)}
        selectedBusinessId={selectedBusinessId}
      />
      <EquipDetailPanel
        nodes={nodes}
        edges={edges}
        visible={currentNodeInfo && currentNodeInfo.type === '设备'}
        selectedBusinessId={selectedBusinessId}
      />
    </TopologyContentWrapper>
  );
};

export default memo(TopologyContent);
