import { DatePicker, Form, Select, Space, Tooltip } from 'antd';
import styles from './volute-maintenance-drainage.page.less';
import { ReactNode, useEffect, useState } from 'react';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { PageParams, TableOptions, params as paramsOptions } from './volute-maintenance-drainage';
import { DelModal } from '@/components/del-modal/del-modal.component';
import moment from 'moment';

const { RangePicker } = DatePicker;
const inputW = { width: '250px' };
let params: PageParams = JSON.parse(JSON.stringify(paramsOptions));
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
const TemplateManagementPage = () => {
  //新增弹框控制
  const [addTemModalShow, setAddTemModalShow] = useState<boolean>(false);
  //编辑弹框控制
  const [editTemModalShow, setEditTemModalShow] = useState<boolean>(false);
  //删除弹框控制
  const [delTemModalShow, setDelTemModalShow] = useState<boolean>(false);
  //当前操作数据
  const [curTableData, setCurTableData] = useState<TableOptions>();
  //表格数据
  const [tableList, setTableList] = useState<TableOptions[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [forms] = Form.useForm<PageParams>();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '日期',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '事件名称',
      dataIndex: 'code',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '检修机组',
      dataIndex: 'createTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '排水时长（min）',
      dataIndex: 'remark',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '起始时间',
      dataIndex: 'remark1',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '终止时间',
      dataIndex: 'remark2',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '记录人',
      dataIndex: 'remark3',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      render: (text, record: any) => controllerComponet(record),
      align: 'center',
      ellipsis: true,
      width: 200,
    },
  ];
  useEffect(() => {
    getPageData();
    return () => {
      params = JSON.parse(JSON.stringify(paramsOptions));
    };
  }, []);
  //获取分页数据
  function getPageData(): void {
    // setLoading(true);
    // listPage<TableOptions>(params)
    //     .then((res) => {
    //         if (res.code === '1') {
    //             setTableList(res.data.list);
    //             setTotal(Number(res.data.total));
    //         }
    //         setLoading(false);
    //     })
    //     .catch((err) => {
    setLoading(false);
    //     });
  }
  //删除表格数据方法
  function delTableDataFn(): Promise<any> {
    return Promise.resolve();
  }
  //表格操作栏渲染
  function controllerComponet(record: TableOptions): ReactNode {
    const { createUserId } = record;
    const isAuthoritie = userId === createUserId || userId == 1;
    const look = (
      <Tooltip title="查看">
        <EyeOutlined onClick={() => toLook(record)} className={styles.icon} />
      </Tooltip>
    );
    const edit = (
      <Tooltip title="编辑">
        <EditOutlined onClick={() => openEditModal(record)} className={styles.icon} />
      </Tooltip>
    );
    const del = (
      <Tooltip title="删除">
        <DeleteOutlined onClick={() => openDelModal(record)} className={styles.icon} />
      </Tooltip>
    );
    //编辑:有权限用户
    //删除：只有记录人和超管账户才能删除
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          {look}
          {isAuthoritie && edit}
          {isAuthoritie && del}
        </Space>
      </div>
    );
  }
  //打开删除弹框
  function openDelModal(data: TableOptions): void {
    setCurTableData(data);
    setDelTemModalShow(true);
  }
  //打开编辑弹框
  function openEditModal(data: TableOptions): void {
    setCurTableData(data);
    setEditTemModalShow(true);
  }
  //上部操作按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button onClick={openAddModal}>+新增</type-button>
      </div>
    );
  }
  //跳转查看
  function toLook(data: TableOptions): void {}
  //跳转新增
  function openAddModal(): void {
    setAddTemModalShow(true);
  }
  //查询
  function query(): void {
    const { name, startDate } = forms.getFieldsValue();
    if (startDate) {
      let date: any = startDate;
      params.startDate = moment(date[0].$d).format('YYYY-MM-DD HH:mm:ss');
      params.endDate = moment(date[1].$d).format('YYYY-MM-DD 23:59:59');
    }
    params.name = name;
    params.pageNum = 1;
    getPageData();
  }
  //重置
  function reset(): void {
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    getPageData();
  }
  //页码改变
  function pageChange(page: number, pageSize: number): void {
    params.pageNum = page;
    params.pageSize = pageSize;
    getPageData();
  }
  //搜索表单模板
  function searchFormComponent(): ReactNode {
    const btnStyle = 'border-radius:2px;box-shadow:none';
    return (
      <Form className="report-form" form={forms} layout="inline">
        <Form.Item<PageParams> label="机组" name="name">
          <Select style={inputW} options={[]} placeholder="请选择..."></Select>
        </Form.Item>
        <Form.Item<PageParams> style={{ marginLeft: '30px' }} label="时间" name="startDate">
          <RangePicker style={inputW} />
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={query} styles={btnStyle}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={reset} styles={btnStyle}>
            重置
          </type-button>
        </Form.Item>
      </Form>
    );
  }
  return (
    <div className={styles['page-container']}>
      <section className={styles['top-search-container']}>
        {controllerComponent()}
        {searchFormComponent()}
      </section>
      <section>
        <TableComponent
          params={{
            pageSize: params.pageSize,
            pageChange,
            total,
            current: params.pageNum,
          }}
          loading={loading}
          columns={columns}
          tableList={tableList}
        ></TableComponent>
      </section>
      {/* 删除弹框 */}
      <DelModal
        onOk={query}
        delFn={delTableDataFn}
        open={delTemModalShow}
        close={() => setDelTemModalShow(false)}
      ></DelModal>
    </div>
  );
};

export default TemplateManagementPage;
