.report-add-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-container {
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid #28487b;
}

.controller-container {
  display: flex;
}

.back {
  cursor: pointer;
  margin-left: 20px;
  color: #0392d8;
  display: flex;
  align-items: center;

  span {
    color: #0392d8;
  }
}

.title {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.excel {
  height: 100%;
}
.spin {
  flex: 1;
}
.form {
  padding: 0px 30px;
  margin-top: 20px;

  &-name {
    padding-bottom: 20px;
    border: 1px solid #214177;
    font-size: 16px;
    color: #fff;

    span:first-child {
      padding: 10px 20px;
      background-color: #103677;
      display: inline-block;
      width: 240px;
      height: 100%;
    }

    span:last-child {
      padding-left: 20px;
    }
  }
}
:global {
  .ant-spin-container {
    height: 100%;
  }
}
