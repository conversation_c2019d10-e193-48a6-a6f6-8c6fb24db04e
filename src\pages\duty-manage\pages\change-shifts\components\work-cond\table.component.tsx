import styles from './work-cond.component.less';
import type { ColumnsType } from 'antd/es/table';
import { Button, Table, Flex, ConfigProvider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { memo } from 'react';

// TableComponent 的 props 类型
interface TableComponentProps {
  title: string;
  columns: ColumnsType<any>;
  dataSource: any[];
  onAdd: () => void;
}

// TableComponent 组件
export default memo(function TableComponent({
  title,
  columns,
  dataSource,
  onAdd,
}: TableComponentProps) {
  return (
    <div className={styles['table-container']}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 12 }}>
        <h3 className={styles['table-title']}>{title}</h3>
        <Button type="primary" icon={<PlusOutlined />} onClick={onAdd}>
          新增
        </Button>
      </Flex>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2F4F81', // 表格边框颜色
            },
          },
        }}
      >
        <Table columns={columns} dataSource={dataSource} pagination={false} size="small" bordered />
      </ConfigProvider>
    </div>
  );
});
