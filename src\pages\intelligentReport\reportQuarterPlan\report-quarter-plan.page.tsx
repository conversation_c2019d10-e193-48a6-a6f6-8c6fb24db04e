import { DatePicker, Form, Input, Space, Tooltip } from 'antd';
import styles from './report-quarter-plan.page.less';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { DeleteOutlined, EditOutlined, EyeOutlined, LoginOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { PageParams, listPage, planReportDel } from '@/services/intelligentReport/report-plan';
import { TableOptions, params as paramsOptions } from './report-quarter-plan';
import moment from 'moment';
import { DelModal } from '@/components/del-modal/del-modal.component';

const { RangePicker } = DatePicker;
const inputW = { width: '250px' };
let params: PageParams = JSON.parse(JSON.stringify(paramsOptions));
const ReportQuarterPlan = () => {
  const [tableList, setTableList] = useState<TableOptions[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  //当前操作数据
  const [curTableData, setCurTableData] = useState<TableOptions>();
  //删除弹框控制
  const [delTemModalShow, setDelTemModalShow] = useState<boolean>(false);
  const [forms] = Form.useForm<PageParams>();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = pageDataColumnsHandler();
  useEffect(() => {
    getPageData();
  }, []);
  //分页数据Columns组合处理
  function pageDataColumnsHandler(): ColumnsType<ColumnsOptions> {
    if (tableList.length > 0) {
      const fields = Object.keys(tableList[0]).filter(
        (item) => !['id', '计划周期', 'submission_id'].includes(item),
      );
      const list: ColumnsType<ColumnsOptions> = fields.map((item) => {
        return {
          title: item,
          dataIndex: item,
          align: 'center',
          ellipsis: true,
        };
      });
      list.push({
        title: '操作',
        render: (text: string, record: any) => controllerComponet(record),
        align: 'center',
        ellipsis: true,
        width: 200,
      });
      return list;
    } else {
      return [];
    }
  }
  //获取分页数据
  function getPageData(): void {
    setLoading(true);
    listPage(params)
      .then((res) => {
        if (res.code === '1') {
          setTableList(res.data.list || []);
          setTotal(Number(res.data.total));
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //删除表格数据方法
  function delTableDataFn(): Promise<any> {
    if (curTableData) {
      return planReportDel(curTableData.submission_id);
    } else {
      return Promise.resolve({});
    }
  }
  //表格操作栏渲染
  function controllerComponet(record: TableOptions): ReactNode {
    const look = (
      <Tooltip title="查看">
        <EyeOutlined onClick={() => toLook(record)} className={styles.icon} />
      </Tooltip>
    );
    const edit = (
      <Tooltip title="编辑">
        <EditOutlined onClick={() => toAdd(record)} className={styles.icon} />
      </Tooltip>
    );
    const del = (
      <Tooltip title="删除">
        <DeleteOutlined onClick={() => openDelModal(record)} className={styles.icon} />
      </Tooltip>
    );
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          {look}
          {edit}
          {del}
        </Space>
      </div>
    );
  }
  //打开删除弹框
  function openDelModal(data: TableOptions): void {
    setCurTableData(data);
    setDelTemModalShow(true);
  }
  //上部操作按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button onClick={() => toAdd()}>+新增</type-button>
      </div>
    );
  }
  //跳转查看
  function toLook(data: TableOptions): void {
    history.push('/reportQuarterPlanLook/' + data.submission_id);
  }
  //跳转新增
  function toAdd(data?: TableOptions): void {
    if (data) {
      history.push('/reportQuarterPlanAdd/' + data.submission_id);
    } else {
      history.push('/reportQuarterPlanAdd/add');
    }
  }
  //查询
  function query(): void {
    const { startDate, value } = forms.getFieldsValue();
    let date: any = startDate;
    params = {
      ...params,
      pageNum: 1,
      value,
      ...(date
        ? {
            startDate: moment(date[0].$d).format('YYYY-MM-DD'),
            endDate: moment(date[1].$d).format('YYYY-MM-DD'),
          }
        : {}),
    };
    getPageData();
  }
  //重置
  function reset(): void {
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    getPageData();
  }
  //页码改变
  function pageChange(page: number, pageSize: number): void {
    params.pageNum = page;
    params.pageSize = pageSize;
    getPageData();
  }
  //搜索表单模板
  function searchFormComponent(): ReactNode {
    const btnStyle = 'border-radius:2px;box-shadow:none';
    return (
      <Form className="report-form" form={forms} layout="inline">
        <Form.Item<PageParams> label="名称" name="value">
          <Input style={inputW} placeholder="请输入名称..."></Input>
        </Form.Item>
        <Form.Item<PageParams> label="填报日期" name="startDate">
          <RangePicker style={inputW} />
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={query} styles={btnStyle}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={reset} styles={btnStyle}>
            重置
          </type-button>
        </Form.Item>
      </Form>
    );
  }
  return (
    <div className={styles['page-container']}>
      <section className={styles['top-search-container']}>
        {controllerComponent()}
        {searchFormComponent()}
      </section>
      <section>
        <TableComponent
          params={{
            pageSize: params.pageSize,
            pageChange,
            total,
            current: params.pageNum,
          }}
          loading={loading}
          columns={columns}
          tableList={tableList}
        ></TableComponent>
      </section>
      {/* 删除弹框 */}
      <DelModal
        onOk={getPageData}
        delFn={delTableDataFn}
        open={delTemModalShow}
        close={() => setDelTemModalShow(false)}
      ></DelModal>
    </div>
  );
};

export default ReportQuarterPlan;
