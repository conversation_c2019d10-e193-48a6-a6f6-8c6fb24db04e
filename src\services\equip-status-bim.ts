import { request, jsonHeader, formHeader } from '@/utils/request';
import { ResponseOptions } from './monitoring-point-annotation';
import { PointParams } from '@/pages/equip-status-bim/components/center-equip-info/center-equip-info';

//根据点位id获取最新数据
export function pointDataList(data: PointParams): Promise<ResponseOptions<any[]>> {
  return request.post('/nr/data/getByKeyList', { data: JSON.stringify(data), headers: jsonHeader });
}
//点位插入数据
export function pointInsertData(data: any): Promise<ResponseOptions<any[]>> {
  return request.post('/nr/data/insertFlux ', { data: JSON.stringify(data), headers: jsonHeader });
}
//获取机组状态数据
export function getDeviceStatus(data: PointParams): Promise<ResponseOptions<any[]>> {
  return request.post('/nr/data/getStateByKeyList ', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//获取列表历史数据
export function getPointHistoryData(data: PointParams): Promise<ResponseOptions<any>> {
  return request.post('/nr/data/getHistoryGroupByKey ', {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
//模型树
export async function getNodeTree(params: object): Promise<any> {
  return request.get(`/node/tree`, { params });
}
