p {
  margin: 0;
  padding: 0;
}

.top-statistics {
  display: flex;
  padding-left: 15%;
  column-gap: 10%;
}

.statistics-item {
  display: flex;
  align-items: center;
  column-gap: 15px;

  &-info {
    display: flex;
    flex-direction: column;
    row-gap: 5px;

    &-title {
      color: #fff;
      font-size: 16px;
      text-shadow: 0 0 3px #fff;
    }

    &-unit {
      font-size: 16px;
      margin-left: 10px;
    }

    &-value {
      font-family: DingTalk;
      font-size: 20px;
    }

    &-digital {
      font-family: Digital;
      font-size: 24px;
      display: inline-block;
      height: 31.5px;
      letter-spacing: 5px;
    }
  }

  &-timeIcon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(~@/assets/images/leakDetection/icon_time.png);
  }

  &-waterIcon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(~@/assets/images/leakDetection/icon_waterlevel.png);
  }

  &-changeIcon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(~@/assets/images/leakDetection/icon_duration.png);
  }
}
