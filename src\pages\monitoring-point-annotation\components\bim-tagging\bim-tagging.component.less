.bim-taggin-container {
  flex: 1;
  position: relative;
  &-empty {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, 0);
    &-text {
      font-size: 16px;
    }
  }
  &-tips {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translate(-50%, 0);
    padding: 10px 48px;
    font-size: 13px;
    color: #fff;
    background: rgba(3, 19, 64, 0.5);
    border-radius: 2px;
    z-index: 1;
  }

  &-controller {
    position: absolute;
    width: 100%;
    top: 15px;
    right: 30px;
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    z-index: 1;
  }

  &-bim {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    &-img {
      user-select: none;
      margin-top: 30px;
      cursor: default;
      position: relative;
    }
    &-tagg {
      cursor: pointer;
    }
    &-point {
      position: absolute;
    }
  }
}
