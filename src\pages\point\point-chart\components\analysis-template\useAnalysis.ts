import { useMemo } from 'react';
import type { ITemplates } from '@/pages/point/point-chart/point-analysis.page';
import _ from 'lodash';
export const useAnalysis = (layout: 1 | 2 | 3, points: ITemplates) => {
  // 不通布局样式
  const layoutConfig = useMemo(
    () => ({
      1: { height: 600, titleFontSize: 20 },
      2: { height: 400, titleFontSize: 16 },
      3: { height: 300, titleFontSize: 14 },
    }),
    [],
  );
  const chartOptions = useMemo(() => {
    switch (layout) {
      case 1:
        return {
          appendPadding: [20, 35, 20, 0],
        };
      case 2:
        return {
          legend: false, // 隐藏图例
          appendPadding: [10, 0, 10, 0],
        };
      case 3:
        return {
          slider: false, // 隐藏缩略轴
          tooltip: false, // 隐藏tooltip
          legend: false, // 隐藏图例
        };
    }
  }, [layout]);

  // 折线图数据
  const chartData = useMemo(() => {
    return (
      points?.basePointDTOS
        ?.map((point) => {
          return (
            point.historyDatas?.map((h) => ({
              type: point.pointName,
              time: h.time,
              value: h.value,
            })) || []
          );
        })
        .flat() || []
    );
  }, [points]);

  // 是否是当前布局
  const isLayout = (layoutType: 1 | 2 | 3) =>
    useMemo(() => _.isEqual(layout, layoutType), [layout, layoutType]);

  return {
    layoutConfig,
    chartOptions,
    chartData,
    isLayout,
  };
};
