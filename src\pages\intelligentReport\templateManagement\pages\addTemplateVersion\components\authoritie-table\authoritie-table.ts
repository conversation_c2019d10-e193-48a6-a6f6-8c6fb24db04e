import {
  AuthoritieOptions,
  CellControllerOptions,
  DataTypeEnum,
} from '@/components/spread-table/spread-table';
import { UseImperativeOptions as TableUseImperativeOptions } from '@/components/spread-table/spread-table';
export interface PropOptions {
  allTableList: AuthoritieOptions[]; //全部列表
  tableList: AuthoritieOptions[]; //当前tab列表
  tableListChange: React.Dispatch<React.SetStateAction<AuthoritieOptions[]>>;
  isEdit: boolean;
  spreadRef: React.MutableRefObject<TableUseImperativeOptions>;
  ref: any;
  inputFocusFn: (val: CellControllerOptions, type: DataTypeEnum.AUTHORITIE) => void; //输入框聚焦
}
export interface UseImperativeOptions {
  verifyPermis: (data: AuthoritieOptions, list: AuthoritieOptions[]) => boolean;
  cellBackHandler: (data: AuthoritieOptions, status?: 0 | 1) => void;
  cellBackBatchHandler: (list: AuthoritieOptions[], status?: 0 | 1) => void;
  addPermisRange: () => void;
  tablePermisEvent: () => void;
}
