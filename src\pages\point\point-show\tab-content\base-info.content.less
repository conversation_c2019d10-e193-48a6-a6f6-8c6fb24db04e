.descriptionsPanel {
  :global {
    .ant-descriptions-item-label {
      width: 140px;
      padding-left: 24px;
      color: #d4ebfd !important;

      &::after {
        content: '';
      }
    }
    .ant-descriptions-item-content {
      color: #fff !important;
    }
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      min-height: auto;
      font-size: 13px;
      line-height: 40px;
    }

    .ant-descriptions-row > th,
    .ant-descriptions-row > td {
      padding-bottom: 0;
    }
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      border-inline-end: 1px solid rgba(65, 174, 251, 0.3) !important;
    }
    .ant-descriptions-view .ant-descriptions-row {
      border-bottom: 1px solid rgba(65, 174, 251, 0.3) !important;
    }
    .ant-descriptions-view {
      border: 1px solid rgba(65, 174, 251, 0.3) !important;
    }
  }
}
