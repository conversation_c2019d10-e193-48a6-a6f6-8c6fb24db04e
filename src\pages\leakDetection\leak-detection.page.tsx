import { ButtonModal } from './components/button-modal/button-modal.component';
import { CenterBim } from './components/center-bim/center-bim.component';
import { ChartStatistics } from './components/chart-statistics/chart-statistics.component';
import { TopStatistics } from './components/top-statistics/top-statistics.component';
import styles from './leak-detection.page.less';

const LeakDetectionPage = () => {
  return (
    <section className={styles['leak-detection-page']}>
      {/* 顶部统计数字 */}
      <TopStatistics></TopStatistics>
      {/* bim模型与参数 */}
      <CenterBim></CenterBim>
      {/* 按钮弹框 */}
      <ButtonModal></ButtonModal>
      {/* 统计图 */}
      <ChartStatistics></ChartStatistics>
    </section>
  );
};

export default LeakDetectionPage;
