export default [
  {
    path: '/',
    redirect: '/equipStatusOverview',
  },
  /** 设备状态1 */
  {
    path: '/equipStatusOverview', // 状态一览1
    component: '@/pages/equip-status-overview/equip-status-overview.page',
  },
  /** 设备状态2 */
  {
    path: '/deviceStatusBim', // 状态一览2
    component: '@/pages/equip-status-bim/equip-status-bim.page',
  },
  /** 主变压器状态 */
  {
    path: '/mainTransformerStatus',
    component: '@/pages/main-transformer-status/main-transformer-status.page',
  },
  // 渗漏排水监测
  {
    path: '/leakDetection',
    component: '@/pages/leakDetection/leak-detection.page',
  },
  //蜗壳检修排水监测
  {
    path: '/voluteMaintenanceDrainage',
    component: '@/pages/volute-maintenance-drainage/volute-maintenance-drainage.page',
  },
  /** 监测点标注 */
  {
    path: '/monitoringPointAnnotation', // 监测点标注
    component: '@/pages/monitoring-point-annotation/monitoring-point-annotation.page',
  },
  /** 运行监视 */
  {
    path: '/monitor',
    component: '@/pages/monitor/monitor.page',
  },

  /** 测点列表 */
  {
    path: '/pointList',
    component: '@/pages/point/point-list/point-list.page',
  },

  /** 测点详情 */
  {
    path: '/pointShow',
    component: '@/pages/point/point-show/point-show.page',
  },

  /** 测点分析 */
  {
    path: '/pointChart',
    component: '@/pages/point/point-chart/point-chart.page',
  },
  /** 测点分析详情（新建图例） */
  {
    path: '/pointAnalysis',
    component: '@/pages/point/point-chart/point-analysis.index.tsx',
  },
  // 智能报表-月报分页
  {
    path: '/monthlyProductionReport/:code',
    component:
      '@/pages/intelligentReport/monthlyProductionReport/monthly-production-report.page.tsx',
  },
  // 智能报表月报新增
  {
    path: '/monthlyProductionReportAdd/:code',
    component:
      '@/pages/intelligentReport/monthlyProductionReport/pages/report-add/report-add.page.tsx',
  },
  // 智能报表-月报编辑-查看
  {
    path: '/monthlyProductionReportEdit/:id',
    component:
      '@/pages/intelligentReport/monthlyProductionReport/pages/report-edit/report-edit.page.tsx',
  },
  // 智能报表-月报审核
  {
    path: '/monthlyProductionReportExamine/:id',
    component:
      '@/pages/intelligentReport/monthlyProductionReport/pages/report-examine/report-examine.page.tsx',
  },
  // 报表月计划
  {
    path: '/reportPlan',
    component: '@/pages/intelligentReport/reportPlan/report-plan.page.tsx',
  },
  // 报表月计划-新增
  {
    path: '/reportPlanAdd/:id',
    component: '@/pages/intelligentReport/reportPlan/pages/report-add/report-add.page.tsx',
  },
  // 报表月计划-查看
  {
    path: '/reportPlanLook/:id',
    component: '@/pages/intelligentReport/reportPlan/pages/report-look/report-look.page.tsx',
  },
  // 报表季度计划
  {
    path: '/reportQuarterPlan',
    component: '@/pages/intelligentReport/reportQuarterPlan/report-quarter-plan.page.tsx',
  },
  // 报表季度计划-新增
  {
    path: '/reportQuarterPlanAdd/:id',
    component: '@/pages/intelligentReport/reportQuarterPlan/pages/report-add/report-add.page.tsx',
  },
  // 报表季度计划-查看
  {
    path: '/reportQuarterPlanLook/:id',
    component: '@/pages/intelligentReport/reportQuarterPlan/pages/report-look/report-look.page.tsx',
  },
  // 报表年计划
  {
    path: '/reportYearPlan',
    component: '@/pages/intelligentReport/reportYearPlan/report-year-plan.page.tsx',
  },
  // 报表年计划-新增
  {
    path: '/reportYearPlanAdd/:id',
    component: '@/pages/intelligentReport/reportYearPlan/pages/report-add/report-add.page.tsx',
  },
  // 报表年计划-查看
  {
    path: '/reportYearPlanLook/:id',
    component: '@/pages/intelligentReport/reportYearPlan/pages/report-look/report-look.page.tsx',
  },
  // 模板管理
  {
    path: '/templateManagement',
    component: '@/pages/intelligentReport/templateManagement/template-management.page.tsx',
  },
  // 模板管理-信息
  {
    path: '/templateManagementInfo/:id',
    component:
      '@/pages/intelligentReport/templateManagement/pages/templateInfo/template-info.page.tsx',
  },
  // 模板管理-版本新增/编辑/查看
  {
    path: '/addTemplateVersion/:id/:versionId',
    component:
      '@/pages/intelligentReport/templateManagement/pages/addTemplateVersion/add-template-version.page.tsx',
  },
  // 电量计算
  {
    path: '/electricityCalculation',
    component: '@/pages/electricity-calculation/electricity-calculation.page.tsx',
  },
  // —————————————————————————————————————————————— 值班管理
  // 交接班
  {
    path: '/duty-manage/changeShifts',
    component: '@/pages/duty-manage/pages/change-shifts/change-shifts.page',
  },
  // 值班日志
  {
    path: '/duty-manage/dutyLog',
    component: '@/pages/duty-manage/pages/duty-log/duty-log.page',
  },
  // 值班日历
  {
    path: '/duty-manage/dutyCalendar',
    component: '@/pages/duty-manage/pages/duty-calendar/duty-calendar.page',
  },
  // 设备工况模板
  {
    path: '/duty-manage/equipTem',
    component: '@/pages/duty-manage/pages/equip-tem/equip-tem.page',
  },
  // 接地刀闸配置
  {
    path: '/duty-manage/groundConfig',
    component: '@/pages/duty-manage/pages/ground-config/ground-config.page',
  },
  /** NOT FOUND */
  {
    component: '@/pages/404.page.tsx',
  },
];
