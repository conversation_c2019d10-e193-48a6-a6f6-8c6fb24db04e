import type { Effects, Reducers } from '@/global';
import type { RecordType } from '../c-cpns/detail-table/detail-table.component';

export interface IRootState {
  electricityCalculation: IState;
}

export interface IState {
  record: RecordType;
}

const initalState: IState = {
  record: null,
};

const effects: Effects = {};

const reducers: Reducers = {
  changeRecord(state: IState, { payload }) {
    return { ...state, record: payload };
  },
};

export default {
  state: initalState,
  effects,
  reducers,
};
