import { ConfigProvider, Modal } from 'antd';
import React, { Component } from 'react';
import type { DraggableData, DraggableEvent } from 'react-draggable';
import Draggable from 'react-draggable';

import styles from './drag-modal.component.less';
import type { ModalProps } from 'antd/lib/modal';

interface State {
  disabled: boolean;
  bounds: { left: number; top: number; bottom: number; right: number };
}

export class DragModal extends Component<ModalProps, State> {
  private draggleRef = React.createRef<HTMLDivElement>();

  constructor(props: ModalProps) {
    super(props);
    this.state = {
      disabled: true,
      bounds: { left: 0, top: 0, bottom: 0, right: 0 },
    };
  }

  onStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = this.draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    this.setState({
      bounds: {
        left: -targetRect.left + uiData.x,
        right: clientWidth - (targetRect.right - uiData.x),
        top: -targetRect.top + uiData.y,
        bottom: clientHeight - (targetRect.bottom - uiData.y),
      },
    });
  };

  render() {
    const { disabled, bounds } = this.state;
    const { title = '' } = this.props;

    return (
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#0ae4ff',
            colorBgBase: '#091f4c',
            colorTextBase: '#fff',
            colorBorder: '#0A88AF',
            colorIcon: '#051B46',
            boxShadow:
              '0 0px 10px 3px rgba(10, 136, 175, 0.8), inset 0 0px 10px 2px rgba(10, 136, 175, 0.8)',
            paddingContentHorizontalLG: 10,
            paddingMD: 10,
          },
          components: {
            Modal: {
              /* here is your component tokens */
              titleLineHeight: 1,
              titleFontSize: 15,
              titleColor: '#fff',
              headerBg: '#162B4B',
              footerBg: '#051b46',
              contentBg: '#051b46',
            },
          },
        }}
      >
        <Modal
          {...this.props}
          className={styles.modal}
          title={
            <div
              style={{ width: '100%', cursor: 'move', color: '#fff' }}
              onMouseOver={() => {
                if (disabled) {
                  this.setState({ disabled: false });
                }
              }}
              onMouseOut={() => {
                this.setState({ disabled: true });
              }}
            >
              {title}
            </div>
          }
          // mask={false}
          modalRender={(modal) => (
            <Draggable
              disabled={disabled}
              bounds={bounds}
              onStart={(event, uiData) => this.onStart(event, uiData)}
            >
              <div ref={this.draggleRef}>{modal}</div>
            </Draggable>
          )}
        />
      </ConfigProvider>
    );
  }
}
