import { Button, DatePicker, Form, Input, Select, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs'; // antd 5.0 自带
import type { ReactNode } from 'react';
import { forwardRef, memo, useImperativeHandle } from 'react';
import { ElementType, EnumElement, ISearchFormConfig, IBaseFormExp } from './type';

interface IProps {
  children?: ReactNode;
  config: ISearchFormConfig;
}

const FormSearch = forwardRef<IBaseFormExp, IProps>((props, ref) => {
  const {
    config: {
      elements: els, // 元素
      layout, // 表单布局 horizontal | vertical | inline
      initialValues, // 设置子元素默认值
      formPorps, // 表单属性
    },
  } = props;
  const [form] = useForm<any>();
  // 暴露
  useImperativeHandle<IBaseFormExp, IBaseFormExp>(
    ref,
    () => ({
      baseFormInstance: form, // 暴露 form 实例
    }),
    [],
  );

  return (
    <Form
      form={form}
      layout={layout ?? 'inline'}
      initialValues={initialValues}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      {...formPorps}
    >
      <Space>
        {els.map((el: ElementType) => {
          const {
            key, // diff key
            type, // 组件种类
            label, // item 文字
            formItemProps, // 组件属性
          } = el;
          switch (type) {
            case EnumElement.INPUT:
              return (
                <Form.Item key={key} label={label} name={el.field} rules={el.rules} {...formItemProps}>
                  <Input
                    placeholder="请输入..."
                    disabled={el.disabled}
                    onBlur={el.onBlur}
                    onChange={el.onChange}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EnumElement.BUTTON:
              return (
                <Form.Item key={key} noStyle={el.noStyle ?? true} {...formItemProps}>
                  <Button type="primary" loading={el.loading} disabled={el.disabled} onClick={el.onClick} {...el.props}>
                    {label}
                  </Button>
                </Form.Item>
              );
            case EnumElement.SELECT:
              return (
                <Form.Item key={key} label={label} name={el.field} rules={el.rules} {...formItemProps}>
                  <Select
                    placeholder="请选择..."
                    options={el.options} // { label, value }[]
                    allowClear={el.allowClear} // 自定义清除按钮
                    disabled={el.disabled} // 禁用
                    onChange={el.onChange} // 选项变更回调事件
                    {...el.props}
                  />
                </Form.Item>
              );
            case EnumElement.RANGEPICKER:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  normalize={(values: [Dayjs, Dayjs] | null) => {
                    if (!values) return values;
                    const [startTime, endTime] = values;
                    return [
                      dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
                      dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
                    ];
                  }}
                  getValueProps={(value: any) => {
                    if (typeof value === 'string') return { value: dayjs(value, 'YYYY-MM-DD HH:mm:ss') };
                    return {};
                  }}
                  {...formItemProps}
                >
                  <DatePicker.RangePicker
                    placeholder={['请选择...', '请选择...']}
                    allowClear={el.allowClear}
                    {...el.props}
                  />
                </Form.Item>
              );
            default:
              return <></>;
          }
        })}
      </Space>
    </Form>
  );
});

export default memo(FormSearch);
