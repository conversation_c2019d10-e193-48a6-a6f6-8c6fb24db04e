/**
 * <AUTHOR>
 * @description 并发请求函数封装
 * @param {Array<Promise>} apis 请求函数集
 * @param {number} maxNum 最大并发量
 * @returns {Promise<any>}
 */

export default function concurrency(apis: Promise<any>[], maxNum: number) {
  return new Promise((resolve) => {
    if (apis.length === 0) {
      resolve([]);
      return;
    }
    const results: any[] = []; // 所有返回结果
    let index = 0; // 当前请求索引
    let count = 0; // 当前完成请求总数
    // 异步请求函数
    async function request() {
      // 请求数超过
      if (index === apis.length) {
        return;
      }
      const i = index; // 原始的下标
      const api = apis[index]; // 对应下标请求地址
      index++; // 下一个请求的下标
      try {
        const resp = await api;
        results[i] = resp.data; // 对应下标保存对应返回结果
      } catch (err) {
        results[i] = err; // 错误
      } finally {
        count++; // 每完成一个请求量+1
        // 所有请求完毕
        if (count === apis.length) {
          console.log('over');
          resolve(results);
        }
        request(); // 无论成功或错误，都请求下一个请求
      }
    }
    // 并发量控制 - 并发量不能超过总请求数
    const times = Math.min(maxNum, apis.length);
    for (let x = 0; x < times; x++) {
      request();
    }
  });
}
