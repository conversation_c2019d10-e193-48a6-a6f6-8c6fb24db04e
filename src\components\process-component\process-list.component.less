@line-left: 2px;
@line-top: 2px;
@circle-diameter: 14px;
/* 圆的直径 */
@circle-border-width: 2px;
/* 圆的边宽 */
@line-width: 2px;
/* 连接线的宽度 */
@line-color: #01b7ff;
/* 线条颜色 */

.list {
  height: calc(100% - 55px);
  overflow: auto;
  scrollbar-width: thin;

  .item {
    line-height: 20px;
    position: relative;
    padding-left: 35px;
    padding-bottom: 15px;
    padding-top: 2px;

    &::before {
      content: '圆圈';
      font-size: 0;
      display: block;
      box-sizing: content-box;
      position: absolute;
      top: @line-top;
      transform: scale(1.1);
      left: @line-left;
      width: @circle-diameter;
      height: @circle-diameter;
      border: @circle-border-width solid @line-color;
      border-radius: 50%;
      z-index: 2;
    }

    &::after {
      content: '连接线';
      font-size: 0;
      box-sizing: content-box;
      display: block;
      width: 0;
      height: calc(100% - @circle-diameter - @line-width * 2);
      border-left: @line-width solid @line-color;
      position: absolute;
      top: calc(@circle-diameter + @circle-border-width * 2 + @line-top);
      left: calc(@circle-diameter / 2 + @circle-border-width + @line-left);
      z-index: 2;
      transform: translateX(-70%);
    }

    &:last-child::after {
      display: none;
    }

    .baseInfo {
      margin-bottom: 10px;
      font-size: 14px;

      .username {
        color: #01b7ff;
        margin-right: 5px;
      }

      .time {
        font-size: 13px;
        margin-left: 5px;
        margin-right: 15px;
      }
    }

    .content {
      background-color: rgba(105, 105, 105, 0.2);
      padding: 10px 15px;
      margin-right: 15px;
      font-size: 13px;
      line-height: 25px;
    }
  }
}

.taskUserName {
  font-size: 14px;
  padding: 5px 6px;
  border: 1px solid #0d6ee4;
  border-radius: 5px;
  max-height: 56px;
  overflow-y: scroll;
  color: #01b7ff;

  span {
    color: #fff;
  }
}
