# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist
/micro_operations

# misc
.DS_Store
npm-debug.log*
yarn-error.log
/coverage
.idea
*bak
.svn

# visual studio code
.history
*.log
*.zip
functions/*
.temp/**

# umi
.umi
.umi-production

# screenshot
screenshot
.firebase
.eslintcache

build
.svn/wc.db
node_modules
