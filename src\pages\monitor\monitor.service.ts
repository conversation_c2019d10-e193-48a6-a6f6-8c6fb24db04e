import { request, formHeader, json<PERSON>eader } from '@/utils/request';
import { parseParamFromData } from '@/utils/utils';

export async function getSfShowPointList(params: any): Promise<any> {
  return request.get(`/SfShowPoint/list`, { params });
}
export async function getHistory(params: any): Promise<any> {
  return request.get(`/nr/data/getHistory`, { params });
}
// 对比的历史数据
export async function getListHistoryDataOld(params: any): Promise<any> {
  return request.get(`/nr/data/getListHistoryData`, { params });
}
export async function getListHistoryData(data: any): Promise<any> {
  return request.post(`/nr/data/getListHistoryDataByPoint`, {
    data: JSON.stringify(data),
    headers: jsonHeader,
  });
}
// 点位状态
export async function getStateStatus(): Promise<any> {
  return request.get(`/nr/data/getStateStatus`);
}
// 对比点位列表
export async function getComparePoint(params: any): Promise<any> {
  return request.get(`/SfShowPoint/getComparePoint`, { params });
}
export async function getSelectFlux(params: any): Promise<any> {
  return request.post(`/nr/data/selectFlux`, { params });
}
export async function getHeadshow(): Promise<any> {
  return request.post(`/nr/data/getHeadShow`);
}
