### **三维配置功能**

##### **一、****三维场景配置**

###### **1.1、****视角预设管理**

支持创建自定义视角预设（如巡检视角、全局视角、设备特写视角），每个预设包含相机位置、焦距、视角范围等参数

环境光：支持 RGB 颜色调节，亮度 0-100% 无级调节

点光源 / 平行光：可在三维场景中添加动态光源，设置光源位置、照射范围、颜色温度

模型材质编辑：支持金属度、粗糙度、透明度等 PBR 材质参数调节，重要设备可配置高光显示

 

不同场景模型构建/id加载

 

###### **1.2****、标点配置功能细化**

**1.2.1****标点基础属性配置**

1）形状配置

内置形状库：提供基础几何形状（圆形、方形、三角形）、设备图标（泵阀、传感器、摄像头）、自定义图标（支持 svg/png 导入）

形状参数调节：圆形 / 方形可设置边长 / 半径，支持旋转角度调整（0-360°，步进 1°），自定义图标可设置锚点位置（中心点 / 左上角等 5 种预设）

2）位置配置

三维场景：x/y/z 坐标

构件坐标：通过和模型构件关联

坐标输入方式：可视化拖拽定位 + 精确坐标输入

3）视觉样式配置

颜色配置：主色 / 警示色双色系，正常状态显示主色（支持色值输入 / 调色板选择），报警状态自动切换警示色（可配置渐变过渡效果）

字体配置：支持字体类型、字号、字重（100-900）、文字阴影等效果配置

 

**1.2.2****数据连接配置**

1）websocket 连接配置

连接参数：ws/wss 协议选择，服务器地址、端口、心跳间隔（5-600 秒），支持自定义握手参数（如认证 token）

消息解析：支持 JSON/Protobuf 格式，可配置数据映射规则（如 websocket 消息中的 "value" 字段映射到标点的实时数据值）

重连机制：支持自动重连（可配置重试次数、间隔时间），连接异常时标点显示离线状态图标

2）restful 接口轮询配置

轮询策略：固定间隔轮询（1-3600 秒）/ 基于事件触发轮询（如模型视角切换时触发数据更新）

请求配置：支持 GET/POST 方法，自定义请求头、URL 参数、请求体（支持变量替换，如 {modelId} 自动替换为当前模型构建 ID）

响应处理：可配置数据提取规则，支持设置超时时间（1-30 秒），失败重试次数（0-5 次）3）数据动画绑定

配置预设动画，通过数据触发相关动画，达到如报警闪烁，机组转动等动画效果。

#### **二****、业务配置**

###### **2.1****模型 - 业务绑定**

1.业务模块属性

基础信息：业务名称（唯一索引）、业务编码（遵循企业统一编码规范）、所属模型构建 ID

路由配置：可配置业务专属访问路径（如 /api/model/{modelId}/business/{bizCode}），支持 RESTful 接口权限控制（按业务模块划分 API 访问权限）

 

#### 三、**动画管理（可后期扩展业务）**

**3.1动画库**

1）基础动画：支持标点进行水平平移、垂直移动、自由路径移动，路径可自行绘制或和模型构件绑定。支持标点进行旋转、缩放、透明度渐变，支持线性、缓入缓出等

2）业务类动画：

报警动画：包含闪烁、脉冲、颜色渐变等效果。支持颜色、频率、脉冲幅度、颜色渐变时长参数配置。

设备运行动画：针对机组、风机等设备，预设转动、开合、伸缩等动画。转动设备可设置转速、转向等，开合设备支持开合比列等。

数据流动画：根据绑定的数据流，进行颜色、大小等变化，变化幅度和变化量成比例，如温度升高逐渐变红。

 

**3.2 动画编辑**

 

\1. 可视化编辑工具：用户通过拖拽、设置关键帧等方式组合基础动画，调整动画顺序、播放时长、过渡效果。支持对动画片段的复制、粘贴、编辑等操作。

\2. 标签与分组：支持将预设动画按业务场景、动画类型等维度进行分组管理，并添加自定义标签，便于快速检索与调用。例如，将所有报警相关动画归为 “报警动画组”，添加 “警示”“紧急” 等标签。

 

**3.3数据与动画绑定规则**

1.触发规则

1）状态变位触发：针对遥信类型数据，当状态发生改变时触发相应的动画，如机组启动时候触发转动动画。

2）阈值触发：针对遥测数值型数据，设置多个阈值区间，每个区间对应不同的动画效果。例如，当温度数据处于 [0 - 30]℃时，标点保持静止；处于 (30 - 50]℃时，标点开始轻微闪烁；超过 50℃时，标点快速闪烁并改变颜色。

3）时间周期触发：按照固定的时间周期频率重复播放动画，用于周期性的展示数据状态。

4）用户触发：在三维场景中，通过鼠标点击、拖拽等操作触发特定动画，方便用户主动查看某些数据。

 

2.动态绑定

1）数据驱动：动画参数（如转动速度、闪烁频率）和数据源中数据进行映射，如将风机转速速度和实际实际转速绑定，转速越高，动画转动越快

2）自定义表达式计算：通过自定义表达式计算后驱动动画

####  

#### **四、****预设使用场景******

三维场景实用功能

路径规划功能

支持在三维场景中绘制巡检路径，标点按路径顺序高亮显示，可配置自动巡检动画（播放速度 1-10 倍速）

实时数据标注

标点可实时显示关联数据源的数值（如温度 23.5℃），支持单位自动转换数据显示样式：正常范围显示黑色字体，预警范围显示橙色，报警范围显示红色，数值变化时支持动态过渡效果

历史数据回溯

在三维场景中可选择时间轴，查看历史时刻的标点状态（如 24 小时前某设备的报警状态回顾）

 