export type CommonPointType = 'state' | 'analog' | '遥信' | '遥测';
// 测点单位值字典
export type PointUnitDictionary = {
  dictCode: string;
  dictName: string;
  dictOrder: number;
  dictValue: string;
  editDate: string | null;
  editUserId: string | null;
  id: string;
  isLeaf: boolean;
  isSys: null;
  isTree: null;
  memo: string;
  parentCode: string;
  rootId: string;
  children: Array<{
    children: any[];
    dictCode: string;
    dictName: string;
    dictOrder: number;
    dictValue: string;
    editDate: string | null;
    editUserId: string | null;
    id: string;
    isLeaf: boolean;
    isSys: null;
    isTree: null;
    memo: string;
    parentCode: string;
    rootId: string;
  }>;
};

// 测点列表
export namespace Pointlist {
  export interface Query {
    pointCode: number;
    pointName: number;
    pointType: CommonPointType;
    pageNum: number;
    pageSize: number;
    orderColumn: string;
    orderCondition: string;
  }
  export interface List {
    deviceCode: null | string;
    deviceId: null | string;
    entryTime: string;
    id: string;
    max: null | number;
    memo: null | string;
    min: null | number;
    parentId: null | string;
    path: string;
    pointCode: string;
    pointName: string;
    pointType: CommonPointType;
    sfCode: string;
    subscribe: null | string;
    switching: null | string;
    switchingValue: null | string;
    unit: null | string;
  }
  export interface Common {
    endRow: string;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    isFirstPage: boolean;
    isLastPage: boolean;
    list: Array<List>;
    navigateFirstPage: number;
    navigateLastPage: number;
    navigatePages: number;
    navigatepageNums: number[];
    nextPage: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    prePage: number;
    size: number;
    startRow: string;
    total: string;
  }
  export interface Details {
    entryTime: number;
    id: string;
    max: null | number;
    memo: string;
    min: null | number;
    parentId: null | string;
    path: string;
    pointCode: string;
    pointName: string;
    pointType: CommonPointType;
    sfCode: 'nrPoint';
    subscribe: null | string;
    switching: null | string;
    switchingValue: '0' | '1';
    unit: null | string;
    unitData: string;
    deviceCode: null | string;
    deviceId: null | string;
    deviceResumes: Array<{
      code: string;
      createTime: number;
      createUser: string | null;
      factory: string;
      field: string;
      fileIds: string | null;
      id: string;
      inspectionCycle: string;
      kks: string;
      kksLike: string | null;
      kksPathName: string | null;
      lastTimeInspectDate: string;
      loctionInstall: string;
      memo1: string | null;
      memo2: string;
      memo3: string | null;
      name: string;
      nameLike: string | null;
      nextTimeInspectDate: string | null;
      note: string;
      specification: string;
      type: string;
      updateTime: string | null;
      updateUser: string | null;
      useTime: number;
      years: number;
    }>;
  }
  // 测点值
  export interface PointValue {
    time: string;
    value: number;
  }

  // 测点绑定单位配置
  export interface PointUnitConfig {
    endRow: string;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    isFirstPage: boolean;
    isLastPage: boolean;
    navigateFirstPage: number;
    navigateLastPage: number;
    navigatePages: number;
    navigatepageNums: number[];
    nextPage: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    prePage: number;
    size: number;
    startRow: string;
    total: string;
    list: Array<{
      additionalField: null | string;
      attachmentId: null | string;
      auxiliaryField: null | string;
      backupField: null | string;
      businessId: null | string;
      createTime: null | string;
      deviceState: string;
      dummyField: null | string;
      extraField: null | string;
      id: string;
      incrementalField: null | string;
      modifyTime: null | string;
      path: null | string;
      pointCode: string;
      provisionalField: null | string;
      reservedField: null | string;
      spareField: null | string;
      supplementaryField: null | string;
      syncState: string;
      unitData: string;
      updateTime: null | string;
      version: null | string;
    }>;
  }
  // 同步数据
  export interface SyncData {
    endRow: string;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    isFirstPage: boolean;
    isLastPage: boolean;
    list: Array<{
      additionalPoint: string;
      createTime: string | null;
      diffTime: string;
      dummyField: string | null;
      extraField: string | null;
      fallbackField: string | null;
      id: string;
      incrementalField: string;
      modifyPoint: string;
      path: string | null;
      pointType: CommonPointType;
      removePoint: string;
      reservedField: string | null;
      spareField: string | null;
      version: string;
    }>;
    navigateFirstPage: number;
    navigateLastPage: number;
    navigatePages: number;
    navigatepageNums: number[];
    nextPage: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    prePage: number;
    size: number;
    startRow: string;
    total: string;
  }
  // 同步记录-历史明细
  export interface HistoricalDetails {
    endRow: string;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    isFirstPage: boolean;
    isLastPage: boolean;
    list: Array<{
      compareId: string;
      compilerState: '1' | '2' | '3';
      deviceCode: string | null;
      deviceId: string | null;
      entryTime: number;
      id: string;
      max: number | null;
      memo: string;
      min: number | null;
      parentId: string | null;
      path: string;
      pointCode: string;
      pointName: string;
      pointType: CommonPointType;
      sfCode: string;
      subscribe: string | null;
      switching: string | null;
      switchingValue: string | null;
      unit: string | null;
    }>;
    navigateFirstPage: number;
    navigateLastPage: number;
    navigatePages: number;
    navigatepageNums: number[];
    nextPage: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    prePage: number;
    size: number;
    startRow: string;
    total: string;
  }
}

// 测点分析列表
export namespace PointAnalysis {
  // 测点分析页查询
  export interface Query {
    pageNum: number;
    pageSize: number;
    name: string;
    id: string;
    myFavorites: number;
    myShare: number;
    shareWithMe: number;
    userId: string;
  }
  // 测点分析页列表
  export interface List {
    analyseData: null | string;
    analyseDataDTOList: null | Array<any>;
    createTime: string;
    createUser: string;
    createUserId: null | string;
    endTime: null | string;
    id: string;
    memo1: null | string;
    memo2: null | string;
    myFavorites: number;
    myShare: number;
    name: string;
    shareWithMe: number;
    startTime: null | string;
    thumbnailId: null | string;
    thumbnailContentList: Array<{
      businesstype: null | string;
      endTime: null | string;
      format: string;
      genre: string;
      id: string;
      ids: null | string;
      length: number;
      memo: string;
      name: string;
      startTime: null;
      thumbnail: string;
      type: string;
      uploadDate: string;
      uploadUserName: string;
      uploadUserRealName: string;
      url: string;
      userId: string;
    }>;
  }
  // 测点分析页分页查询
  export interface Common {
    endRow: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    isFirstPage: boolean;
    isLastPage: boolean;
    list: Array<List>;
    navigateFirstPage: number;
    navigateLastPage: number;
    navigatePages: number;
    navigatepageNums: number[];
    nextPage: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    prePage: number;
    size: number;
    startRow: number;
    total: number;
  }
  // 测点分析页详情页
  export interface Detail {
    analyseData: string;
    analyseDataDTOList: null;
    createTime: string;
    createUser: string;
    createUserId: string;
    endTime: string;
    id: string;
    memo1: null | string;
    memo2: null | string;
    myFavorites: null | number;
    myShare: null | number;
    name: string;
    shareWithMe: null | number;
    startTime: string;
    thumbnailId: string;
  }
  // 测点分析页详情页历史数据查询
  export interface HistoryQuery {
    basePointList: Array<Pointlist.List>;
    selectBegin: string;
    selectEnd: string;
  }
  // 测点分析页详情页历史数据
  export interface History {
    deviceCode: null | string;
    deviceId: null | string;
    dummyField: null | string;
    entryTime: number;
    historyDatas: Array<{
      time: string;
      value: number;
    }>;
    id: string;
    max: null | string;
    min: null | string;
    parentId: null | string;
    path: string;
    pointCode: string;
    pointName: string;
    pointType: CommonPointType;
    sfCode: string;
    subscribe: null | string;
    switching: null | string;
    switchingValue: null | string;
    unit: null | string;
  }
  export interface Add {
    name: string;
    thumbnailId: string;
    exceedsLimits?: string;
    lessThanLimits?: string;
  }
  export interface Update {
    id: string;
    name: string;
    analyseDataDTOList: Array<any>;
    startTime: string;
    endTime: string;
    thumbnailId?: string;
  }
  export interface Share {
    noticerIdList: string[];
    optionId: string;
  }
  export interface CancelShare {
    id: string;
  }
  export interface CancelShareWithMe {
    id: string;
  }
  export interface Collect {
    optionId: string;
    myFavorites: number;
  }
}
