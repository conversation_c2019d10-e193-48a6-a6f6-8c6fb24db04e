import { DatePicker, Form, Input, Space, Tooltip } from 'antd';
import styles from './template-management.page.less';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { AddTemModal } from './components/addTemplateModal/add-template-modal.component';
import { delTemplateBaseInfo, listPage } from '@/services/intelligentReport/template-management';
import {
  PageParams,
  TableOptions,
  params as paramsOptions,
  productUpdate,
} from './template-management';
import { DelModal } from '@/components/del-modal/del-modal.component';
import moment from 'moment';
import { EditTemModal } from './components/editTemplateModal/edit-template-modal.component';
import { ProductOptions, delPrivilegeProduct, privilegeProductList } from '@/services/system';
import { ResponseOptions } from '@/services/monitoring-point-annotation';
import { FormEditOptions } from './components/editTemplateModal/edit-template-modal';

const { RangePicker } = DatePicker;
const inputW = { width: '250px' };
let params: PageParams = JSON.parse(JSON.stringify(paramsOptions));
const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
const TemplateManagementPage = () => {
  //新增弹框控制
  const [addTemModalShow, setAddTemModalShow] = useState<boolean>(false);
  //编辑弹框控制
  const [editTemModalShow, setEditTemModalShow] = useState<boolean>(false);
  //删除弹框控制
  const [delTemModalShow, setDelTemModalShow] = useState<boolean>(false);
  //当前操作数据
  const [curTableData, setCurTableData] = useState<TableOptions>();
  //表格数据
  const [tableList, setTableList] = useState<TableOptions[]>([]);
  //当前模板菜单数据
  const [curTableMenuData, setCurTableMenuData] = useState<ProductOptions>();
  //父级菜单数据
  const [parentMenuData, setParentMenuData] = useState<ProductOptions>();
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [forms] = Form.useForm<PageParams>();
  //表格字段配置
  const columns: ColumnsType<ColumnsOptions> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '编码',
      dataIndex: 'code',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '最新修改日期',
      dataIndex: 'createTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      render: (text, record: any) => controllerComponet(record),
      align: 'center',
      ellipsis: true,
      width: 200,
    },
  ];
  useEffect(() => {
    getReportParentMenuData();
    getPageData();
    return () => {
      params = JSON.parse(JSON.stringify(paramsOptions));
    };
  }, []);
  //获取分页数据
  function getPageData(): void {
    setLoading(true);
    listPage<TableOptions>(params)
      .then((res) => {
        if (res.code === '1') {
          setTableList(res.data.list);
          setTotal(Number(res.data.total));
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //刷新路由页面
  function refreshPage(data?: FormEditOptions): void {
    if (data && curTableData && data.name !== curTableData.name) {
      productUpdate();
    } else if (data == undefined) {
      productUpdate();
    }
    getPageData();
  }
  //删除表格数据方法
  function delTableDataFn(): Promise<ResponseOptions<any>> {
    if (curTableData) {
      return new Promise((reslove, reject) => {
        const list = [
          ...(curTableMenuData ? [delPrivilegeProduct(curTableMenuData.id || '')] : []), //删除菜单
          delTemplateBaseInfo(curTableData.id), //删除模板
        ];
        Promise.allSettled(list)
          .then((res) => {
            let fulList = res.filter(
              (item) => item.status === 'fulfilled' && item.value.code === '1',
            );
            if (fulList.length === list.length) {
              reslove({ code: '1', data: '', message: '' });
            } else {
              reject();
            }
          })
          .catch((err) => {
            reject();
          });
      });
    } else {
      return Promise.resolve({ code: '1', data: '', message: '' });
    }
  }
  //表格操作栏渲染
  function controllerComponet(record: TableOptions): ReactNode {
    const { createUserId } = record;
    const isAuthoritie = userId === createUserId || userId == 1;
    const look = (
      <Tooltip title="查看">
        <EyeOutlined onClick={() => toLook(record)} className={styles.icon} />
      </Tooltip>
    );
    const edit = (
      <Tooltip title="编辑">
        <EditOutlined onClick={() => openEditModal(record)} className={styles.icon} />
      </Tooltip>
    );
    const del = (
      <Tooltip title="删除">
        <DeleteOutlined onClick={() => openDelModal(record)} className={styles.icon} />
      </Tooltip>
    );
    return (
      <div className={styles['table-controller-container']}>
        <Space size="middle">
          {look}
          {isAuthoritie && edit}
          {isAuthoritie && del}
        </Space>
      </div>
    );
  }
  //获取报表父级菜单数据
  function getReportParentMenuData(): void {
    privilegeProductList('operations-report').then((res) => {
      if (res.code === '1') {
        ///筛选出pc菜单
        const list = res.data.filter((item) => item.idPath?.startsWith('0,1'));
        if (list.length > 0) {
          setParentMenuData(list[0]);
        } else {
          setParentMenuData(undefined);
        }
      }
    });
  }
  //获取模板菜单数据
  function getTemplateMenuData(data: TableOptions): void {
    privilegeProductList(data.type).then((res) => {
      if (res.code === '1') {
        ///筛选出pc菜单
        const list = res.data.filter((item) => item.idPath?.startsWith('0,1'));
        if (list.length > 0) {
          setCurTableMenuData(list[0]);
        } else {
          setCurTableMenuData(undefined);
        }
      }
    });
  }
  //打开删除弹框
  function openDelModal(data: TableOptions): void {
    getTemplateMenuData(data);
    setCurTableData(data);
    setDelTemModalShow(true);
  }
  //打开编辑弹框
  function openEditModal(data: TableOptions): void {
    getTemplateMenuData(data);
    setCurTableData(data);
    setEditTemModalShow(true);
  }
  //上部操作按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button onClick={openAddModal}>+新增</type-button>
      </div>
    );
  }
  //跳转查看
  function toLook(data: TableOptions): void {
    history.push('/templateManagementInfo/' + data.id);
  }
  //跳转新增
  function openAddModal(): void {
    setAddTemModalShow(true);
  }
  //查询
  function query(): void {
    const { name, startDate } = forms.getFieldsValue();
    if (startDate) {
      let date: any = startDate;
      params.startDate = moment(date[0].$d).format('YYYY-MM-DD HH:mm:ss');
      params.endDate = moment(date[1].$d).format('YYYY-MM-DD 23:59:59');
    }
    params.name = name;
    params.pageNum = 1;
    getPageData();
  }
  //重置
  function reset(): void {
    forms.resetFields();
    params = JSON.parse(JSON.stringify(paramsOptions));
    getPageData();
  }
  //页码改变
  function pageChange(page: number, pageSize: number): void {
    params.pageNum = page;
    params.pageSize = pageSize;
    getPageData();
  }
  //搜索表单模板
  function searchFormComponent(): ReactNode {
    const btnStyle = 'border-radius:2px;box-shadow:none';
    return (
      <Form className="report-form" form={forms} layout="inline">
        <Form.Item<PageParams> label="名称" name="name">
          <Input style={inputW} placeholder="请输入名称..."></Input>
        </Form.Item>
        <Form.Item<PageParams> label="修改日期" name="startDate">
          <RangePicker style={inputW} />
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={query} styles={btnStyle}>
            查询
          </type-button>
        </Form.Item>
        <Form.Item>
          <type-button loading={loading} onClick={reset} styles={btnStyle}>
            重置
          </type-button>
        </Form.Item>
      </Form>
    );
  }
  return (
    <div className={styles['page-container']}>
      <section className={styles['top-search-container']}>
        {controllerComponent()}
        {searchFormComponent()}
      </section>
      <section>
        <TableComponent
          params={{
            pageSize: params.pageSize,
            pageChange,
            total,
            current: params.pageNum,
          }}
          loading={loading}
          columns={columns}
          tableList={tableList}
        ></TableComponent>
      </section>
      {/* 删除弹框 */}
      <DelModal
        onOk={refreshPage}
        delFn={delTableDataFn}
        open={delTemModalShow}
        close={() => setDelTemModalShow(false)}
      ></DelModal>
      {/* 新增模板弹框 */}
      <AddTemModal
        parentMenuData={parentMenuData}
        open={addTemModalShow}
        close={() => setAddTemModalShow(false)}
        onOk={refreshPage}
      />
      {/* 编辑模板弹框 */}
      <EditTemModal
        menuData={curTableMenuData}
        editData={curTableData}
        open={editTemModalShow}
        close={() => setEditTemModalShow(false)}
        onOk={refreshPage}
      ></EditTemModal>
    </div>
  );
};

export default TemplateManagementPage;
