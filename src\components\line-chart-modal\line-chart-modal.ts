import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
import moment from 'moment';
import { ReactNode } from 'react';
export const defaultStartDate = moment().subtract(60, 'minutes').format('YYYY-MM-DD HH:mm:ss');
export const defaultEndDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
export interface PropOptions {
  close: () => void; //关闭方法
  queryFn: (params?: any) => void; //查询方法
  resetFn: () => void; //重置方法
  open: boolean; //展示控制
  ref?: any;
  loading?: boolean; //加载控制
  unit?: string; //统计图单位
  title: string; //弹框标题
  width: string; //弹框宽度
  height: string; //弹框高度
  children?: ReactNode;
  chartData: DataOptions[]; //统计图数据
  formComponent?: ReactNode; //表单节点
  pointComponent: ReactNode; //右侧点位节点
}
