import React, { memo } from 'react';
import type { ReactNode, FC } from 'react';

import { SummaryItemWrapper } from './summary-item.style';

interface IProps {
  children?: ReactNode;
  title: string;
  num: ReactNode;
  unit: string;
  icon: string;
}

const SummaryItem: FC<IProps> = (props) => {
  const { title, num, unit, icon } = props;

  return (
    <SummaryItemWrapper>
      <div className="left">
        <img src={icon} />
      </div>
      <div className="right">
        <div className="title">{title}</div>
        <div className="text">
          <span className="num">{num}</span>
          &nbsp;
          <span className="unit">{unit}</span>
        </div>
      </div>
    </SummaryItemWrapper>
  );
};

export default memo(SummaryItem);
