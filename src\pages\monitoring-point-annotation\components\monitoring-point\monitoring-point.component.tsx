import { FC, forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './monitoring-point.component.less';
import type { PropOptions, TreeOptions } from './monitoring-point';
import { ControllerType } from './monitoring-point';
import { MonitoringPointAdd } from '../monitoring-point-add-modal/monitoring-point-add-modal.component';
import { ResponseOptions, baseUnitTree, delTreeNode } from '@/services/monitoring-point-annotation';
import { MonitoringImageAdd } from '../monitoring-image-add-modal/monitoring-image-add-modal.component';
import { DelModal } from '@/components/del-modal/del-modal.component';

export const MonitoringPoint: FC<PropOptions> = memo(
  forwardRef(({ treeNodeChange }, ref) => {
    //树加载控制
    const [treeLoading, setTreeLoading] = useState<boolean>(true);
    //新增禁用控制
    const [addDisabled, setAddDisabled] = useState<boolean>(true);
    //编辑禁用控制
    const [editDisabled, setEditDisabled] = useState<boolean>(true);
    //删除禁用控制
    const [delDisabled, setDelDisabled] = useState<boolean>(true);
    //删除弹框控制
    const [delModalShow, setDelModalShow] = useState<boolean>(false);
    //标注弹框控制
    const [pointModalShow, setPointModalShow] = useState<boolean>(false);
    //图片弹框控制
    const [imageModalShow, setImageModalShow] = useState<boolean>(false);
    //当前选中节点
    const [curNode, setCurNode] = useState<TreeOptions>();
    //当前节点标题类型
    const [title, setTitle] = useState<ControllerType>(ControllerType.ADD);
    const treeRef = useRef<any>(null);
    useImperativeHandle(ref, () => ({
      treeRef,
    }));
    useEffect(() => {
      getPoinRootList();
      if (treeRef.current) {
        treeRef.current.loadData = getPointChildList;
        treeRef.current.nodeSelect = treeNodeChangeHandler;
      }
    }, []);
    //节点选择处理
    function treeNodeChangeHandler(node: TreeOptions): void {
      const { level } = node;
      setCurNode(node);
      treeNodeChange(node);
      switch (level) {
        case 0:
          setAddDisabled(false);
          setEditDisabled(true);
          setDelDisabled(true);
          break;
        case 1:
          setAddDisabled(false);
          setEditDisabled(false);
          setDelDisabled(false);
          break;
        case 2:
          setAddDisabled(true);
          setEditDisabled(false);
          setDelDisabled(false);
          break;
      }
    }
    //删除节点
    function delTreeNodeRequest(): Promise<ResponseOptions<any>> {
      return delTreeNode(curNode?.id || '');
    }

    //更新树节点
    function updateTreeNode(type?: string): void {
      //sureDelFile
      if (curNode) {
        switch (title) {
          case ControllerType.ADD:
            treeRef.current.updateNode(curNode.id);
            break;
          case ControllerType.EDIT:
            setCurNode(undefined);
            treeRef.current.updateNode(curNode.parentId);
            break;
          case ControllerType.DEL:
            setCurNode(undefined);
            treeRef.current.updateNode(curNode.parentId);
            break;
        }
      }
      setPointModalShow(false);
      setDelModalShow(false);
      setImageModalShow(false);
    }
    //获取监测设备树子节点
    function getPointChildList({ id }: TreeOptions): Promise<ResponseOptions<TreeOptions[]>> {
      return baseUnitTree<TreeOptions[]>({ parentId: id });
    }
    //获取监测设备树根节点
    function getPoinRootList(): void {
      setTreeLoading(true);
      baseUnitTree<TreeOptions[]>({ level: 0 }).then((res) => {
        if (res.code === '1' && treeRef.current) {
          treeRef.current.treeData = res.data.map((item) => {
            item.isLeaf = false;
            return item;
          });
        }
        setTreeLoading(false);
      });
    }
    //根节点操作处理
    function oLevelControllerHandler(type: ControllerType): void {
      setImageModalShow(true);
    }
    //1级子节点操作处理
    function oneLevelControllerHandler(type: ControllerType): void {
      switch (type) {
        case ControllerType.ADD:
          setPointModalShow(true);
          break;
        case ControllerType.EDIT:
          setImageModalShow(true);
          break;
        case ControllerType.DEL:
          setDelModalShow(true);
          break;
        default:
          console.log('未知操作');
          break;
      }
    }
    //2级子节点操作处理
    function twoLevelControllerHandler(type: ControllerType): void {
      switch (type) {
        case ControllerType.ADD:
          break;
        case ControllerType.EDIT:
          setPointModalShow(true);
          break;
        case ControllerType.DEL:
          setDelModalShow(true);
          break;
        default:
          console.log('未知操作');
          break;
      }
    }
    //监测点操作处理
    function controllerHandler(type: ControllerType): void {
      setTitle(type);
      switch (curNode?.level) {
        case 0:
          oLevelControllerHandler(type);
          break;
        case 1:
          oneLevelControllerHandler(type);
          break;
        case 2:
          twoLevelControllerHandler(type);
          break;
        default:
          console.log('未知节点');
          break;
      }
    }
    return (
      <>
        <section className={styles['monitoring-point-container']}>
          <p className={styles['monitoring-point-container-title']}>
            <span>设备监测点列表</span>
          </p>
          <div className={styles['monitoring-point-container-controller']}>
            <type-button disabled={addDisabled} onClick={() => controllerHandler(ControllerType.ADD)}>
              {ControllerType.ADD}
            </type-button>
            <type-button disabled={editDisabled} onClick={() => controllerHandler(ControllerType.EDIT)}>
              {ControllerType.EDIT}
            </type-button>
            <type-button disabled={delDisabled} onClick={() => controllerHandler(ControllerType.DEL)}>
              {ControllerType.DEL}
            </type-button>
          </div>
          <div className={styles['monitoring-point-container-tree']}>
            <checked-tree
              value={curNode ? [curNode.id] : []}
              maxlevel={2}
              cancellation={false}
              loading={treeLoading}
              isicon={true}
              ref={treeRef}
            ></checked-tree>
          </div>
        </section>
        {/* 删除弹框 */}
        <DelModal
          delFn={delTreeNodeRequest}
          onOk={updateTreeNode}
          tipsText={'是否删除 “' + curNode?.name + '” 节点？'}
          open={delModalShow}
          close={() => setDelModalShow(false)}
        ></DelModal>
        {/* 监测点表单弹框 */}
        <MonitoringPointAdd
          type={title}
          nodeData={curNode as TreeOptions}
          onOk={updateTreeNode}
          title={curNode?.name + ' - ' + title}
          close={() => setPointModalShow(false)}
          open={pointModalShow}
        />
        {/* 图片表单弹框 */}
        <MonitoringImageAdd
          type={title}
          nodeData={curNode as TreeOptions}
          onOk={updateTreeNode}
          title={curNode?.name + ' - ' + title}
          close={() => setImageModalShow(false)}
          open={imageModalShow}
        />
      </>
    );
  }),
);
