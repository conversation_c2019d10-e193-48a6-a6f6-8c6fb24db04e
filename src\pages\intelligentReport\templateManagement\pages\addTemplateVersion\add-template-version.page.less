.report-add-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-container {
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid #28487b;
}

.controller-container {
  display: flex;
}

.back {
  cursor: pointer;
  margin-left: 20px;
  color: #0392d8;
  display: flex;
  align-items: center;

  span {
    color: #0392d8;
  }
}

.title {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.work-table-tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.work-table-tabs-hidden {
  display: none;
}

.content {
  height: 100%;
  display: flex;
}

.mask {
  width: 100%;
  height: 100%;
  background: #9995;
}

.icon {
  font-size: 18px;
  color: #3a91e6;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.info {
  flex: 0 0 450px;
  width: 450px;
  display: flex;
  flex-direction: column;

  &-item {
    border-bottom: 1px solid #28487b;
    display: flex;

    span {
      color: #fff;
      font-size: 14px;
      display: inline-block;
      padding: 10px 10px 8px 10px;
    }

    span:first-child {
      border-right: 1px solid #28487b;
      color: #83c4f0;
      flex: 0 0 100px;
    }
  }
}

.point-name {
  cursor: pointer;
}

.permiss-title-container {
  display: flex;
  justify-content: space-between;
  margin: 0px;
}

.permiss-title {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.spin {
  flex: 1;
}

:global {
  .add-template-version-page {
    .ant-spin-container {
      height: 100%;
    }

    .custom-point {
      background: url(~@/assets/images/point.png) no-repeat;
      background-size: contain;
    }
    .custom-influxes {
      background: url(~@/assets/images/influxes.png) no-repeat;
      background-size: contain;
    }
    .custom-history {
      background: url(~@/assets/images/history.png) no-repeat;
      background-size: contain;
    }
    .custom-plan {
      background: url(~@/assets/images/plan.png) no-repeat;
      background-size: contain;
    }
    .ant-collapse {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      min-height: 0px;
      flex-basis: 0;
    }

    .ant-collapse-header {
      align-items: center !important;
      color: #fff !important;
    }

    .ant-tabs-nav {
      padding-left: 20px;
      margin: 0px;
    }

    .ant-tabs-tab-btn {
      font-size: 16px;
    }

    .work-table-tabs {
      .ant-tabs-nav {
        padding-left: 30px;
      }

      .ant-tabs-tab {
        padding: 17px 16px 5px;
        margin: 0px !important;
      }

      .ant-tabs-tab-btn {
        font-size: 14px;
      }

      .ant-tabs-tab-active.ant-tabs-tab {
        background: url(/images/active.png);
        background-size: cover;
      }
    }
  }
}
