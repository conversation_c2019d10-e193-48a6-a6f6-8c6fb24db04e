import { Tag } from 'antd';
import dayjs from 'dayjs';

import styles from './process-list.component.less';

interface Props {
  data: any[];
  taskUserName?: string;
}

const ProcessListComponent = ({ data, taskUserName }: Props) => {
  return (
    <div className={styles.list}>
      {data.map((item) => (
        <div className={styles.item} key={item.id}>
          <div className={styles.baseInfo}>
            <span>{item.taskName}</span>
            {item.approverName ? (
              <span className={styles.username}>（{item.approverName}）</span>
            ) : null}
            {item?.approvalTime ? <span>提交时间：</span> : null}
            <span className={styles.time}>
              {item?.approvalTime ? dayjs(item?.approvalTime).format('YYYY/MM/DD HH:mm:ss') : ''}
            </span>
            {item?.approvalResult === '提交' ? <Tag color="#02aafa">提交</Tag> : null}
            {item?.approvalResult === '退回' ? <Tag color="#d9001b">退回</Tag> : null}
            {item?.approvalResult === '终结' ? <Tag color="#02e190">终结</Tag> : null}
            {item?.approvalResult === '合格' ? <Tag color="#02e190">合格</Tag> : null}
            {item?.approvalResult === '通过' ? <Tag color="#02e190">通过</Tag> : null}
            {item?.approvalResult === '不合格' ? <Tag color="#d9001b">不合格</Tag> : null}
            {item?.approvalResult === '作废' ? <Tag color="#d9001b">作废</Tag> : null}
          </div>
          {item?.opinion ? (
            <div className={styles.content}>
              <span>
                {['工作票负责人变动', '发起负责人变更申请'].includes(item.taskName)
                  ? '变动情况说明：'
                  : ['工作票延期', '发起工作票延期'].includes(item.taskName)
                  ? '延期理由：'
                  : ['工作票交回'].includes(item.taskName)
                  ? '备注：'
                  : '审核意见：'}
              </span>
              <span>{item.opinion}</span>
            </div>
          ) : null}
        </div>
      ))}
      {taskUserName ? (
        <div className={styles.taskUserName}>
          <span>当前处理人：</span> {taskUserName}
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default ProcessListComponent;
