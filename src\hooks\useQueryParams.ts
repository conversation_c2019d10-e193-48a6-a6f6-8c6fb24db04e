import { useMemo } from 'react';
import { useLocation } from 'umi';
/**
 * 获取路由参数
 * @returns
 */
function useQueryParams() {
  const location = useLocation();
  return useMemo(() => {
    const params = new URLSearchParams(location.search);
    const result: Record<string, string> = {};
    params.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }, [location.search]);
}
export default useQueryParams;
