import styled from 'styled-components';

import bannerBgIcon from '@/assets/images/icon_banner_bg.png';

export const DashboradGraphWrapper = styled.div`
  .title {
    text-align: center;
    height: 10%;
    font-size: 18px;
    font-weight: bold;
    background: url(${bannerBgIcon});
    background-position: center center;
    background-repeat: no-repeat;
  }

  .subtitle {
    height: 15%;
    font-weight: bold;
    text-align: center;
    font-size: 16px;

    .name {
      font-size: 18px;
    }

    .time {
      font-size: 30px;

      & > span {
        color: #0ae4ff;
        display: inline-block;
        width: 5%;
        font-size: 32px;
        font-weight: bold;
        margin-right: 1%;
        text-align: center;
        background: linear-gradient(90deg, rgba(21, 161, 216, 0.1) 0%, rgba(21, 161, 216, 0.05) 100%);
      }
    }

    .status {
      color: #34f3c4;
      font-size: 18px;
      padding: 1%;
      border: 1px solid #2964ad;
    }
  }

  .graph {
    height: 40%;
    position: relative;
    justify-content: center;
    align-items: center;

    canvas {
      height: 100%;
      display: block;
      margin: auto;
    }

    .text {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;

      .num {
        font-weight: bold;
        font-size: 18px;
      }

      .unit {
        font-size: 10px;
      }

      .label {
        margin-top: 15%;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }

  .parameters {
    height: 35%;
    width: 90%;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: space-between;
    align-items: center;

    & > .parameter-item {
      height: 30%;
      width: 49%;
      border: 1px solid #2c5894;
      display: flex;

      &:not(:nth-last-child(2 - n)) {
        margin-bottom: 0.5%;
      }

      .label,
      .value {
        flex: 1;
        padding: 3% !important;
        text-align: center !important;
        display: flex;
        align-items: center;
      }

      .label {
        background-color: #164c8a;
        justify-content: center;
      }

      .value {
        justify-content: space-around;

        & > .num {
          color: #0ae4ff;
        }
      }
    }
  }
`;
