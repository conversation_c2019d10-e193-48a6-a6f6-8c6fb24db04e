import { FC, ReactNode } from 'react';
import type { PropsOptions, ColumnsOptions } from './table';
import { Table } from 'antd';
//通用表格组件
export const TableComponent: FC<PropsOptions> = ({
  onTableRowClick,
  rowSelection,
  tableList,
  columns,
  params,
  loading,
  rowKey = 'id',
  rowClassName,
  className,
  scroll,
  isIndex = true,
  isVirtual = false,
  indexW = 100,
}) => {
  const columnsOptions: any[] = columns;
  if (isIndex) {
    //序号处理
    columnsOptions.unshift({
      title: '序号',
      dataIndex: 'xuhao',
      width: indexW,
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
      render: (planName: string, row: any, index: number) => {
        if (params) {
          const { pageSize, current } = params;
          const indexs = (current - 1) * pageSize + index + 1;
          return indexs;
        } else {
          return index + 1;
        }
      },
    });
  }
  return (
    <Table
      scroll={scroll}
      pagination={
        params
          ? {
              //分页配置
              onChange: params.pageChange,
              total: params.total,
              pageSize: params.pageSize,
              current: params.current,
              showQuickJumper: true,
              showSizeChanger: true,
              showTotal: (count) => `共 ${count} 条`,
            }
          : false
      }
      onRow={(record) => ({
        //行点击
        onClick: () => {
          if (onTableRowClick) {
            onTableRowClick(record);
          }
        },
      })}
      virtual={isVirtual}
      loading={loading} //加载
      className={className}
      rowClassName={rowClassName}
      rowSelection={rowSelection}
      columns={columnsOptions}
      dataSource={tableList}
      rowKey={rowKey}
    />
  );
};
