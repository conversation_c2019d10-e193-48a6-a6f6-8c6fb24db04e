import React, { memo, useEffect, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import { Chart } from '@antv/g2';
import { v4 as uuidv4 } from 'uuid';

import { LineGraphWrapper } from './line-graph.style';

interface IProps {
  children?: ReactNode;
  title: string;
}

const LineGraph: FC<IProps> = (props) => {
  const { title } = props;

  const idRef = useRef<string>(`chart_source_${uuidv4().slice(0, 8)}`);

  useEffect(() => {
    renderGraph();
  }, []);

  const renderGraph = () => {
    const chart = new Chart({ container: idRef.current });

    chart.options({
      type: 'view',
      autoFit: true,
      data: [
        { year: '1991', value: 15468 },
        { year: '1992', value: 16100 },
        { year: '1993', value: 15900 },
        { year: '1994', value: 17409 },
        { year: '1995', value: 17000 },
        { year: '1996', value: 31056 },
        { year: '1997', value: 31982 },
        { year: '1998', value: 32040 },
        { year: '1999', value: 33233 },
      ],
      children: [
        {
          type: 'area',
          encode: { x: (d: any) => d.year, y: 'value', shape: 'area' },
          style: { opacity: 0.2 },
          axis: {
            y: {
              title: '(MW)',
              titleFill: '#fff',
              titlePosition: 'lc', // 标题相对坐标轴的位置
              labelFormatter: '~s',
              labelFill: '#fff',
              line: true, // 是否显示坐标轴线
              lineStroke: '#6CA1DD',
              lineExtension: [0, 0], // 轴线两侧的延长线
              grid: true, // 是否显示刻度线
              gridStroke: '#eee', // 纵轴网格线颜色设置为淡灰色
              gridLineWidth: 1, // 纵轴网格线宽度
              gridLineDash: [3, 3], // 纵轴网格线虚线样式
            },
            x: {
              labelFill: '#fff', // 设置横坐标文字颜色
              line: true,
              lineStroke: '#6CA1DD',
            },
          },
        },
        { type: 'line', encode: { x: 'year', y: 'value', shape: 'line', color: '#0AE4FF' } },
      ],
    });

    // 设置图表标题
    chart.title({
      title: `日偏差发电量 ${'XX'} 万KW.h`,
      align: 'center',
      titleFill: '#fff',
    });

    chart.render();
  };

  return (
    <LineGraphWrapper>
      <div className="title">{title}</div>
      <div className="chart" id={idRef.current}></div>
    </LineGraphWrapper>
  );
};

export default memo(LineGraph);
