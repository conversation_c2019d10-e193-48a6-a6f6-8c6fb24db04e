import React, { forwardRef, memo, useImperativeHandle, useState } from 'react';
import type { ReactNode, FC } from 'react';
import {
  Button,
  DatePicker,
  Form,
  Input,
  Radio,
  Select,
  Upload,
  Image,
  Switch,
  InputNumber,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import { UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

import { BaseFormWrapper } from './base-form.style';
import { EElementType } from './base-form.types';
import type { IBaseFormConfig, ElementType, FieldType, IBaseFormExp } from './base-form.types';

interface IProps {
  children?: ReactNode;
  config: IBaseFormConfig;
}

const BaseForm = forwardRef<IBaseFormExp, IProps>((props, ref) => {
  const {
    config: { elements: els, layout, initialValues, formPorps },
  } = props;

  const [form] = useForm<FieldType>();

  useImperativeHandle<IBaseFormExp, IBaseFormExp>(ref, () => ({ baseFormInstance: form }), []);

  return (
    <BaseFormWrapper>
      <Form
        form={form}
        layout={layout ?? 'inline'}
        initialValues={initialValues}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        {...formPorps}
      >
        {els.map((el: ElementType) => {
          const { key, type, label, formItemProps } = el;
          switch (type) {
            case EElementType.BUTTON:
              return (
                <Form.Item key={key} noStyle={el.noStyle ?? true} {...formItemProps}>
                  <Button
                    type="primary"
                    disabled={el.disabled}
                    loading={el.loading}
                    onClick={el.onClick}
                    {...el.props}
                  >
                    {label}
                  </Button>
                </Form.Item>
              );
            case EElementType.INPUT:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <Input
                    placeholder="请输入..."
                    disabled={el.disabled}
                    onBlur={el.onBlur}
                    onChange={el.onChange}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EElementType.INPUTNUMBER:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <InputNumber
                    placeholder="请输入..."
                    min={el.min}
                    max={el.max}
                    addonAfter={el.addonAfter}
                    disabled={el.disabled}
                    onBlur={el.onBlur}
                    onChange={el.onChange}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EElementType.TEXTAREA:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <Input.TextArea
                    placeholder="请输入..."
                    disabled={el.disabled}
                    onBlur={el.onBlur}
                    onChange={el.onChange}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EElementType.SELECT:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <Select
                    placeholder="请选择..."
                    options={el.options}
                    allowClear={el.allowClear}
                    disabled={el.disabled}
                    onChange={el.onChange}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EElementType.RADIO:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <Radio.Group disabled={el.disabled} options={el.options} {...el.props} />
                </Form.Item>
              );
            case EElementType.SWITCH:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <Switch defaultChecked={el.defaultChecked} onChange={el.onChange} {...el.props} />
                </Form.Item>
              );
            case EElementType.DATEPICKER:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  {...formItemProps}
                >
                  <DatePicker placeholder="请选择..." allowClear={el.allowClear} {...el.props} />
                </Form.Item>
              );
            case EElementType.RANGEPICKER:
              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  rules={el.rules}
                  normalize={(values: [Dayjs, Dayjs] | null) => {
                    if (!values) return values;
                    const [startTime, endTime] = values;
                    return [
                      dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
                      dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
                    ];
                  }}
                  getValueProps={(value: any) => {
                    if (Array.isArray(value)) return { value: value.map(dayjs) };
                    else return {};
                  }}
                  {...formItemProps}
                >
                  <DatePicker.RangePicker
                    placeholder={['请选择...', '请选择...']}
                    allowClear={el.allowClear}
                    {...el.props}
                  />
                </Form.Item>
              );
            case EElementType.UPLOAD:
              const normFile = (e: any) => {
                if (Array.isArray(e)) return e;
                return e?.fileList;
              };

              /* 图片上传相关 */
              const [previewOpen, setPreviewOpen] = useState<boolean>(false);
              const [previewImage, setPreviewImage] = useState<string | null>(null);

              const getBase64 = (file: Blob) =>
                new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.readAsDataURL(file);
                  reader.onload = () => resolve(reader.result);
                  reader.onerror = (error) => reject(error);
                });

              const handleImagePreview = async (file: any) => {
                if (!file.url && !file.preview) {
                  file.preview = await getBase64(file.originFileObj);
                }
                setPreviewImage(file.url || file.preview);
                setPreviewOpen(true);
              };

              return (
                <Form.Item
                  key={key}
                  label={label}
                  name={el.field}
                  valuePropName="fileList"
                  getValueFromEvent={normFile}
                  rules={el.rules}
                  {...formItemProps}
                >
                  {/* 文件上传及上传列表 */}
                  <Upload
                    listType={el.listType}
                    accept={
                      el.accept ??
                      // el.listType === 'picture' | 'picture-card' | 'picture-circle' ==> 'image/*'
                      (['text', undefined].includes(el.listType) ? undefined : 'image/*')
                    }
                    maxCount={el.maxCount}
                    multiple={el.multiple}
                    beforeUpload={() => false}
                    // file.url决定是否支持预览；onPreview（点击文件链接或预览图标时的回调）决定是否使用自定义预览函数
                    // el.listType === 'picture' | 'picture-card' | 'picture-circle' ==> handleImagePreview
                    onPreview={
                      ['text', undefined].includes(el.listType) ? undefined : handleImagePreview
                    }
                    onChange={el.onChange}
                    {...el.props}
                  >
                    {el.children ??
                      (['text', 'picture', undefined].includes(el.listType) ? (
                        <Button type="primary" icon={<UploadOutlined />}>
                          点击上传
                        </Button>
                      ) : (
                        // el.listType === 'picture-card' | 'picture-circle'
                        <button
                          type="button"
                          style={{
                            border: 0,
                            background: 'none',
                            cursor: 'inherit',
                          }}
                        >
                          <UploadOutlined />
                          <div style={{ marginTop: '8px' }}>点击上传</div>
                        </button>
                      ))}
                  </Upload>

                  {/* 图片预览 */}
                  {previewImage && (
                    <Image
                      wrapperStyle={{
                        display: 'none',
                      }}
                      preview={{
                        visible: previewOpen,
                        onVisibleChange: (visible) => setPreviewOpen(visible),
                        afterOpenChange: (visible) => !visible && setPreviewImage(null),
                      }}
                      src={previewImage}
                    />
                  )}
                </Form.Item>
              );
            case EElementType.IMAGE:
              return (
                <Form.Item key={key} label={label} {...formItemProps}>
                  {el.group ? (
                    <Image.PreviewGroup items={el.items}>
                      {el.items.length && <Image src={el.items[0]} {...el.props} />}
                    </Image.PreviewGroup>
                  ) : (
                    <Image src={el.src} {...el.props} />
                  )}
                </Form.Item>
              );
            case EElementType.CUSTOM:
              return (
                <Form.Item key={key} label={label} noStyle={el.noStyle} {...formItemProps}>
                  {el.element}
                </Form.Item>
              );
            default:
              let check: never;
              break;
          }
        })}
      </Form>
    </BaseFormWrapper>
  );
});

export default memo(BaseForm);
