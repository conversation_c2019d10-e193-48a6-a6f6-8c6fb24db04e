import { FC, memo, useEffect, useState } from 'react';
import { SvgModal } from '@/components/svg-modal/svg-modal.component';
import { Form, Input } from 'antd';
import { TableComponent } from '@/components/table-component/table-component';
import { ColumnsOptions } from '@/components/table-component/table';
import { ColumnsType } from 'antd/es/table';
import styles from './point-modal.component.less';
export interface PointModalOptions {
  open: boolean;
  close: () => void; //关闭方法
  onOk: (list: any[]) => void; //确定
  columns: ColumnsType<ColumnsOptions>; //点位选择弹窗表格配置
  onSearch: (searchParam: any) => void; //监听查询
  pointList: RowsDataType[]; //点位列表
  loading: boolean; //加载控制
  chosePointList: RowsDataType[]; //外部传入的已选择的数据
}
export interface RowsDataType {
  id: string;
  name: string;
  [key: string]: any;
}
export const PointModal: FC<PointModalOptions> = memo(
  ({ chosePointList, loading, pointList, onOk, open, close, columns, onSearch }) => {
    const [selectList, setSelectList] = useState<RowsDataType[]>([]);
    const [forms] = Form.useForm();
    //表格选择配置
    const rowSelection = {
      selectedRowKeys: selectList.map((item) => item.key),
      onChange: (newSelectedRowKeys: React.Key[], selectedRows: RowsDataType[]) => {
        setSelectList(selectedRows);
      },
    };

    //表格行点击
    function onTableRowClick(data: RowsDataType): void {
      const node = selectList.find((item) => item.key === data.key);
      if (node) {
        setSelectList(selectList.filter((item) => item.key !== data.key));
      } else {
        setSelectList(selectList.concat(data));
      }
    }
    //数据查询
    function query(): void {
      setSelectList([]);
      console.log(forms.getFieldValue('searchParam'), 'getFieldValue========');

      onSearch(forms.getFieldValue('searchParam'));
    }

    // //已选中测点重置
    // function reset(): void {
    //   setSelectList([]);
    // }

    useEffect(() => {
      if (open) {
        forms.setFieldValue('searchParam', '');
        setSelectList([]);
        onSearch(forms.getFieldValue('searchParam'));
      }
    }, [open]);
    useEffect(() => {
      if (open && chosePointList) {
        setSelectList(chosePointList);
      }
    }, [open, chosePointList]);
    return (
      <SvgModal
        width="800px"
        height="fitContent"
        close={() => close()}
        isFooterBtn={false}
        open={open}
        title="对比点位数据列表"
      >
        <Form
          form={forms}
          layout="inline"
          initialValues={{ layout: 'inline' }}
          style={{ maxWidth: 'none', paddingLeft: '23px' }}
        >
          <Form.Item label="数据类型" name="searchParam">
            <Input style={{ width: '200px' }} placeholder="请选择..." />
          </Form.Item>
          <Form.Item>
            <type-button loading={loading} onClick={query}>
              查询
            </type-button>
          </Form.Item>
          {/* <Form.Item>
            <type-button loading={loading} onClick={reset}>
              重置
            </type-button>
          </Form.Item> */}
        </Form>
        <TableComponent
          isVirtual={true}
          onTableRowClick={onTableRowClick}
          rowKey="key"
          rowSelection={rowSelection}
          loading={loading}
          scroll={{ y: 290 }}
          columns={columns}
          tableList={pointList}
        ></TableComponent>
        <div className={styles.footer}>
          <type-button onClick={() => close()}>取消</type-button>
          <type-button onClick={() => onOk(selectList)}>确定</type-button>
        </div>
      </SvgModal>
    );
  },
);
