import { FC, forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { PropOptions } from './upload-file';
import { FileOptions } from '@/services/system';

export const UploadComponent: FC<PropOptions> = memo(
  forwardRef(({ styleType, delChange, value, onChange, maxcount }, ref) => {
    const fileRef = useRef<any>(null);
    const [file, setFile] = useState<string | undefined>('');
    useImperativeHandle(ref, () => ({
      fileRef,
    }));
    useEffect(() => {
      if (fileRef.current) {
        fileRef.current.onChange = fileOnChange;
        fileRef.current.delFileFn = delFileChange;
      }
    }, []);
    useEffect(() => {
      if ((value && typeof value === 'string') || value == undefined || value == null) {
        setFile(value);
      } else if (typeof value === 'object') {
        setFile(value.attachmentId);
      }
    }, [value]);
    //文件删除
    function delFileChange(files: FileOptions[], fileNode: FileOptions) {
      if (delChange) {
        delChange(files, fileNode);
      }
    }
    //文件上传
    function fileOnChange(attachmentId: string, fileList: FileOptions[]) {
      if (onChange) {
        if (fileList.length > 0) {
          onChange({
            attachmentId,
            fileList,
          });
        } else {
          onChange(undefined);
        }
      }
    }
    return <upload-file styletype={styleType} maxcount={maxcount} value={file} ref={fileRef}></upload-file>;
  }),
);
