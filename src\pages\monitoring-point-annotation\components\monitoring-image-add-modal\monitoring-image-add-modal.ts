import { ControllerType, TreeOptions } from '../monitoring-point/monitoring-point';

export interface PropOptions {
  open: boolean;
  close: () => void; //关闭方法
  onOk?: (type: string) => void; //提交方法
  title: string; //标题
  nodeData: TreeOptions; //选中节点数据
  type: ControllerType; //操作类型
}
//图片上传参数
export interface ImageParams {
  attachId: string; //文件夹id
  imgId: string; //文件id
  name: string; //名称
  parentId?: string; //父节点id
  id?: string; //编辑节点id
}
