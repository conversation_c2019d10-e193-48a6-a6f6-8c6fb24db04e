import { MouseEvent, memo, useState } from 'react';
import styles from './Interval-duration-chart.component.less';
import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
import { DoubleLineChart } from '@/components/line-chart-double/line-chart-double.component';

export const IntervalDurationChart = memo(() => {
  const [chartData, setChartData] = useState<[DataOptions[], DataOptions[]]>([
    [
      {
        label: '2025/07/01',
        value: 4,
        type: '集水井液位',
      },
      {
        label: '2025/07/02',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/03',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/04',
        value: 12,
        type: '集水井液位',
      },
      {
        label: '2025/07/05',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/06',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/07',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/08',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/09',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/10',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/11',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/12',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/13',
        value: 10,
        type: '集水井液位',
      },
      {
        label: '2025/07/14',
        value: 2,
        type: '集水井液位',
      },
      {
        label: '2025/07/15',
        value: 10,
        type: '集水井液位',
      },
    ],
    [
      {
        label: '2025/07/01',
        value: 9,
        type: '液位变化率',
      },
      {
        label: '2025/07/02',
        value: 8,
        type: '液位变化率',
      },
      {
        label: '2025/07/03',
        value: 3,
        type: '液位变化率',
      },
      {
        label: '2025/07/04',
        value: 7,
        type: '液位变化率',
      },
      {
        label: '2025/07/05',
        value: 3,
        type: '液位变化率',
      },
      {
        label: '2025/07/06',
        value: 13,
        type: '液位变化率',
      },
      {
        label: '2025/07/07',
        value: 5,
        type: '液位变化率',
      },
      {
        label: '2025/07/08',
        value: 20,
        type: '液位变化率',
      },
      {
        label: '2025/07/09',
        value: 15,
        type: '液位变化率',
      },
      {
        label: '2025/07/10',
        value: 30,
        type: '液位变化率',
      },
      {
        label: '2025/07/11',
        value: 2,
        type: '液位变化率',
      },
      {
        label: '2025/07/12',
        value: 1,
        type: '液位变化率',
      },
      {
        label: '2025/07/13',
        value: 10,
        type: '液位变化率',
      },
      {
        label: '2025/07/14',
        value: 7,
        type: '液位变化率',
      },
      {
        label: '2025/07/15',
        value: 3,
        type: '液位变化率',
      },
    ],
  ]);
  const [time, setTime] = useState<string>('1');
  //统计图线条配置
  const geometryOptions = [
    {
      color: '#0AC8E6',
      seriesField: 'type',
    },
    {
      color: '#FBEE6A',
      seriesField: 'type',
      lineStyle: {
        lineWidth: 1,
        lineDash: [2, 1],
      },
    },
  ];
  //时间改变
  function timeChange(e: MouseEvent<HTMLDivElement>): void {
    const target: any = e.target;
    const value = target.getAttribute('data-value');
    if (value) {
      setTime(value);
    }
  }
  return (
    <div className={styles['interval-duration-chart']}>
      <div className={styles['interval-duration-chart-top']}>
        <div className={styles['interval-duration-chart-title']}>
          <span>集水井液位和液位变化率分析</span>
        </div>
        <div
          onClick={timeChange}
          className={`${styles['interval-duration-chart-check']} ${
            styles['interval-duration-chart-check-' + time]
          }`}
        >
          <span data-value="1">近七天</span>
          <span data-value="2">近一月</span>
        </div>
      </div>
      <div className={styles['interval-duration-chart-main']}>
        <DoubleLineChart
          geometryOptions={geometryOptions}
          appendPadding={[20, 0, 0, 0]}
          padding="auto"
          legend={false}
          unitY={20}
          unit={['m', 'm/h']}
          chartData={chartData}
        ></DoubleLineChart>
      </div>
    </div>
  );
});
