/**
 * 检查身份证号是否合法
 * @param {string} idNumber 身份证号
 * @returns
 */
export function idNumberCheck(idNumber: string) {
  // 校验长度
  if (idNumber.length !== 18) {
    return false;
  }

  // 校验前17位是否为数字
  const reg = /^\d{17}(\d|x|X)$/;
  if (!reg.test(idNumber.slice(0, 17))) {
    return false;
  }

  // 校验最后一位
  const wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 加权因子
  const validateCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']; // 校验码
  let sum = 0;
  for (var i = 0; i < 17; i++) {
    sum += parseInt(idNumber.charAt(i)) * wi[i];
  }
  const mod = sum % 11;
  const lastChar = idNumber.charAt(17);
  if (lastChar.toUpperCase() !== validateCode[mod]) {
    return false;
  }

  return true;
}
/**
 * 从身份证号中提取出生日期以及性别
 * @param {string} idNumber 身份证号
 * @returns
 */
export function extractBirthDateAndGender(idNumber: string) {
  const birthYear = Number.parseInt(idNumber.slice(6, 10));
  const birthMonth = Number.parseInt(idNumber.slice(10, 12));
  const birthDay = Number.parseInt(idNumber.slice(12, 14));

  const genderCode = idNumber.slice(16, 17);
  const gender = parseInt(genderCode) % 2 === 0 ? '1' : '0';

  const birthDate = new Date(birthYear, birthMonth - 1, birthDay); // 月份需减1

  return {
    birthDate: birthDate.toLocaleDateString('zh-CN'),
    gender: gender,
  };
}
