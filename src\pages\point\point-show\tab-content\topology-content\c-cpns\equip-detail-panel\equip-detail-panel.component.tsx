import React, { memo, useEffect, useState } from 'react';
import type { ReactNode, FC } from 'react';
import { Descriptions } from 'antd';
import type { DescriptionsProps, TableProps } from 'antd';

import { DetailPanelWrapper } from './equip-detail-panel.style';
import { getDeviceInfoById } from '../../topology-content.service';

export function traverse(dictCode: string, nodes: any[]) {
  for (const node of nodes) {
    if (node.dictCode === dictCode) return node;
    if (node.children?.length) {
      const result: any = traverse(dictCode, node.children);
      if (result) return result;
    }
  }
  return null;
}

type RecordType = any;

interface IProps {
  children?: ReactNode;
  nodes: any[];
  edges: any[];
  visible: boolean;
  selectedBusinessId: string;
}

const DetailPanel: FC<IProps> = (props) => {
  const { visible, selectedBusinessId, nodes, edges } = props;

  const [baseInfoData, setBaseInfoData] = useState<any>(null);

  useEffect(() => {
    if (!visible || !selectedBusinessId) return;

    getDeviceInfoById(selectedBusinessId).then((res) => {
      setBaseInfoData({
        ...res.data,
      });
    });
  }, [visible, selectedBusinessId]);

  const baseInfo: DescriptionsProps['items'] = [
    {
      key: '1',
      label: '节点类型',
      span: 3,
      children: '设备',
    },
    {
      key: '2',
      label: '设备名称',
      span: 3,
      children: baseInfoData?.name || '-',
    },
    {
      key: '3',
      label: '设备编码(kks)',
      span: 3,
      children: baseInfoData?.kks || '-',
    },
    {
      key: '4',
      label: '规格型号',
      span: 3,
      children: baseInfoData?.specification || '-',
    },
    {
      key: '5',
      label: '设备类型',
      span: 3,
      children: baseInfoData?.type || '-',
    },
    {
      key: '6',
      label: '制造厂家',
      span: 3,
      children: baseInfoData?.factory || '-',
    },
    {
      key: '7',
      label: '安装地点',
      span: 3,
      children: baseInfoData?.loctionInstall || '-',
    },
  ].map<NonNullable<DescriptionsProps['items']>[number]>((item, index) => ({
    ...item,
    style: { color: '#fff' },
    labelStyle: { color: '#8fd4ff' },
  }));

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'deviceName',
      align: 'center',
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      key: 'deviceType',
      align: 'center',
      title: '设备类型',
      dataIndex: 'deviceType',
    },
    {
      key: 'distance',
      align: 'center',
      title: '间距',
      dataIndex: 'distance',
    },
  ];

  return (
    <DetailPanelWrapper $visible={visible}>
      <div className="title-bar">基本信息</div>
      <Descriptions items={baseInfo} bordered size="small" />
    </DetailPanelWrapper>
  );
};

export default memo(DetailPanel);
