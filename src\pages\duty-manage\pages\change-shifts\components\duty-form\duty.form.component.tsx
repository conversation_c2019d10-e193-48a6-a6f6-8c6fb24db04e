import { Form, Input, DatePicker, Select, Row, Col } from 'antd';
import {
  DUTY_STATUS_LIST,
  RUN_VALUE_LIST,
  SHIFT_LIST,
  FORM_LAYOUT,
} from '@/pages/duty-manage/utils/contant';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { FormRef } from '@/pages/duty-manage/utils/typping';
import { useMount } from 'ahooks';
import UserSelectorComponent from '@/pages/duty-manage/components/user-selector/user-selector.component';

export default function DutyFormComponent() {
  const userSelectorRef = useRef<FormRef>(null);
  const [form] = Form.useForm();
  useMount(() => {
    if (userSelectorRef.current) {
      userSelectorRef.current.form = form; // web-component 组件
    }
  });
  return (
    <Form
      {...FORM_LAYOUT}
      form={form}
      initialValues={{
        dutyType: '值班日志',
        dutyStatus: DUTY_STATUS_LIST[0].value,
      }}
    >
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label="值班类型" name="dutyType">
            <Input placeholder="请输入值班类型" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="值班日期" name="dutyDate">
            <DatePicker
              placeholder="请选择值班日期"
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="日志状态" name="dutyStatus">
            <Select placeholder="请选择日志状态" options={DUTY_STATUS_LIST} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="值班班次" name="dutyShift">
            <Select placeholder="请选择值班班次" options={SHIFT_LIST} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label="值别" name="runValue">
            <Select placeholder="请选择值别" options={RUN_VALUE_LIST} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="值班负责人" name="dutyManager">
            <Input placeholder="请输入值班负责人" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="值班人员" name="dutyPersonnel">
            <Input placeholder="请输入值班人员" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <UserSelectorComponent
            callback={(users) => {
              form.setFieldsValue({
                dutyPersonnel: users?.join(',') || '',
              });
            }}
          />
        </Col>
      </Row>
    </Form>
  );
}
