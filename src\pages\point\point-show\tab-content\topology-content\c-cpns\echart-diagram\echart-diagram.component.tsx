import React, { memo, useEffect, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import * as echarts from 'echarts';

import { EchartDiagramWrapper } from './echart-diagram.style';
import graph from './data.json';

export function traverse(dictCode: string, nodes: any[]) {
  for (const node of nodes) {
    if (node.dictCode === dictCode) return node;
    if (node.children?.length) {
      const result: any = traverse(dictCode, node.children);
      if (result) return result;
    }
  }
  return null;
}

interface IProps {
  children?: ReactNode;
  nodes: any[];
  edges: any[];
  setSelectedBusinessId: React.Dispatch<React.SetStateAction<string>>;
  setCurrentNodeInfo: React.Dispatch<any>;
}

const EchartDiagram: FC<IProps> = (props) => {
  const { nodes, edges, setSelectedBusinessId, setCurrentNodeInfo } = props;

  const chartDomRef = useRef<any>(null);

  const nodeData = nodes.map((item) => {
    return {
      id: item.id,
      businessId: item.data.id,
      name: item.data.type,
      category: graph.categories.findIndex((category) => category.name === item.data.type),
      value: item.data.text,
      symbolSize: item.data.type === '当前测点' ? 30 : item.data.type === '测点' ? 20 : 10,
      // 初始化当前测点为默认选中节点，添加选中样式
      itemStyle:
        item.data.type === '当前测点'
          ? {
              borderColor: 'white', // 设置白色边框
              borderWidth: 3, // 设置边框宽度
              shadowBlur: 10,
              shadowColor: '#fff', // 白色阴影
            }
          : null,
    };
  });

  const linkData = edges.map((edge) => ({
    source: edge.startId,
    target: edge.endId,
    label: {
      show: false,
      position: 'middle',
      textStyle: { color: '#fff' },
      formatter: `${edge.distance}m`,
    },
  }));

  const option = {
    title: {
      text: '设备拓扑关系图',
      subtext: 'Default layout',
      top: 'bottom',
      left: 'right',
    },
    tooltip: {},
    legend: [
      {
        // selectedMode: 'single',
        data: graph.categories.map(function (a: { name: string }) {
          return a.name;
        }),
        textStyle: {
          color: '#fff',
        },
      },
    ],
    series: [
      {
        name: '节点',
        type: 'graph',
        layout: 'force',
        data: nodeData,
        links: linkData,
        categories: graph.categories,
        roam: true,
        label: {
          show: true,
          position: 'inside',
          textStyle: { color: '#fff' },
          formatter: function (params) {
            return params.data.value;
          },
        },
        force: {
          repulsion: 100,
        },
      },
    ],
  };

  useEffect(() => {
    return render();
  }, [nodes, edges]);

  const render = () => {
    // chart (re)render
    const myChart = echarts.init(chartDomRef.current);
    // 高亮（选中）根测点
    myChart.setOption(option);

    // event handler registry
    const clickCb = (params: any) => {
      if (params.dataType === 'node') {
        // 获取当前点击的节点ID
        const clickedNodeId = params.data.id;
        const graphData = option.series.map((item) => item.data).flat(1);

        // 更新详情面板业务id
        setSelectedBusinessId(params.data.businessId);

        // 更新操作表单所选节点内容
        setCurrentNodeInfo({
          id: params.data.id,
          type: params.data.name,
        });

        // 更新节点高亮
        for (let i = 0; i < graphData.length; i++) {
          graphData[i].itemStyle =
            graphData[i].id === clickedNodeId
              ? {
                  borderColor: 'white', // 设置白色边框
                  borderWidth: 3, // 设置边框宽度
                  shadowBlur: 10,
                  shadowColor: '#fff', // 白色阴影
                }
              : null; // 高亮点击的节点，其他节点恢复原状
        }

        // 更新图表
        myChart.setOption(option);
      }
    };

    myChart.on('click', clickCb);

    return () => {
      myChart.off('click', clickCb);
    };
  };

  return (
    <EchartDiagramWrapper>
      <div id="echart-diagram" ref={chartDomRef}></div>
    </EchartDiagramWrapper>
  );
};

export default memo(EchartDiagram);
