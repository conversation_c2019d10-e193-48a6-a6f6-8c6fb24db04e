import { createRoot } from 'react-dom/client';
import { Spin } from 'antd';

const style = {
  position: 'fixed',
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '20px',
  backgroundColor: 'rgba(0, 0, 0, 0.4)',
  zIndex: 9999,
};

function Loading({ tip = '' }: { tip?: string }) {
  return (
    <Spin tip={tip} size="large" className="global-loading">
      <span style={{ display: 'none' }}>welcome</span>
    </Spin>
  );
}

let count = 0;
export const showLoading = () => {
  if (count === 0) {
    const loading = document.createElement('div') as HTMLDivElement;
    loading.setAttribute('id', 'global-loading');
    Object.assign(loading.style, style); // 将样式应用到加载容器
    document.body.appendChild(loading);
    createRoot(loading).render(<Loading />);
  }
  count++;
};
export const hideLoading = () => {
  count--;
  if (count === 0) {
    document.body.removeChild(document.getElementById('global-loading') as HTMLDivElement);
  }
};
