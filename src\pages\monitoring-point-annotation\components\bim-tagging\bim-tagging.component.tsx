import { FC, MouseEvent, ReactNode, memo, useEffect, useRef, useState } from 'react';
import styles from './bim-tagging.component.less';
import testImg from '@/assets/images/tset.png';
import { PropOptions, TaggOptions, TaggParams } from './bim-tagging';
import { urlPrefix } from '@/utils/request';
import Icon from '@ant-design/icons';
import { ResponseOptions, addImageNode, baseUnitTree, delTreeNode, updateImageNode } from '@/services/monitoring-point-annotation';
import { message, Empty, Tooltip } from 'antd';
import { DelModal } from '@/components/del-modal/del-modal.component';
import { TreeOptions } from '../monitoring-point/monitoring-point';
import { TranslMove } from '@/utils/translMove';
export const Svg = (color: string) => () =>
  (
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4294" width="20" height="24">
      <path
        d="M512 938.666667c-53.333333 0-384-257.258667-384-469.333334S299.925333 85.333333 512 85.333333s384 171.925333 384 384-330.666667 469.333333-384 469.333334z m0-352c64.8 0 117.333333-52.533333 117.333333-117.333334s-52.533333-117.333333-117.333333-117.333333-117.333333 52.533333-117.333333 117.333333 52.533333 117.333333 117.333333 117.333334z"
        fill={color}
        p-id="4295"
      ></path>
    </svg>
  );
//红色1
export const RedImg = () => <Icon component={Svg('red')}></Icon>;
//橙色2
export const OrigImg = () => <Icon component={Svg('#FFA500')}></Icon>;
//绿色3
export const GreenImg = () => <Icon component={Svg('#8ee530')}></Icon>;
//蓝色4
export const BlueImg = () => <Icon component={Svg('#334ded')}></Icon>;
//标注图片
export const PosImg = () => <Icon component={Svg('#f08228')}></Icon>;
// 当前标注图片
export const CurPosImg = () => <Icon component={Svg('#852601')}></Icon>;
//TranslMove实例
let trans: TranslMove | null = null;
export const BimTagging: FC<PropOptions> = memo(({ delChange, taggChange, pointData, nodeData }) => {
  //标注状态
  const [taggStatus, setTaggStatus] = useState<boolean>(false);
  //提交状态
  const [loading, setLoading] = useState<boolean>(false);
  //标注点信息
  const [taggData, setTaggData] = useState<TaggOptions>();
  //删除弹框
  const [delModalShow, setDelModalShow] = useState<boolean>(false);
  //其余标注点列表,[x,y]
  const [otherTaggList, setOtherTaggList] = useState<TaggOptions[]>([]);
  //图片容器
  const imgRef = useRef<any>(null);
  //父容器
  const parentRef = useRef<any>(null);
  useEffect(() => {
    //esc关闭标注
    document.addEventListener('keydown', function (event) {
      if (event.key === 'Escape') {
        closeTaggStatus();
      }
    });
    //鼠标右键关闭标注
    document.addEventListener('contextmenu', function (event) {
      event.preventDefault(); // 阻止默认右键菜单
      closeTaggStatus();
    });

    return () => {
      document.removeEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
          closeTaggStatus();
        }
      });
      document.removeEventListener('contextmenu', function (event) {
        event.preventDefault(); // 阻止默认右键菜单
        closeTaggStatus();
      });
    };
  }, []);
  useEffect(() => {
    closeTaggStatus();
    updateOtherTaggList();
    if (trans) {
      //重置
      trans.resetFit();
    }
    if (nodeData.imgId && imgRef.current) {
      trans = new TranslMove(imgRef.current, parentRef.current);
      trans.zoomEvent();
    }
  }, [nodeData, pointData]);
  useEffect(() => {
    taggDataInit();
  }, [pointData]);
  //当前标注点数据初始化
  function taggDataInit(): void {
    const { id, xcoordinate, ycoordinate, name } = pointData;
    if (xcoordinate >= 1 && ycoordinate >= 1) {
      setTaggData({ id, x: xcoordinate, y: ycoordinate, name });
    } else {
      setTaggData(undefined);
    }
  }
  //更新其余标注点列表
  function updateOtherTaggList(): void {
    if (nodeData.level === 1) {
      if (nodeData.children && nodeData.children.length > 0) {
        const list = otherTaggListHandler(nodeData.children);
        setOtherTaggList(list);
      } else if (nodeData.children == undefined) {
        getTaggListRequest();
      } else {
        setOtherTaggList([]);
      }
    }
  }
  //获取标注列表请求
  function getTaggListRequest(): void {
    baseUnitTree<TreeOptions[]>({ parentId: nodeData.id }).then((res) => {
      if (res.code === '1') {
        const list = otherTaggListHandler(res.data);
        setOtherTaggList(list);
      }
    });
  }
  //其余标注点列表数据处理
  function otherTaggListHandler(val: TreeOptions[]): TaggOptions[] {
    //过滤出有标注点的测点，然后组装列表
    const list = val
      .filter((item) => {
        //筛选出当前选中的标注点
        return item.xcoordinate >= 1 && item.ycoordinate >= 1 && item.id !== pointData.id;
      })
      .map((item) => {
        return {
          x: item.xcoordinate,
          y: item.ycoordinate,
          id: item.id,
          name: item.name,
        };
      });
    return list;
  }
  //标注点数据处理
  function taggDataHandler(): TaggParams {
    const { x, y } = taggData ? taggData : { x: 0, y: 0 };
    return {
      xcoordinate: x,
      ycoordinate: y,
      id: pointData.id,
    };
  }
  //更新标注
  function updateTagg(): void {
    delChange();
    taggChange();
    setTaggData(undefined);
    setDelModalShow(false);
  }
  //提交标注点位
  function commitPoint(): void {
    setLoading(true);
    const data = taggDataHandler();
    //标注点关联在测点上，所以只需要更改测点的标注点信息就行了。
    updateImageNode<TaggParams>(data, data.id)
      .then((res) => {
        if (res.code == '1') {
          //给标注添加id，用于后面删除
          taggData && setTaggData({ ...taggData, id: data.id });
          taggChange();
          message.success(res.message || '添加成功');
        } else {
          message.error(res.message || '添加失败');
        }
        setLoading(false);
        closeTaggStatus();
      })
      .catch((err) => {
        closeTaggStatus();
      });
  }
  //关闭标注状态
  function closeTaggStatus(): void {
    setTaggStatus(false);
  }
  //标注点删除请求
  function delTaggRequest(): Promise<ResponseOptions<any>> {
    //标注点关联在测点上，所以只需要删除测点的标注点信息就行了。
    return updateImageNode<TaggParams>({ parentId: pointData.parentId, xcoordinate: -1, ycoordinate: -1, id: pointData.id }, pointData.id);
  }
  //标注删除处理
  function delTaggHandler(): void {
    if (taggData && taggData.id) {
      setDelModalShow(true);
    } else {
      //还没有添加的数据假删
      setTaggData(undefined);
    }
  }
  //标注点位
  function taggPoint(e: MouseEvent<HTMLElement>): void {
    if (taggStatus) {
      //缩放比例
      const scale: number = trans ? parseFloat(trans.scale) : 1;
      //点击元素
      const target: any = e.target;
      //元素边界信息
      const parentData = target.parentNode.getBoundingClientRect();
      // 点击的屏幕位置 - 父元素屏幕位置 / 缩放比例 = 点击位置在父元素的位置
      const x: number = (e.clientX - parentData.left) / scale;
      const y: number = (e.clientY - parentData.top) / scale;
      setTaggData({
        x,
        y,
        name: pointData.name,
      });
    }
  }
  //控制模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['bim-taggin-container-controller']}>
        {taggData && taggStatus ? (
          <type-button loading={loading} onClick={commitPoint}>
            确定标注
          </type-button>
        ) : null}
        {!taggStatus ? (
          <type-button loading={loading} onClick={() => setTaggStatus(true)}>
            开始标注
          </type-button>
        ) : null}
        {taggData ? (
          <type-button loading={loading} onClick={delTaggHandler} type="danger">
            删除
          </type-button>
        ) : null}
      </div>
    );
  }
  //当前标注点模板
  function curTaggComponent(): ReactNode {
    if (taggData) {
      return (
        <Tooltip placement="top" title={'当前标注点-' + taggData.name}>
          <div
            style={{
              left: taggData.x - 10,
              top: taggData.y - 12,
            }}
            className={styles['bim-taggin-container-bim-point']}
          >
            <CurPosImg />
          </div>
        </Tooltip>
      );
    } else {
      return <></>;
    }
  }
  //其余标注点模板
  function otherTaggComponent(): ReactNode {
    return otherTaggList.map((item, index: number) => {
      return (
        <Tooltip key={index} placement="top" title={item.name}>
          <div
            style={{
              left: item.x - 10,
              top: item.y - 12,
            }}
            className={styles['bim-taggin-container-bim-point']}
          >
            <PosImg />
          </div>
        </Tooltip>
      );
    });
  }
  //空数据占位模板
  function emptyComponent(): ReactNode {
    return (
      <div className={styles['bim-taggin-container-empty']}>
        <Empty
          description={<span className={styles['bim-taggin-container-empty-text']}>请选择机组下的设备图片展示</span>}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }
  return (
    <section className={styles['bim-taggin-container']}>
      {/* 空数据提示 */}
      {nodeData.imgId ? null : emptyComponent()}
      {/* 标注提示 */}
      {taggStatus ? (
        <div className={styles['bim-taggin-container-tips']}>
          <span>点击鼠标左键在对应位置上进行标注，右键或Esc退出</span>
        </div>
      ) : null}
      {/* 操作区域 */}
      {pointData.pointCode ? controllerComponent() : null}
      <div ref={parentRef} className={styles['bim-taggin-container-bim']}>
        {nodeData.imgId ? (
          <div
            ref={imgRef}
            className={`${styles['bim-taggin-container-bim-img']} ${taggStatus ? styles['bim-taggin-container-bim-tagg'] : null}`}
          >
            {/* 当前设备图片 */}
            <img onClick={taggPoint} src={urlPrefix + '/Attachment/downloadAttachment/' + nodeData.imgId} />
            {/* 当前标注点 */}
            {curTaggComponent()}
            {/* 其余标注点 */}
            {otherTaggComponent()}
          </div>
        ) : null}
      </div>
      {/* 删除标注弹框 */}
      <DelModal
        delFn={delTaggRequest}
        onOk={updateTagg}
        tipsText={'是否删除 “' + pointData.name + '” 标注？'}
        open={delModalShow}
        close={() => setDelModalShow(false)}
      ></DelModal>
    </section>
  );
});
