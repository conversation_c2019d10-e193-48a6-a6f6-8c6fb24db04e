import { forwardRef, memo, useState, useImperativeHandle, useRef, useMemo } from 'react';
import { Form, Input, Space, Button } from 'antd';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { useMount } from 'ahooks';
import { FileOptions } from '@/services/system';
import { PointAnalysis } from '@/pages/point/type';
interface IProps {
  callback: (params: PointAnalysis.Add) => Promise<void>;
}
interface IRef {
  openModal: (type?: 'create' | 'edit', formData?: PointAnalysis.Add) => void;
}
const NewPointModal = forwardRef<IRef, IProps>((props, ref) => {
  const { callback } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [openType, setOpenType] = useState<'create' | 'edit'>('create');
  const [form] = Form.useForm();
  const fileRef = useRef<{
    onChange: (attachmentId: string, fileList: FileOptions) => void;
    sureDelFile: () => void; // 确定删除已经被关闭的文件，一般用于提交时，删除之前上传的文件
    initDelFile: () => void; // 删除除了初始文件的所有文件，一般用于未提交时，删除多余的上传文件
  }>();
  const title = useMemo(() => {
    if (openType === 'create') {
      return '新增';
    }
    return '编辑';
  }, [openType]);

  useImperativeHandle(ref, () => ({
    openModal,
  }));

  function openModal(type: 'create' | 'edit' = 'create', formData?: PointAnalysis.Add) {
    if (type === 'edit') {
      form.setFieldsValue({
        ...formData,
      });
    }
    setOpenType(type);
    setVisible(true);
  }

  useMount(() => {
    if (fileRef.current) {
      fileRef.current.onChange = fileOnChange;
    }
  });

  /**
   * 文件上传回调
   * @param attachmentId 文件目录id
   * @param fileList 文件列表
   */
  function fileOnChange(attachmentId: string, _fileList: FileOptions) {
    form.setFieldsValue({
      thumbnailId: attachmentId,
    });
  }
  // 关闭
  const close = () => {
    setVisible(false);
    form.resetFields();
  };
  // 取消
  const handleCancel = () => {
    close();
    fileRef.current?.initDelFile(); // 删除除了初始文件的所有文件，一般用于未提交时，删除多余的上传文件
  };
  // 新增确定
  const handleFinish = async () => {
    const valid = await form.validateFields();
    if (valid) {
      const param = form.getFieldsValue();
      callback(param).then(() => {
        close();
        fileRef.current?.sureDelFile(); // 删除上传的多余文件
      });
    }
  };

  return (
    <DragModal
      title={title}
      open={visible}
      onCancel={handleCancel}
      destroyOnClose // 关闭时销毁 Modal 里的子元素
      width={500}
      mask={true}
      centered
      forceRender // 强制渲染 Modal
      footer={false}
    >
      <Form form={form}>
        <Form.Item label="名称" name="name" rules={[{ required: true, message: '请输入名称' }]}>
          <Input type="text" placeholder="请输入名称" />
        </Form.Item>
        <Form.Item label="上传封面图" name="thumbnailId">
          <upload-file
            ref={fileRef}
            maxcount={1}
            format={['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']}
          />
        </Form.Item>
        <Form.Item
          label=""
          labelCol={{ span: 0 }}
          wrapperCol={{ span: 24 }}
          style={{ textAlign: 'right' }}
        >
          <Space size={10}>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={handleFinish}>
              确定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </DragModal>
  );
});

export default memo(NewPointModal);
