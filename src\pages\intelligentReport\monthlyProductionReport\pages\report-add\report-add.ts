import { InfluxesEnums } from '@/components/spread-table/spread-table';
import { PlanTypeEnum } from '@/services/intelligentReport/report-plan';

export interface FormOptions {
  monthlyName: string;
  year: any;
}

//属性值配置
export interface AttributeOptions {
  attributeId: string;
  attributeName: string;
  value: string;
  planCycle: PlanTypeEnum;
}
//历史值配置
export interface HistoryOptions {
  rowCount: string;
  columnCount: string;
  planCycle: string;
  date: string;
  sheetId: string;
  type: string;
  value: string;
}
//历史值配置
export interface InfluxesOptions {
  key: string; //测点code
  startTime: string;
  endTime: string;
  dataType: InfluxesEnums;
  influxValue: number;
  switchValue: number;
  type: 'max' | 'min';
}
