.equip-type-tab-component-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
  z-index: 1;

  &-tab {
    position: relative;
    z-index: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;

    &-item {
      font-family: 'DingTalk';
      user-select: none;
      cursor: pointer;
      width: 120px;
      height: 34px;
      color: #000;
      text-align: center;
      line-height: 32px;
      background: rgba(17, 80, 153, 0.34);
      border-radius: 0px;
      border: 0px solid #2964ad;
      font-size: 16px;
      border-radius: 2px;
      border: 1px solid transparent;
      transition: all 0.3s;
    }
    &-other {
      color: #fff;
    }
    &-item-warning {
      color: #ee0505;
      border-color: #f01818;
    }
    &-item-active {
      color: #fff;
      border-image: linear-gradient(to right, #1a5b7e, #36e5ff, #1a5b7e) 1;
      box-shadow: inset 0 0 7px #1f95d3;
    }
  }

  &-warning {
    position: absolute;
    height: 100%;
    left: 340px;
    right: 110px;
    top: 0px;
    bottom: 0px;
    z-index: -1;

    &-tab {
      right: 13px;
      position: absolute;
      bottom: -40px;
      display: flex;
      align-items: center;
      background: rgba(30, 78, 134, 0.5);
      border-radius: 2px;
      padding: 6px 10px;
      gap: 11px;

      p {
        margin: 0;
        padding: 0;
        display: flex;
        gap: 2px;
      }

      &-item {
        color: #1a1a1a;
        font-size: 12px;
        display: inline-block;
        padding: 4px 7px;
      }
    }
  }
}
