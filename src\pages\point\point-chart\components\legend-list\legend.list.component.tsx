import React, { memo } from 'react';
import { Spin } from 'antd';
import Empty from '@/components/empty-component/empty-component';
import styles from './legend.list.component.less';
import { But<PERSON>, Modal } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  ShareAltOutlined,
  StarOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons';
import { history } from 'umi';
import { PointAnalysis } from '@/pages/point/type';
import _ from 'lodash';
import {
  handleCollect,
  handleDelete,
  handleCancelShare,
  handleCancelShareWithMe,
} from '@/pages/point/point-chart/server';
import { urlPrefix } from '@/utils/request';
interface IProps {
  list: PointAnalysis.List[];
  loading: boolean;
  currentTab: string;
  getList: () => Promise<void>;
  handleOpenUserSelector: (id: string) => void;
}

const LegendList: React.FC<IProps> = ({
  list,
  loading,
  currentTab, // 当前tab
  getList,
  handleOpenUserSelector,
}) => {
  const activeStyle = {
    color: '#0AD0EE',
  };

  const showEdit = ['myLegends', 'myShares', 'myFavorites'].includes(currentTab); // 可编辑
  const showDel = ['myLegends'].includes(currentTab); // 可删除
  const showShare = ['myLegends', 'myShares'].includes(currentTab); // 可分享
  const showFavorite = ['myLegends', 'myCollections'].includes(currentTab); // 可收藏

  // 点击收藏
  const collect = async (item: PointAnalysis.List) => {
    const params = {
      optionId: item.id,
      myFavorites: item.myFavorites === 1 ? 0 : 1,
    };
    await handleCollect(params, getList);
  };

  // 点击分享
  const handleShare = async (item: PointAnalysis.List) => {
    const { id, myShare } = item;
    if (_.isEqual(currentTab, 'sharesToMe')) {
      return handleCancelShareWithMe({ id }, getList); // 取消分享给我
    } else if (myShare === 1) {
      return handleCancelShare({ id }, getList); // 取消分享
    } else {
      handleOpenUserSelector(id); // 分享
    }
  };

  // 点击删除图标
  const deleteItem = (item: PointAnalysis.List) => {
    Modal.confirm({
      title: '温馨提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确定删除此项?',
      okText: '确定',
      cancelText: '取消',
      onOk: () => handleDelete(item.id, getList),
    });
  };

  // 查看或编辑
  const handleView = (id: string, type: string) => {
    history.push(`/pointAnalysis?id=${id}&type=${type}`);
  };

  // 加载中
  if (loading)
    return (
      <Spin
        size="large"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: 'calc(100vh - 265px)',
        }}
        spinning={loading}
      />
    );

  // 空数据
  if (list.length === 0) return <Empty />;

  // 渲染图标
  const renderIcon = (item: PointAnalysis.List) => {
    const { myFavorites, myShare, shareWithMe } = item;
    const activeShare = _.isEqual(myShare, 1) || _.isEqual(shareWithMe, 1);
    return (
      <header className={styles['top-icons']}>
        {showFavorite ? (
          <StarOutlined
            className={styles.icon}
            style={_.isEqual(myFavorites, 1) ? activeStyle : {}}
            onClick={() => collect(item)}
          />
        ) : null}
        {showShare ? (
          <ShareAltOutlined
            style={activeShare ? activeStyle : {}}
            className={styles.icon}
            onClick={() => handleShare(item)}
          />
        ) : null}
        {showDel ? (
          <DeleteOutlined className={styles.icon} onClick={() => deleteItem(item)} />
        ) : null}
      </header>
    );
  };

  // 渲染底部按钮
  const renderFooter = (item: PointAnalysis.List) => {
    return (
      <footer className={styles['center-buttons']}>
        <Button
          type="primary"
          icon={<EyeOutlined />}
          onClick={() => handleView(item.id, 'check')} // check 查看
        >
          查看
        </Button>
        {showEdit ? (
          <Button
            ghost // 幽灵按钮
            icon={<EditOutlined />}
            onClick={() => handleView(item.id, 'edit')} // edit 编辑
          >
            编辑
          </Button>
        ) : null}
      </footer>
    );
  };
  return (
    <main className={styles['list-container']}>
      {list.map((item, index) => {
        const { id, name, createTime, thumbnailContentList } = item;
        const imgUrl = thumbnailContentList?.[0]?.id || '39537';
        return (
          <article
            className={styles['list-wrapper']}
            key={id}
            style={
              {
                '--i': Math.floor(index / 4),
              } as React.CSSProperties
            }
          >
            <div
              className={styles['overlay-wrapper']}
              style={{
                backgroundImage: `url(${urlPrefix}/Attachment/downloadAttachment/${imgUrl})`,
              }}
            >
              <nav className={styles.overlay}>
                {renderIcon(item)}
                {renderFooter(item)}
              </nav>
            </div>
            <aside>
              <h3 className={styles.tlt}>{name}</h3>
              <time className={styles.date}>{createTime}</time>
            </aside>
          </article>
        );
      })}
    </main>
  );
};

export default memo(LegendList);
