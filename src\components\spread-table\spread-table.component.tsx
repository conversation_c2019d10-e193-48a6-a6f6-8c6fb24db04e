import { FC, forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './spread-table.component.less';
import {
  ActiveCellOptions,
  CellControllerOptions,
  CellOptions,
  CellRangeOptions,
  MessageEnum,
  MessageOptions,
  PropOptions,
  UseImperativeOptions,
  WorkTableNameChangeOptions,
  hiddenMenuList,
} from './spread-table';
import '@grapecity-software/spread-sheets-designer/styles/gc.spread.sheets.designer.min.css';
import '@grapecity-software/spread-sheets/styles/gc.spread.sheets.excel2013white.css';
import * as GC from '@grapecity-software/spread-sheets';
import '@grapecity-software/spread-sheets-resources-zh';
import '@grapecity-software/spread-sheets-designer-resources-cn';
import '@grapecity-software/spread-sheets-designer';
import * as Excel from '@grapecity-software/spread-excelio';
import { getRangeCells, toExcelCoordinate } from '@/utils/utils';
import { useSyncCallback } from '@/hooks';
import { Spin, message } from 'antd';

const GCC: any = GC;
const SpreadExcel: any = Excel;
let designers: any = null;
// let designerConfig:any = null
GCC.Spread.Sheets.LicenseKey = SpreadExcel.LicenseKey =
  'yqyw.hhsd.com.cn,E243958947719799#B13JZLBhVVvlEdRl7VYxGUoVVdwtURlVTM0dXWjFFRhlzZpljZrJWUhNFd5lGN6gDRNhnaSVDZMdDcS3WMsRDWyQ7TOtiRxolRKRlWEZ6LsN4NPp4T5MlUkFGcwU4Uu5kN9kHONx6N6EEN9N4NUhzUZBHUIt4NLJ5Kl3GVBJTeo9GekFTNzM6SSdEMFZkbWRXR6cXaVhUQrIXN5tCTBBVO9gHcrNjWSVlRHVnT6VzT4UmdLtSOzJGOxJ6TxdUNX9WTnx6KzQkN9JkZVJFakdmRYdWZxYlZxhESO34MEJVQslXNwFGRMB7KkhWWNZTaJB7M6t6N4cTNOtmUKBlZyxkQpVzU5k6aTJzcnpkSD3WRNZVV7VzKrEGUKdlM5lUTKVTOxhzNDFEcKlURn3ybwVlI0IyUiwiIDZkQENEMFNjI0ICSiwSMxIDNyITM7cTM0IicfJye#4Xfd5nIIlkSCJiOiMkIsICOx8idgMlSgQWYlJHcTJiOi8kI1tlOiQmcQJCLiIjMwITMwACNyYDM5IDMyIiOiQncDJCLiQjM7ATNyAjMiojIwhXRiwiIuNmLt36YuQ6cohmL7lXc9JiOiMXbEJCLig1jlzahlDZmpnInmLampbbqnTJonHqro1rrovYtmj9ilfJjl/bpoL0mlb8mpr1ulXLln71ml7KukLiOiEmTDJCLlVnc4pjIsZXRiwiI9kzN9EzN7QTO8UTOzQjMiojIklkIs4XXiQXZlh6U4RnbhdkIsICdyFGaDFGdhRkIsICdlVGaTRncvBXZSJCLiUGbiFGV43mdpBlIbpjInxmZiwSZzxWYmpjIyNHZisnOiwmbBJye0ICRiwiI34TU0NjSvhzZZZ7TFh4M4EEcuJXVwVlYDRUehNnbTNWOJV6SU5GbupON';
//'172.168.10.79,E382574113236448#B1XSfVVTvQUeaJTY7ZnUZhkTo5kaupVeLBzLRRlZ9FWMIZ4QEZ7RKxUVtdUOpVUa7IGS6tCarFXVYBHbrd4Szk5T4EzQ4sWethEOxkTe6UESmF5ZFxEamNjeTJXdvpEd5oFe7hjYONHO5sGMpR6UhVUV4oURlh6QOtEWshlVwUEV9omMvpXcvZ5akRnNFJ7Vqd4LjFzY5knMvYGcxcGWYhlS7NWO7QHM0N6cJtWcwtWY6ZVYBRWT5FkaVVjUvJUSzsWYtl4cSpWcrZTQ0pHVQhWStlUMSpUOChmbqh6blRFOOpWYwAlQk94aGl4b7Y5RLhVcod4ZBFVS6JGbiJjV0hlQDx4dP5EMTt6UiojITJCLiIzMyEjNxQ4NiojIIJCL8QTO4cjN9MTN0IicfJye=#Qf35VfigUSKJkI0IyQiwiI8EjL6ByUKBCZhVmcwNlI0IiTis7W0ICZyBlIsISNyUDMzADIxITNwUjMwIjI0ICdyNkIsICOyUDM5IDMyIiOiAHeFJCLikzNuATMugjNx8iM7EjI0IyctRkIsICuPWOrFWOkZmeica+rcaOgKa+rBaeo/S+kYa+gNWevDmOkIauI0ISYONkIsUWdyRnOiwmdFJCLigDN4YzMyMTMxQzN5IDOzIiOiQWSiwSfdtlOicGbmJCLlNHbhZmOiI7ckJye0ICbuFkI1pjIEJCLi4TPBBTeEh4SGlXRRdjRDNmZ8p7dVhnSRFzQFF6dONlSaVTOvdkZUtUe5QFWvM4Uq3kdL5GaNFHM0hlRmhldRljbMtydyNzM9x4SpFIS3R';
GCC.Spread.Sheets.Designer.LicenseKey =
  'yqyw.hhsd.com.cn,E377174381365444#B1LcYRBh7Z7oFMWp7VZhmNzRmYM34VJx6LR3ib8Z5LJVjWlZjVHN5d8NlRUZ5VQh6amZEdxQ5QEtiN0dVUBtiSHVUV8dTc5RHSX3kTO3iaQl5VzV6YJpGbFNTRNpnRYFla84UeYVEaJpHVMNFOStUbvNVWqlXYUh5ZMlkc4gFbZdUazxme9YUcVhUMMNGcSdjR4cTZ4QEaltmdnV6NsFTNzVzQ4gVY8hWUnJUWHRHM9lnZlNlV7E6ch5kYzk5UzBVaxdGUU5kTqNUcSZWNhdWTHVTc5IFRYJHVGZDRGd6RjJHawBlS9A7KPVna8dkZKR4KWhkeQlncxUHcYRVRFNHN63COGZ7LkJFVQ36TyZVYiojITJCLiMDR6cDO5Y4NiojIIJCL9ATOwcTOxEjN0IicfJye=#Qf35VfikTWzMjI0IyQiwiI8EjL6BibvRGZB5icl96ZpNXZE5yUKRWYlJHcTJiOi8kI1tlOiQmcQJCLiMjMwITMwACNyYDM5IDMyIiOiQncDJCLiQjM7ATNyAjMiojIwhXRiwiIuNmLt36YuQ6cohmL7lXc9JiOiMXbEJCLig1jlzahlDZmpnInmLampbbqnTJonHqro1rrovYtmj9ilfJjl/bpoL0mlb8mpr1ulXLln71ml7KukLiOiEmTDJCLlVnc4pjIsZXRiwiI4QDN5YzMxgzM4cTM7czMiojIklkI1pjIEJCLi4TPB9kYndVSIV4VyJjb5U7KL3URPNUd4p4UudVWz2ybKljavdFaTFWenRXRUNEeyAVcOVjVYdTbwR7Q6RmbCVHdBBnYWl6MJZkQ';
//'172.168.10.79,E667559332123412#B1thwZ7cVOJZXaFdHaPhUQJdmb4YEe9ITdo3CZttWc8clZoNzZX3iNwk5c8pUWvInYh3EN8RWUxEjVRNEVQllQlNHSJVmV48GVtJ5UJdVan9kakJkd5kVMpJWV5NWROZFRDRzTvZ6ZN5WO9QEZl54TSdkeaZ6T6wkd4xEOwRTdqF4SGJXZsBVMxMTVOJHUCVGdI3iMsFjWCZUbMVDbrx4KMdjV4cjViNGUOVHavUURDdkSGZXMvM7NChkZS5UZ59WUURFb9YWbMVTMMxUOyZ7c95WO5E6ZIh6UJlEWk5WarQ5Nr8WYLNEbnpHWYVUbUlmQygjSVZ5dQJla4UWOWxmMklUWiojITJCLiEDREVkMERkMiojIIJCL9kTN9MTM8gDN0IicfJye=#Qf35VfikTWzMjI0IyQiwiI8EjL6BibvRGZB5icl96ZpNXZE5yUKRWYlJHcTJiOi8kI1tlOiQmcQJCLiYjM5AzMwASMyUDM5IDMyIiOiQncDJCLigjM5ATNyAjMiojIwhXRiwiI9cjLwEjL8YTMuIzNxIiOiMXbEJCLig1jlzahlDZmpnInm/KnmDoim/agmH0vkPJmmPYjl71gpDJimLiOiEmTDJCLlVnc4pjIsZXRiwiIyEDNzITMyMzM9UTN7YjNiojIklkI1pjIEJCLi4TPBZnSycWbRhTT8ZTerkjZmt4NIdmTjhDWx2iZ0dWOzJVZWhzShlVYpd4aNFDexZjd85GdjNlY5YHSrEGTXNEVaRkazJ7YElTSrQEOwoWMCZWRZhJM';
export const SpreadTable: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        cellMenu = [],
        createInit,
        workTableChange,
        isWookTable = false,
        disabled = true,
        isRibbonCollapse = false,
        isHiddenFileMenu = true,
        messageOptions = MessageEnum,
        workTableNameChange,
        cellClick,
        fileImportEnd = () => {},
      },
      ref,
    ) => {
      const [spreadInit, setSpreadInit] = useState<any>();
      const [designerInit, setDesignerInit] = useState<any>();
      const [designerConfig, setDesignerConfig] = useState<any>();
      const [loading, setLoading] = useState<boolean>(false);
      const messageTooltip = useRef<MessageOptions>(messageHandler());
      const designerRef = useRef<any>();
      const fileRef = useRef<any>();
      const createInitSync = useSyncCallback(createInit);
      //判断工作表新增状态
      const isAddingSheet = useRef(false);
      useImperativeHandle<any, UseImperativeOptions>(ref, () => ({
        GC,
        designerInit,
        liftWorkTableProtected,
        cellImgChange,
        importFile,
        exportFile,
        getAllWorkTable,
        cellBackChange,
        importJSON,
        cellIsMerged,
        setActiveTableIndex,
        setActiveSheet,
        hiddenTableMenu,
        tableCheckRangeEditHandler,
        getWorkData,
        getWorkTableData,
        getActiveCell,
        getCellRange,
        setTableCellValue,
        verticalPositionTop,
      }));
      useEffect(() => {
        if (isWookTable) {
          createWookTable();
        } else {
          createTable();
        }
        return () => {
          designers && designers.destroy();
          designers = null;
        };
      }, []);
      useEffect(() => {
        workTableDisabledHandler();
      }, [disabled]);
      //消息提示处理
      function messageHandler(): MessageOptions {
        const fields = Object.keys(MessageEnum);
        const data = JSON.parse(JSON.stringify(MessageEnum));
        fields.forEach((item) => {
          if (messageOptions[item]) {
            data[item] = messageOptions[item];
          }
        });
        return data;
      }
      //创建工作薄
      function createWookTable(): void {
        const spread = new GCC.Spread.Sheets.Workbook(designerRef.current);
        designers = spread;
        setTableDefaultOptions();
        workTableChangeEvent(spread);
        workTableInvalidEvent(spread);
        cellClickEvent(spread);
        workTableTabEvent(spread);
        workTableNameEvent(spread);
        setSpreadInit(spread);
        createInitSync(spread);
      }
      //创建表格+工具栏
      function createTable(): void {
        //获取表格配置
        const designerConfig = addCellMenu();
        const designer = new GCC.Spread.Sheets.Designer.Designer(
          designerRef.current,
          designerConfig,
        );
        designer.setData('isRibbonCollapse', isRibbonCollapse);
        const spread = designer.getWorkbook();
        designers = designer;
        setTableDefaultOptions();
        workTableChangeEvent(spread);
        workTableInvalidEvent(spread);
        cellClickEvent(spread);
        workTableTabEvent(spread);
        workTableNameEvent(spread);
        setDesignerInit(designer);
        setDesignerConfig(designerConfig);
        createInitSync(designer);
        isHiddenFileMenu && hiddenFileMenu();
      }
      //隐藏菜单文件项
      function hiddenFileMenu(): void {
        setTimeout(() => {
          const dom: any = document.getElementsByClassName('fileMenuButton')[0];
          //隐藏文件选项
          dom && (dom.style.display = 'none');
        }, 0);
      }
      //单元格点击事件
      function cellClickEvent(spread: any): void {
        spread.bind(GC.Spread.Sheets.Events.CellClick, function (e: any, info: any) {
          let { row, col, sheet } = info;
          let isLock = sheet.getCell(row, col).locked();
          if (isLock) {
            sheet.options.protectionOptions.formatCells = false;
          } else {
            sheet.options.protectionOptions.formatCells = true;
          }
          if (cellClick) {
            const position = toExcelCoordinate(row, col);
            cellClick({
              row,
              col,
              position: position,
            });
          }
          designers.refresh();
        });
      }
      //工作表编辑状态操作
      function workTableDisabledHandler(): void {
        if (designerInit || spreadInit) {
          const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
          //右键菜单
          spread.options.allowContextMenu = disabled;
          //新增表格
          spread.options.newTabVisible = disabled;
          //表格标签
          spread.options.tabEditable = disabled;
          spread.sheets.forEach((item: any) => {
            // 设置表单保护
            item.options.isProtected = !disabled;
          });
          disabledMenuItem(!disabled);
        }
      }
      //工作表名称修改监听
      function workTableNameEvent(spread: any): void {
        spread.bind(
          GC.Spread.Sheets.Events.SheetNameChanged,
          function (sender: any, info: WorkTableNameChangeOptions) {
            if (workTableNameChange) {
              workTableNameChange(info);
            }
          },
        );
      }
      //工作薄无效操作监听
      function workTableInvalidEvent(spread: any): void {
        spread.bind(GC.Spread.Sheets.Events.InvalidOperation, function (sender: any, info: any) {
          //invalidType操作类型值
          const { invalidType, message: msg } = info;
          if (invalidType === 8) {
            message.warning(msg);
          } else if (invalidType === 13) {
            setTimeout(() => {
              const mask = document.getElementsByClassName('gc-designer-dialog-overlay')[0];
              if (mask) {
                const modal = document.getElementsByClassName('gc-designer-dialog')[0];
                mask.remove();
                modal.remove();
              }
              message.warning(messageTooltip.current.disabledEditCell);
            }, 0);
          }
        });
      }
      //监听工作表选项卡事件
      function workTableTabEvent(spread: any): void {
        // 监听工作表添加事件
        spread.bind(GC.Spread.Sheets.Events.SheetTabClick, function (sender: any, info: any) {
          const { sheetTabIndex } = info;
          if (sheetTabIndex === -1) {
            //新增
            isAddingSheet.current = true;
          } else {
            isAddingSheet.current = false;
          }
        });
      }
      //工作表切换事件
      function workTableChangeEvent(spread: any): void {
        spread.bind(GC.Spread.Sheets.Events.ActiveSheetChanging, function (sender: any, info: any) {
          if (info.oldSheet.options.isProtected) {
            if (info.newSheet && isAddingSheet.current === true) {
              info.cancel = true;
              //保护状态新增就删掉
              const index = spread.getSheetIndex(info.newSheet.name());
              spread.removeSheet(index);
              message.warning(messageTooltip.current.disabledAddTable);
            } else if (workTableChange) {
              workTableChange(info);
            }
          } else if (workTableChange) {
            workTableChange(info);
          }
        });
      }
      //设置当前工作表索引
      function setActiveTableIndex(index: number): void {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        //工作表数量
        const len = spread.getSheetCount();
        if (index <= len - 1) {
          spread.setActiveSheetIndex(index);
          spread.startSheetIndex(index);
        }
      }
      //设置表格默认配置
      function setTableDefaultOptions(): void {
        //语言包使用"zh-cn"
        GCC.Spread.Common.CultureManager.culture('zh-cn');
        const res = GCC.Spread.Sheets.Designer.getResources();
        //修改错误信息提示弹框标题
        res.title = '信息提示';
        GCC.Spread.Sheets.Designer.setResources(res);
      }
      //新增右键单元格菜单
      function addCellMenu(): any {
        const designerConfig = JSON.parse(JSON.stringify(GCC.Spread.Sheets.Designer.DefaultConfig));
        if (cellMenu.length > 0) {
          const menuData: any = {};
          cellMenu.forEach((item) => {
            menuData[item.text] = item;
          });
          designerConfig.commandMap = menuData;
          const keyNames = Object.keys(menuData);
          designerConfig.contextMenu.push(...keyNames);
        }
        return designerConfig;
      }
      //禁用菜单部分工具栏
      function disabledMenuItem(isProtected: boolean): void {
        if (designerConfig) {
          const designerConfigs = JSON.parse(JSON.stringify(designerConfig));
          const isDiabled = isProtected ? 'IsInTableSheetDesignMode' : '!IsInTableSheetDesignMode';
          designerConfigs.commandMap = {
            ...designerConfigs.commandMap,
            cellsInsert: {
              //开始-插入
              commandName: 'cellsInsert',
              enableContext: isDiabled,
            },
            cellsDelete: {
              //开始-删除
              commandName: 'cellsDelete',
              enableContext: isDiabled,
            },
            cellsFormat: {
              //开始-格式
              commandName: 'cellsFormat',
              enableContext: isDiabled,
            },
          };
          setDesignerConfig(designerConfigs);
          designerInit.setConfig(designerConfigs);
          isHiddenFileMenu && hiddenFileMenu();
        }
      }
      //设置工作表保护状态
      function liftWorkTableProtected(isProtected: boolean): void {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        spread.sheets.forEach((item: any) => {
          item.options.isProtected = isProtected;
        });
        spread.options.newTabVisible = !isProtected;
        spread.options.tabEditable = isProtected;
        disabledMenuItem(isProtected);
      }
      //指定工作表指定范围单元格允许编辑,调用后工作表会被锁定
      function tableCheckRangeEditHandler(
        listRange: CellControllerOptions[],
        isProtected: boolean,
      ): void {
        listRange.forEach((item) => {
          const table = getWorkTableData(item.tableId);
          const position = table.getRange(`${item.startPosition}:${item.endPosition}`);
          const cells = getRangeCells(position); //单元格坐标数组
          table.suspendPaint(); //停止表格重绘
          cells.forEach((citem) => {
            const cell = table.getRange(`${citem}:${citem}`);
            const style = table.getStyle(cell.row, cell.col) || new GC.Spread.Sheets.Style();
            const { backColor } = style || {};
            style.locked = isProtected ? false : undefined; //false-解除锁定，undefined-锁定
            style.backColor = isProtected
              ? backColor
                ? backColor
                : '#9995'
              : backColor === '#9995'
              ? ''
              : backColor;
            cell.setStyle(style);
          });
          table.resumePaint(); //表格重绘
        });
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        liftWorkTableProtected(isProtected);
        let menuData = spread.contextMenu.menuData;
        //删除命中菜单项
        for (let i = 0; i < menuData.length; i++) {
          if (
            hiddenMenuList.includes(menuData[i].name) ||
            hiddenMenuList.includes(menuData[i].command)
          ) {
            menuData.splice(i, 1);
            i--;
          }
        }
      }
      //隐藏菜单栏
      function hiddenTableMenu(): void {
        const dom: any = document.getElementsByClassName('gc-designer-ribbon');
        if (dom[0]) {
          setTimeout(() => {
            dom[0].style.display = 'none';
            designerInit.refresh();
          }, 0);
        }
      }
      //设置指定工作表的单元格文本数据
      function setTableCellValue(list: CellOptions[]): void {
        list.forEach((item) => {
          if (item.position && item.tableId && item.value != undefined) {
            const table = getWorkTableData(item.tableId);
            table.getRange(item.position).value(item.value);
          }
        });
      }
      //背景图修改
      function cellImgChange(data: CellControllerOptions, status: 0 | 1 = 0, img: string): void {
        if (data.startPosition) {
          const table = getWorkTableData(data.tableId);
          const cell = table.getRange(
            `${data.startPosition}:${data.endPosition || data.startPosition}`,
          );
          if (status === 0) {
            //之前的样式
            const prevStyle = table.getStyle(cell.row, cell.col);
            const style = prevStyle ? prevStyle : new GC.Spread.Sheets.Style();
            style.backgroundImage = img;
            style.backgroundImageLayout = GC.Spread.Sheets.ImageLayout.center;
            cell.setStyle(style);
          } else {
            const style = table.getStyle(cell.row, cell.col);
            if (style) {
              style.backgroundImage = '';
              cell.setStyle(style);
            }
          }
        }
      }
      //背景色修改
      function cellBackChange(
        data: CellControllerOptions,
        status: 0 | 1 = 0,
        color?: string,
        isAuth: boolean = true, //是否权限背景色
      ): void {
        const table = getWorkTableData(data.tableId);
        const cellRange = table.getRange(`${data.startPosition}:${data.endPosition}`); //单元格范围对象
        const cells = getRangeCells(cellRange); //单元格坐标数组
        table.suspendPaint(); //停止表格重绘
        cells.forEach((item) => {
          const cell = table.getRange(`${item}:${item}`);
          const style = table.getStyle(cell.row, cell.col) || new GC.Spread.Sheets.Style();
          const { backColor } = style;
          if (isAuth) {
            if (status === 0 && (backColor == undefined || backColor === '')) {
              cell.backColor(color);
            } else if (status === 1 && backColor === color) {
              cell.backColor('');
            }
          } else {
            if (status === 0) {
              cell.backColor(color);
            } else if (status === 1) {
              cell.backColor('');
            }
          }
        });
        table.resumePaint(); //表格重绘
      }
      //导出当前工作薄文件
      function exportFile(name?: string): Promise<any> {
        return new Promise((reslove, reject) => {
          const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
          spread.export(
            (blob: Blob) => {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = name ? name + '.xlsx' : 'spreadsheet.xlsx';
              document.body.appendChild(a);
              a.click();
              setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                reslove({});
              }, 0);
            },
            (e: any) => {
              reject(e);
            },
          );
        });
      }
      //表格滚动条回到置顶位置
      function verticalPositionTop(): void {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        const sheet = spread.getActiveSheet();
        //滚动到第一行的位置
        sheet.showRow(0, GC.Spread.Sheets.VerticalPosition.top);
      }
      //导入JSON数据
      function importJSON(jsonObj: string): void {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        spread.fromJSON(JSON.parse(jsonObj));
        const sheet = spread.getActiveSheet();
        //取消单元格选中
        spread.focus(false);
        sheet.setActiveCell(null);
        workTableDisabledHandler();
        isHiddenFileMenu && hiddenFileMenu();
      }
      //导入文件
      function importFile(): void {
        if (fileRef.current) {
          fileRef.current.click();
        }
      }
      //判断区域是否合并单元格
      function cellIsMerged(sheet: any, selection: CellRangeOptions): boolean {
        const isMerged = sheet.getSpan(selection.row, selection.col);
        if (isMerged) {
          return true;
        } else {
          return false;
        }
      }
      //切换活跃工作表
      function setActiveSheet(name: string): void {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        const sheet = spread.getActiveSheet();
        if (name !== sheet.name) {
          spread.setActiveSheet(name);
        }
      }
      //获取工作表数据,默认为当前激活工作表，可以传入其他工作表name
      function getWorkTableData(name?: string): any {
        if (designerInit || spreadInit) {
          const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
          if (name) {
            return spread.sheets.find((item: any) => item.name() === name);
          } else {
            const sheet = spread.getActiveSheet();
            return sheet;
          }
        } else {
          return undefined;
        }
      }
      //获取单元格范围数据
      function getCellRange(position: string): any {
        const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
        const sheet = spread.getActiveSheet();
        return sheet.getRange(position);
      }
      //获取所有工作表
      function getAllWorkTable(): any[] {
        const spread = designerInit.getWorkbook();
        return spread.sheets;
      }
      //获取工作薄数据
      function getWorkData(): any {
        if (designerInit || spreadInit) {
          const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
          return spread.toJSON();
        } else {
          return undefined;
        }
      }
      //获取当前激活单元格数据
      function getActiveCell(): ActiveCellOptions | undefined {
        if (designerInit || spreadInit) {
          const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
          const sheet = spread.getActiveSheet();
          const selections = sheet.getSelections();
          const selection = selections[0];
          const start = toExcelCoordinate(selection.row, selection.col);
          const end = toExcelCoordinate(
            selection.row + selection.rowCount - 1,
            selection.col + selection.colCount - 1,
          );
          return { cell: sheet.getRange(`${start}:${end}`), selection, start, end };
        } else {
          return undefined;
        }
      }
      //校验导入文件格式是否xlsx
      function verifyXLSXFile(file: File): boolean {
        // 检查文件扩展名
        const fileName = file.name;
        const fileExtension = fileName.split('.').pop();
        if (fileExtension && fileExtension.toLowerCase() !== 'xlsx') {
          message.warning('请上传 .xlsx 格式的 Excel 文件');
          return false;
        }
        if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          message.warning('文件类型不符合要求');
          return false;
        }
        return true;
      }
      //选择文件处理
      function fileHandler(e: any): void {
        const file: any = e.target.files[0];
        if (file) {
          if (verifyXLSXFile(file)) {
            setLoading(true);
            const spread = spreadInit ? spreadInit : designerInit.getWorkbook();
            spread.import(
              file,
              () => {
                e.target.value = '';
                message.success('导入成功');
                fileImportEnd(1);
                setLoading(false);
              },
              () => {
                e.target.value = '';
                message.warning('导入失败');
                fileImportEnd(0);
                setLoading(false);
              },
            );
          } else {
            e.target.value = '';
          }
        } else {
          e.target.value = '';
        }
      }
      return (
        <>
          <input
            accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ref={fileRef}
            type="file"
            style={{ display: 'none' }}
            onChange={fileHandler}
          />
          {loading && <Spin className={styles.loading} spinning={true}></Spin>}
          <div ref={designerRef} className={styles['univer-container']}></div>
        </>
      );
    },
  ),
);
