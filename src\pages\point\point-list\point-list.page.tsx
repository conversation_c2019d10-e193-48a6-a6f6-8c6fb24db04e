import { Badge, Button, Form, Input, message, Select, Space, Table, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { SearchOutlined, RedoOutlined, UndoOutlined } from '@ant-design/icons';
import * as React from 'react';
import { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/es/table';
import { history, useSelector, useDispatch } from 'umi';
import type { PointState } from '@/models/point';
import styles from './point-list.page.less';
import { getPointList, updatePointInfo, exportPointList } from '../point.service';
import AssociatedDevices from './components/AssociatedDevices/index';
import ClearDevices from './components/ClearDevices/index';
import UpdateHistoryModal from './components/UpdateHistory/index';
import { ProcessButtonsStyle } from '@/constants/process-buttons.style';
import { camelToUnderscore } from '@/utils/common';
import { useSetState } from 'ahooks';

interface PageInfo {
  pageNum: number;
  pageSize: number;
}

interface RootState {
  point: PointState;
}

const initPointCode = 'point_code';

const PointList: React.FC = () => {
  const query: any = history.location.state || {};
  const [form] = Form.useForm();
  const [isHistoryModal, setIsHistoryModal] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    pageNum: 1,
    pageSize: 20,
  });
  const [count, setCount] = useState<number>(0);
  const [updateButtonLoading, setUpdateButtonLoading] = useState(false);
  const [isDevicesModal, setIsDevicesModal] = useState<boolean>(false); //关联设备
  const [clearModal, setClearModal] = useState<boolean>(false); //取消关联
  const [activeItem, setActiveItem] = useState<any>();
  const [sortedInfo, setSortedInfo] = useSetState({
    orderColumn: initPointCode,
    orderCondition: 'desc', // 排序条件
  });

  const dispatch = useDispatch();
  const devicesList = useSelector((state: RootState) => state.point.devices || []);

  const columns: ColumnsType<any> = [
    {
      title: '名称',
      dataIndex: 'pointName',
      ellipsis: true,
      sorter: true,
    },
    {
      title: '编码',
      dataIndex: 'pointCode',
      width: 180,
      sorter: true,
    },
    {
      title: '类型',
      dataIndex: 'pointType',
      width: 120,
    },
    {
      title: '时间',
      dataIndex: 'entryTime',
      width: 180,
      sorter: true,
    },
    {
      title: '是否更新',
      dataIndex: 'memo',
      width: 120,
      sorter: true,
      render: (text) => {
        return (
          <Badge
            style={{ color: '#fff' }}
            status={text === '1' ? 'success' : 'error'}
            text={text === '1' ? '已同步' : '未同步'}
          />
        );
      },
    },
    {
      title: '设备关联状态',
      dataIndex: 'switchingValue',
      width: 120,
      render: (text) => {
        return (
          <Badge
            style={{ color: '#fff' }}
            status={text === '1' ? 'success' : 'error'}
            text={text === '1' ? '已关联' : '未关联'}
          />
        );
      },
    },
    {
      title: '操作',
      width: 250,
      align: 'center',
      render: (_, record) => {
        return (
          <Space>
            <a
              onClick={() => {
                let value = form.getFieldsValue();
                history.push(
                  `/pointShow?id=${record.id}&switchingValue=${record?.switchingValue}&pointCode=${record?.pointCode}`,
                  {
                    ...value,
                    ...pageInfo,
                  },
                );
              }}
            >
              查看详情
            </a>
            <a
              onClick={() => {
                setActiveItem(record);
                //查看是否有关联（无则打开关联弹窗，有则打开取消弹窗）record?.???
                if (record?.switchingValue === '1') {
                  setClearModal(true);
                } else {
                  setIsDevicesModal(true);
                }
              }}
            >
              关联设备
            </a>
          </Space>
        );
      },
    },
  ];
  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a
          onClick={() => {
            exportNotAsync();
          }}
        >
          全部
        </a>
      ),
    },
    {
      key: '2',
      label: (
        <a
          onClick={() => {
            exportNotAsync({ pointType: 'analog' });
          }}
        >
          遥测
        </a>
      ),
    },
    {
      key: '3',
      label: (
        <a
          onClick={() => {
            exportNotAsync({ pointType: 'state' });
          }}
        >
          遥信
        </a>
      ),
    },
  ];

  const submit = async (obj = {}) => {
    const value = form.getFieldsValue();
    const query = {
      ...value,
      ...pageInfo,
      ...sortedInfo,
      ...obj,
    };
    setLoading(true);
    const result = await getPointList(query);
    if (result.flag) {
      setDataSource(result.data.list);
      setCount(Number(result.data?.total));
    }
    setLoading(false);
  };

  /** 测点信息同步 */
  const updatePointInfoFn = () => {
    setUpdateButtonLoading(true);
    updatePointInfo()
      .then((res: any) => {
        if (res.flag) {
          message.success(res.data.msg);
        }
        setUpdateButtonLoading(false);
      })
      .catch(() => {
        setUpdateButtonLoading(false);
      });
  };

  /** 测点列表导出 */
  const exportPointListFn = async () => {
    let obj = form.getFieldsValue();
    await exportPointList({ ...obj, ...pageInfo });
  };

  /** 重置 */
  const reset = () => {
    form.resetFields();
    setPageInfo({
      pageNum: 1,
      pageSize: 20,
    });
    setSortedInfo({
      orderColumn: initPointCode,
      orderCondition: 'desc',
    });
    submit({
      pageNum: 1,
      pageSize: 20,
      orderColumn: initPointCode,
      orderCondition: 'desc',
    });
  };

  /** 导出未同步数据 */
  const exportNotAsync = async (obj = {}) => {
    const params = {
      memo: '0',
      ...obj,
    };
    const res = await exportPointList(params);
    if (res.flag) {
      message.success('导出成功！');
    }
  };

  // 获取测点类型（字典）
  const getPointType = async () => {
    dispatch({
      type: 'point/fetchDevices',
    });
  };

  /** 页面初始化 */
  useEffect(() => {
    form.setFieldsValue(query);
    setPageInfo({
      pageNum: query?.pageNum || 1,
      pageSize: query?.pageSize || 20,
    });
    submit(query || {});
    getPointType();
  }, []);

  // ... existing code ...
  const handleTableChange = (pagination: any, _filters: any, sorter: any) => {
    const { field, order } = sorter;
    const newPageInfo = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    setPageInfo(newPageInfo);

    // 确定排序字段和排序方向 asc 升序 desc 降序
    const orderColumn = field ? camelToUnderscore(field) : initPointCode;
    const orderCondition = field ? (order === 'ascend' ? 'asc' : 'desc') : 'desc';

    // 更新排序信息
    setSortedInfo({
      orderColumn,
      orderCondition,
    });

    // 提交查询
    submit({
      ...newPageInfo,
      orderColumn,
      orderCondition,
    });
  };
  // ... existing code ...
  return (
    <div className={styles.page}>
      <Form layout="inline" form={form} labelWrap colon={false}>
        <div className={styles['page-topbar']}>
          <div className={styles.formContent}>
            <Form.Item name="pointName" label="名称">
              <Input type="text" style={{ width: 200 }} placeholder="请输入" />
            </Form.Item>
            <Form.Item name="pointCode" label="编码">
              <Input type="text" placeholder="请输入" />
            </Form.Item>
            <Form.Item name="pointType" label="类型">
              <Select
                placeholder="请选择"
                style={{ width: 150 }}
                options={devicesList}
                fieldNames={{
                  label: 'dictName',
                  value: 'dictCode',
                }}
              />
            </Form.Item>
          </div>
          <div className={styles.formButton}>
            <Space className={styles.formButton_left}>
              <Button
                onClick={() => {
                  submit();
                }}
                type="primary"
                icon={<SearchOutlined />}
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  reset();
                }}
                type="primary"
                icon={<RedoOutlined />}
              >
                重置
              </Button>
            </Space>
            <ProcessButtonsStyle>
              <Button
                loading={updateButtonLoading}
                onClick={() => {
                  updatePointInfoFn();
                }}
                type="primary"
                icon={<UndoOutlined />}
              >
                手动更新
              </Button>
              <Button type="primary" onClick={exportPointListFn}>
                导出全部
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setIsHistoryModal(true);
                }}
              >
                同步记录
              </Button>
              <Dropdown menu={{ items }}>
                <Button type="primary">导出未同步</Button>
              </Dropdown>
            </ProcessButtonsStyle>
          </div>
        </div>
      </Form>
      <div className={styles['page-content']}>
        <Table
          size="small"
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey={(record) => record.id}
          scroll={{ y: window.innerHeight - 220 }}
          onChange={handleTableChange}
          pagination={{
            size: 'small',
            current: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
            total: count,
            pageSizeOptions: [10, 20, 50, 100],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </div>
      <UpdateHistoryModal isHistoryModal={isHistoryModal} setIsHistoryModal={setIsHistoryModal} />
      <AssociatedDevices
        current={activeItem}
        isDevicesModal={isDevicesModal}
        setIsDevicesModal={setIsDevicesModal}
        onOk={submit}
      />
      <ClearDevices
        setIsDevicesModal={setIsDevicesModal}
        current={activeItem}
        clearModal={clearModal}
        setClearModal={setClearModal}
        onOk={submit}
      />
    </div>
  );
};
export default PointList;
