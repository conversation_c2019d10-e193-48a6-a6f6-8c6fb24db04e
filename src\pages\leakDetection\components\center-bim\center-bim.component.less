.center-bim {
  width: 100%;
  height: 40vh;
  position: relative;
}

.bim-container {
  height: 100%;
  width: 100%;

  img {
    height: 120%;
    margin-left: 15%;
  }
}

.bim-paramters {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 15px;
  height: 100%;
  width: 540px;
  background: linear-gradient(45deg, rgba(135, 181, 223, 0.05) 0%, rgba(135, 181, 223, 0.2) 100%);
  border-radius: 2px;
  display: flex;
  flex-direction: column;

  &-title {
    font-family: DingTalk;
    font-size: 18px;
  }

  &-table {
    width: 100%;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    margin-top: 5px;

    &-button {
      background: url(~@/assets/images/leakDetection/name_button_select.png);
      background-repeat: no-repeat;
      background-size: cover;
      display: inline-block;
      padding: 7px 10px 4px 10px;
      font-family: DingTalk;
    }

    &-name {
      font-weight: 400;
      text-shadow: 0 0 3px #fff;
      font-size: 16px;
      margin-right: 4px;
    }

    &-status-succes {
      color: #0ae6a0;
    }

    &-value {
      font-family: DingTalk;
      font-weight: 400;
      text-shadow: 0 0 3px #fff;
      font-size: 16px;
    }

    &-unit {
      margin-left: 4px;
      color: #d4ebfd;
      font-size: 14px;
    }

    table {
      width: 100%;
    }

    td {
      text-align: center;
    }

    thead {
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: brightness(0.98) blur(1.5px);
      td {
        padding: 5px 0px;
        color: #c9e0f3;
      }
    }

    tbody {
      tr {
        background: url(~@/assets/images/leakDetection/data_select.png);
        background-repeat: no-repeat;
        background-size: contain;
        background-position: bottom;
      }

      td {
        padding: 15px 5px;
      }
    }
  }
}
