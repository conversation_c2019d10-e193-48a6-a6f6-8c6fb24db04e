import moment from 'moment';
import { ReactNode } from 'react';
import { RowsDataType } from './components/point-modal/point-modal.component';
export const defaultStartDate = moment().subtract(30, 'minutes').format('YYYY-MM-DD HH:mm:ss');
export const defaultEndDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
export interface DataOptions {
  label: string;
  value: string | number;
  type: string;
  color?: string;
}
export interface QueryParams {
  selectBegin: string;
  selectEnd: string;
  curList: RowsDataType[];
  isRealTime: boolean;
  addList: RowsDataType[];
  deleteList: RowsDataType[];
}
