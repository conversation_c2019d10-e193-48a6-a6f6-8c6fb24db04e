export class TranslMove {
  //translate3d拖动,不会影响元素的布局。性能消耗也小

  private active: boolean = false; //拖动状态控制
  private mousewheel: boolean = false; //缩放状态控制
  private currentX: number = 0;
  private currentY: number = 0;
  private initialX: number = 0;
  private initialY: number = 0;
  private xOffset: number = 0; //x轴位置
  private yOffset: number = 0; //y轴位置
  public scale: string = '1'; //初始大小
  public scaleStep: number = 0.05; //缩放步长
  public maxScale: number = 3; //最大缩放
  public minScale: number = 0.5; //最小缩放
  private dragItem: HTMLElement; //拖动元素
  private container: HTMLElement; //父元素
  constructor(dragItem: HTMLElement, container: HTMLElement) {
    //在container中拖动dragItem元素
    this.dragItem = dragItem;
    this.container = container;
  }

  public zoomEvent(): void {
    //放大缩小
    if (this.container) {
      this.mousewheel = false;
      //鼠标滑轮滚动事件
      this.container.addEventListener('mousewheel', this.onMouseWheel.bind(this));
      this.container.addEventListener('DOMMouseScroll', this.onMouseWheel.bind(this));
      //兼容手势事件
      //开始拖动
      this.container.addEventListener('touchstart', this.dragStart.bind(this), false);
      //拖动结束
      this.container.addEventListener('touchend', this.dragEnd.bind(this), false);
      //拖动中
      this.container.addEventListener('touchmove', this.drag.bind(this), false);
    }
  }
  public dragEvent(): void {
    //拖动
    if (this.dragItem) {
      //兼容鼠标事件
      //鼠标按下开始
      this.dragItem.addEventListener('mousedown', this.dragStart.bind(this), false);
      //鼠标松开停止
      this.dragItem.addEventListener('mouseup', this.dragEnd.bind(this), false);
      //鼠标移动
      this.dragItem.addEventListener('mousemove', this.drag.bind(this), false);
    }
  }
  public resetFit(): void {
    //重置视角
    this.active = false;
    this.scale = '1';
    this.initialX = 0;
    this.initialY = 0;
    this.xOffset = 0;
    this.yOffset = 0;
    this.dragItem.style.transform = this.setStyle({
      translate3d: { xPos: this.xOffset, yPos: this.yOffset },
      scale: this.scale,
    });
  }
  public clearZoomEvent(): void {
    //清除缩放事件
    this.mousewheel = true;
  }
  public clearEvent(): void {
    //清除事件
    this.dragItem.removeEventListener('mousedown', this.dragStart.bind(this), false);
    this.dragItem.removeEventListener('mouseup', this.dragEnd.bind(this), false);
    this.dragItem.removeEventListener('mousemove', this.drag.bind(this), false);
    this.container.removeEventListener('mousewheel', this.onMouseWheel.bind(this));
    this.container.removeEventListener('DOMMouseScroll', this.onMouseWheel.bind(this));
    this.container.removeEventListener('touchstart', this.dragStart.bind(this), false);
    this.container.removeEventListener('touchend', this.dragEnd.bind(this), false);
    this.container.removeEventListener('touchmove', this.drag.bind(this), false);
  }
  public zoomController(down: boolean): void {
    //缩放控制
    this.zoomHandler(down);
  }
  private onMouseWheel(e: any): void {
    //滚轮事件
    if (!this.active && !this.mousewheel) {
      let ev = e || window.event;
      let down = true; //true为下滚动，false为上滚动
      down = ev.wheelDelta ? ev.wheelDelta < 0 : ev.detail > 0;
      this.zoomHandler(down);
    }
  }
  private zoomHandler(down: boolean): void {
    //缩放处理
    if (!this.active) {
      if (down && parseFloat(this.scale) > this.minScale) {
        //最小
        this.scale = (parseFloat(this.scale) - this.scaleStep).toFixed(2);
      } else if (!down && parseFloat(this.scale) < this.maxScale) {
        //最大
        this.scale = (parseFloat(this.scale) + this.scaleStep).toFixed(2);
      }
      this.dragItem.style.transform = this.setStyle({
        translate3d: { xPos: this.xOffset, yPos: this.yOffset },
        scale: this.scale,
      });
    }
  }
  private dragStart(e: any): void {
    if (e.type === 'touchstart') {
      this.initialX = e.touches[0].clientX - this.xOffset;
      this.initialY = e.touches[0].clientY - this.yOffset;
    } else {
      this.initialX = e.clientX - this.xOffset;
      this.initialY = e.clientY - this.yOffset;
    }
    //dragItem有子元素情况下，会导致无法拖动，所以注释掉了。
    //if (e.target === dragItem) {
    this.active = true;
    //}
  }

  private dragEnd(e: any): void {
    //记录结束时的鼠标位置
    this.initialX = this.currentX;
    this.initialY = this.currentY;
    this.active = false;
  }

  private drag(e: any): void {
    this.dragItem.style.cursor = 'move';
    this.dragItem.style.userSelect = 'none';
    if (this.active) {
      e.preventDefault();

      if (e.type === 'touchmove') {
        this.currentX = e.touches[0].clientX - this.initialX;
        this.currentY = e.touches[0].clientY - this.initialY;
      } else {
        this.currentX = e.clientX - this.initialX;
        this.currentY = e.clientY - this.initialY;
      }
      this.xOffset = this.currentX;
      this.yOffset = this.currentY;
      this.setTranslate(this.currentX, this.currentY, this.dragItem);
    }
  }

  private setTranslate(xPos: number, yPos: number, el: HTMLElement): void {
    //改变translate3d
    el.style.transform = this.setStyle({
      translate3d: { xPos, yPos },
      scale: this.scale,
    });
  }
  private setStyle({ translate3d, scale }: any): string {
    //拖动和缩放时要保留之前的样式
    return `scale(${scale})` + ` translate3d(${translate3d.xPos}px, ${translate3d.yPos}px, 0)`;
  }
}
