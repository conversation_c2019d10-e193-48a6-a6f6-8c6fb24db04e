import { Table, TableProps } from 'antd';
import { useMount, useSetState } from 'ahooks';
import styles from './point.table.component.less';
import { DragModal } from '@/components/drag-modal/drag-modal.component';
import { createRoot } from 'react-dom/client';
import { ITemplates } from '@/pages/point/point-chart/point-analysis.page';

interface IProps {
  data: ITemplates['basePointDTOS'][0];
  closeModalCallback?: () => void;
}

const PointTableContent = (props: IProps) => {
  const [state, setState] = useSetState({
    loading: false,
    dataSource: [] as any[],
    allData: [] as any[], // 存储所有数据
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  // 组装分页数据
  const initTableData = (data: ITemplates['basePointDTOS'][0]) => {
    const allData = data.historyDatas.map((item, index) => ({
      pointName: data.pointName,
      pointCode: data.pointCode,
      key: index,
      ...item,
    }));

    // 计算当前页的数据
    const startIndex = 0;
    const endIndex = 10;
    const currentPageData = allData.slice(startIndex, endIndex);

    setState({
      allData,
      dataSource: currentPageData,
      pagination: {
        current: 1,
        pageSize: 10,
        total: allData.length,
      },
    });
  };

  useMount(() => {
    if (props.data && props.data.historyDatas && props.data.historyDatas.length) {
      initTableData(props.data);
    }
  });

  // 关闭
  const closeDrawer = () => {
    props?.closeModalCallback?.();
  };

  // 添加分页变化处理函数
  const handleTableChange = async (page: any) => {
    const { current, pageSize } = page;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    setState({
      dataSource: state.allData.slice(startIndex, endIndex),
      pagination: {
        ...state.pagination,
        current,
        pageSize,
      },
    });
  };

  // 表格列
  const columns: TableProps<any>['columns'] = [
    {
      key: '_',
      align: 'center',
      title: '序号',
      width: 80,
      dataIndex: '_',
      render: (_, __, index) => index + 1,
    },
    {
      key: 'pointName',
      align: 'center',
      title: '测点名称',
      dataIndex: 'pointName',
    },
    {
      key: 'pointCode',
      align: 'center',
      title: '测点编码',
      dataIndex: 'pointCode',
    },
    {
      key: 'value',
      align: 'center',
      title: '值',
      dataIndex: 'value',
    },
    {
      key: 'time',
      align: 'center',
      title: '时间',
      dataIndex: 'time',
    },
  ];

  return (
    <DragModal
      title="测点历史数据"
      open={true}
      onCancel={closeDrawer}
      width={1200}
      footer={false}
      destroyOnClose
      mask={true}
      centered
      forceRender
    >
      <div className={styles['table-container']}>
        <Table
          rowKey={(record, index) => record.key + String(index)}
          dataSource={state.dataSource}
          columns={columns}
          loading={state.loading}
          scroll={{
            y: 450,
            scrollToFirstRowOnChange: true, // 分页变化时滚动到第一行
          }}
          pagination={{
            current: state.pagination.current,
            pageSize: state.pagination.pageSize,
            total: state.pagination.total,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
          }}
        />
      </div>
    </DragModal>
  );
};

export const PointTable = {
  show: (options: { data: ITemplates['basePointDTOS'][0]; closeCallback?: () => void }) => {
    const div = document.createElement('div');
    document.body.appendChild(div);
    const root = createRoot(div);

    const closeModal = () => {
      root.unmount();
      div.remove();
      options?.closeCallback?.();
    };

    root.render(<PointTableContent data={options?.data} closeModalCallback={closeModal} />);
  },
};

export default PointTable;
