import { FC, ReactNode, useState } from 'react';
import styles from './modal.component.less';
import type { positionOptions, PropOptions } from './modal';
import { TopBottom, Direction } from './modal';
import ArrowLeft from '@/assets/images/arrow_left.png';
import ArrowRight from '@/assets/images/arrow_right.png';
import ArrowLefts from '@/assets/images/arrow_lefts.png';
import ArrowRights from '@/assets/images/arrow_rights.png';

export const ModalComponent: FC<PropOptions> = ({ width = 300, height = 100, direction, TopOrBottom, children, show = true }) => {
  //弹框显示控制
  const [modalShow, setModalShow] = useState<boolean>(true);
  function getModalSwitcherIcon(): string {
    //获取开关图标
    if (direction === Direction.left) {
      return modalShow ? ArrowLefts : ArrowRights;
    } else if (direction === Direction.right) {
      return modalShow ? ArrowRight : ArrowLeft;
    } else {
      return '';
    }
  }
  function modalSwitcherIcon(): ReactNode {
    //开关图标
    const icon = getModalSwitcherIcon();
    return (
      <div
        style={{
          backgroundImage: `url(${icon})`,
          top: `calc(50% - 25px)`,
          left: direction === Direction.right ? '-15px' : 'unset',
          right: direction === Direction.left ? '-15px' : 'unset',
        }}
        onClick={() => setModalShow(!modalShow)}
        className={styles['modal-switcher-icon']}
      ></div>
    );
  }
  function computedPosition(): positionOptions {
    //计算弹框位置
    const position: positionOptions = {
      left: '',
      right: '',
      top: '',
      bottom: '',
    };
    if (modalShow) {
      if (direction === Direction.left) {
        position[Direction.left] = '0px';
      } else if (direction === Direction.right) {
        position[Direction.right] = '0px';
      }
      if (TopOrBottom === TopBottom.top) {
        position[TopBottom.top] = '0px';
      } else if (TopOrBottom === TopBottom.bottom) {
        position[TopBottom.bottom] = '0px';
      }
    } else {
      if (direction === Direction.left) {
        position[Direction.left] = `-${width}px`;
      } else if (direction === Direction.right) {
        position[Direction.right] = `-${width}px`;
      }
    }
    return position;
  }
  return (
    <div
      style={{
        width: width + 'px',
        height: height + '%',
        display: show ? 'block' : 'none',
        ...computedPosition(),
      }}
      className={styles['modal-container']}
    >
      {modalSwitcherIcon()}
      <div className={styles['sroll-container']}>{children}</div>
    </div>
  );
};
