import { forwardRef, useState, useRef, useEffect, useImperativeHandle, memo } from 'react';
import { createPortal } from 'react-dom';

interface IUserSelectorRef {
  openUserSelector: () => void;
}
interface PropOptions {
  okCallback?: (users: string[]) => void;
}

const UserSelector = forwardRef<IUserSelectorRef, PropOptions>((props, ref) => {
  const { okCallback } = props;
  const userRef = useRef<HTMLElement>(); // 用户选择组件
  const [users, setUsers] = useState<string[]>([]); // 选择的用户
  const [userOpen, setUserOpen] = useState<boolean>(false); // 用户选择弹窗

  // 选择用户
  function userSelect(val: any) {
    const ids = val.detail.map((item: any) => item.id);
    setUsers(ids);
    okCallback?.(ids);
    setUserOpen(false);
  }

  // 用户选择弹窗关闭
  function userClose() {
    setUserOpen(false);
  }

  function openUserSelector() {
    setUserOpen(true);
  }

  // 暴露
  useImperativeHandle(ref, () => ({
    openUserSelector,
  }));

  // 监听事件
  useEffect(() => {
    if (userRef.current) {
      userRef.current?.addEventListener('select', userSelect);
      userRef.current?.addEventListener('close', userClose);
    }
    return () => {
      userRef.current?.removeEventListener('select', userSelect);
      userRef.current?.removeEventListener('close', userClose);
    };
  }, []);

  return (
    // @ts-ignore
    <>
      {createPortal(
        <user-selector
          open={userOpen}
          values={users}
          ref={userRef}
          titles="用户选择"
          styles="display:none"
        />,
        document.body,
      )}
    </>
  );
});
export default UserSelector;
