import React, { memo, useEffect, useRef } from 'react';
import type { ReactNode, FC } from 'react';
import { Chart } from '@antv/g2';
import { v4 as uuidv4 } from 'uuid';

import { DashboradGraphWrapper } from './dashborad-graph.style';

interface IProps {
  children?: ReactNode;
  title: string;
  subTitle: string;
}

const DashboradGraph: FC<IProps> = (props) => {
  const { title, subTitle } = props;

  const idRef = useRef<string>(`chart_source_${uuidv4().slice(0, 8)}`);

  useEffect(() => {
    renderArc();
  }, []);

  const renderArc = () => {
    const canvas = document.getElementById(idRef.current) as HTMLCanvasElement;
    const ctx = canvas.getContext('2d')!;

    // 计算圆心位置和半径
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 70; // 半径稍小一点，让圆弧不完全填满

    // 计算圆弧起始角度和结束角度
    const startAngle = 0.75 * Math.PI; // 135度位置对应的弧度值
    const endAngle = 0.25 * Math.PI; // 缺口垂直向下，到45度位置结束

    // 绘制渐变颜色的圆弧
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#38F3BF');
    gradient.addColorStop(1, '#00F2FE');
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 10;
    ctx.lineCap = 'round'; // 圆弧线帽样式为圆形

    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    ctx.stroke();
  };

  return (
    <DashboradGraphWrapper>
      <div className="title">{title}</div>
      <div className="subtitle">
        <span className="name">{subTitle}</span>&nbsp;&nbsp;
        <span className="time">
          {'238'.split('').map((digit, index) => (
            <span key={index}>{digit}</span>
          ))}
        </span>
        &nbsp;小时&nbsp;
        <span className="status">发电中</span>
      </div>
      <div className="graph">
        <canvas id={idRef.current}></canvas>
        <div className="text">
          <div className="num">1202.08</div>
          <div className="unit">亿KW.h</div>
          <div className="label">发电量</div>
        </div>
      </div>
      <div className="parameters">
        <div key="1" className="parameter-item">
          <div className="label">稳定运行区</div>
          <div className="value">
            <span className="num">1109.0</span>
            <span>小时</span>
          </div>
        </div>
        <div key="2" className="parameter-item">
          <div className="label">水头</div>
          <div className="value">
            <span className="num">193.0</span>
            <span>m</span>
          </div>
        </div>
        <div key="3" className="parameter-item">
          <div className="label">允许振动区</div>
          <div className="value">
            <span className="num">0.9</span>
            <span>小时</span>
          </div>
        </div>
        <div key="4" className="parameter-item">
          <div className="label">励磁电流</div>
          <div className="value">
            <span className="num">102.9</span>
            <span>A</span>
          </div>
        </div>
        <div key="5" className="parameter-item">
          <div className="label">禁止运行区</div>
          <div className="value">
            <span className="num">0.4</span>
            <span>小时</span>
          </div>
        </div>
        <div key="6" className="parameter-item">
          <div className="label">导叶开度</div>
          <div className="value">
            <span className="num">102.9</span>
            <span>%</span>
          </div>
        </div>
      </div>
    </DashboradGraphWrapper>
  );
};

export default memo(DashboradGraph);
