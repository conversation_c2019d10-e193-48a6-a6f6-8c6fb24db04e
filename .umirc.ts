import { defineConfig } from 'umi';
import proxy from './proxy';
import routes from './routes';

const { NODE_ENV, PORT, HOST } = process.env;
const publicPath = NODE_ENV === 'production' ? '/micro_operations/' : `http://${HOST}:${PORT}/`;

export default defineConfig({
  hash: true,
  history: { type: 'browser' }, // 推荐使用history
  nodeModulesTransform: { type: 'none' },
  qiankun: { slave: {} },
  webpack5: {},
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: false,
  },
  define: {
    // 注入全局常量
    PORT,
    HOST,
    ISDEVELOPMENT: NODE_ENV === 'development',
  },
  title: false,
  exportStatic: {},
  extraBabelPlugins: ['babel-plugin-styled-components'],
  routes,
  proxy,
  base: '/operations',
  outputPath: 'micro_operations',
  publicPath,
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  plugins: ['@alitajs/keep-alive'],
  keepalive: ['/special-equipment'],
  chainWebpack:
    NODE_ENV === 'production'
      ? (config) => {
          config.module
            .rule('fonts')
            .test(/\.(woff2?|eot|ttf|otf)$/i)
            .use('url-loader')
            .loader(require.resolve('@umijs/deps/compiled/url-loader'))
            .options({
              limit: 4096, // 小于1M将会被打包成 base64
              fallback: {
                loader: require.resolve('@umijs/deps/compiled/file-loader'),
                options: {
                  name: 'fonts/[name].[hash:8].[ext]',
                  publicPath,
                },
              },
            })
            .end();
          config.module
            .rule('images')
            .test(/\.(png|jpe?g|gif|webp)$/i)
            .use('url-loader')
            .loader(require.resolve('@umijs/deps/compiled/url-loader'))
            .options({
              limit: 4096, // 小于1M将会被打包成 base64
              fallback: {
                loader: require.resolve('@umijs/deps/compiled/file-loader'),
                options: {
                  esModule: false,
                  name: 'img/[name].[hash:8].[ext]',
                  publicPath,
                },
              },
            })
            .end();
        }
      : (config) => {
          config.resolve.extensions.add('.less');
        },
});
