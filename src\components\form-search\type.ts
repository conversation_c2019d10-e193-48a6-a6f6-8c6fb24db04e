import { ButtonProps, FormItemProps, FormProps, InputProps, SelectProps } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import type { FormInstance, Rule } from 'antd/es/form';

export interface IBaseFormExp {
  baseFormInstance: FormInstance<any>;
}

// 表单
export interface ISearchFormConfig {
  elements: ElementType[]; // antd 标签
  layout?: 'inline' | 'horizontal' | 'vertical';
  initialValues?: Record<string, any>;
  formPorps?: FormProps;
}
export type ElementType = ISelect | IRangePicker | IButton | IInput;

// 组件类型
export enum EnumElement {
  BUTTON,
  SELECT,
  RANGEPICKER,
  INPUT,
}

// 共享
export interface IElement {
  key: string;
  type: EnumElement;
  label: FormItemProps['label'];
  formItemProps?: FormItemProps;
}
// 表单相关
export interface IFieldElement extends IElement {
  field: string;
  rules?: Rule[];
}

// 按钮组件
export interface IButton extends IElement {
  type: EnumElement.BUTTON;
  disabled?: boolean;
  noStyle?: boolean;
  loading?: boolean;
  onClick?: ButtonProps['onClick'];
  props?: ButtonProps; // antd buttons props
}
// 日期组件
export interface IRangePicker extends IFieldElement {
  type: EnumElement.RANGEPICKER;
  allowClear?: boolean;
  props?: RangePickerProps; // antd date props
}

// 下拉选择
export interface IOption {
  key: string;
  label: string;
  value: string | number;
}
export interface ISelect extends IFieldElement {
  type: EnumElement.SELECT;
  options: IOption[];
  allowClear?: boolean;
  disabled?: boolean;
  props?: SelectProps; // antd select props
  onChange?: SelectProps['onChange'];
}

// 输入框
export interface IInput extends IFieldElement {
  type: EnumElement.INPUT;
  disabled?: boolean;
  props?: InputProps;
  onBlur?: InputProps['onBlur'];
  onChange?: InputProps['onChange'];
}
