import { Effect, Reducer } from 'umi';
import { getPonitsType } from '@/pages/point/point.service';
export interface IDevice {
  dictCode: string;
  dictName: string;
  dictOrder: number;
  dictValue: string;
  editDate: null | string;
  editUserId: null | string;
  id: string;
  isLeaf: boolean;
  isSys: null | string;
  isTree: null | string;
  memo: string;
  parentCode: string;
  rootId: string;
  children?: IDevice[];
}
export interface PointState {
  devices: IDevice[];
}

export interface PointModelType {
  namespace: 'point'; // 命名空间
  state: PointState; // 状态
  effects: {
    fetchDevices: Effect; // 获取测点类型
  };
  reducers: {
    setDevices: Reducer<PointState>; // 设置测点类型
  };
}

const initState: PointState = {
  devices: [],
};

const PointModel: PointModelType = {
  namespace: 'point',
  state: initState,
  effects: {
    *fetchDevices({ payload }, { call, put }): Generator<any, void, Global.Response<IDevice[]>> {
      try {
        const response = yield call(getPonitsType);
        if (response?.data?.[0]?.children) {
          yield put({
            type: 'setDevices',
            payload: response.data[0].children,
          });
        }
      } catch (error) {
        console.error('获取测点类型失败:', error);
      }
    },
  },
  reducers: {
    setDevices(state, { payload }) {
      return {
        ...state,
        devices: payload,
      };
    },
  },
};

export default PointModel;
