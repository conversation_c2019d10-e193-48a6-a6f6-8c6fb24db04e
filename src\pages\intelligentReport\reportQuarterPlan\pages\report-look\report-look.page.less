@borderColor: #28487b;
.report-look-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-container {
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid #28487b;
}
.controller-container {
  display: flex;
}
.back {
  cursor: pointer;
  margin-left: 20px;
  color: #0392d8;
  display: flex;
  align-items: center;
  span {
    color: #0392d8;
  }
}
.title {
  display: flex;
  align-items: center;
  font-size: 18px;
}
.form {
  padding: 0px 30px;
  margin-top: 20px;
}
.base-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-top: 1px solid @borderColor;
  border-left: 1px solid @borderColor;
  border-right: 1px solid @borderColor;

  &-item {
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    box-sizing: border-box;

    span {
      display: inline-block;
      flex: 1;
      min-height: 41px;
      padding: 10px 15px 8px;
      border-bottom: 1px solid @borderColor;
    }

    span:first-child {
      flex: 0 0 190px;
      background-color: #103473;
    }
  }

  &-item:last-child:nth-child(odd) {
    grid-column-start: 1;
    grid-column-end: -1;
  }
}
