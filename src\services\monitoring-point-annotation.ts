import { ImageParams } from '@/pages/monitoring-point-annotation/components/monitoring-image-add-modal/monitoring-image-add-modal';
import { TreeParams } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { request, jsonHeader, formHeader } from '@/utils/request';

export interface ResponseOptions<T> {
  //响应配置
  code: string;
  data: T;
  message: string;
  error?: string;
}
export function pointDataList(): Promise<ResponseOptions<any[]>> {
  return request.get('/BasePoint/listPage');
}
//获取设备树
export function baseUnitTree<T>(params: TreeParams): Promise<ResponseOptions<T>> {
  return request.get('/BaseUnitTree/list', { params });
}
//新增设备树-节点
export function addImageNode<T>(data: T): Promise<ResponseOptions<any>> {
  return request.post('/BaseUnitTree/', { data: JSON.stringify(data), headers: jsonHeader });
}
//编辑设备树-节点
export function updateImageNode<T>(data: T, id: string): Promise<ResponseOptions<any>> {
  return request.post('/BaseUnitTree/' + id, { data: JSON.stringify(data), headers: jsonHeader });
}
//删除设备树节点
export function delTreeNode(id: string): Promise<ResponseOptions<any>> {
  return request.delete('/BaseUnitTree/' + id);
}
//获取模型数据
export function getModelInfo(nodeId: string): Promise<ResponseOptions<any>> {
  return request.get('/node/modelVersionInfo', { params: { nodeId } });
}
