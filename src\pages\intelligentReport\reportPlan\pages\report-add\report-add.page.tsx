import { ReactNode, useEffect, useState } from 'react';
import styles from './report-add.page.less';
import { DoubleRightOutlined } from '@ant-design/icons';
import { DatePicker, Form, Input, Spin, message } from 'antd';
import {
  PlanTypeEnum,
  ReportAttributeOptions,
  ReportUpdateOptions,
  planAttributeList,
  planReportAdd,
  planReportUpdate,
  reportDetail,
} from '@/services/intelligentReport/report-plan';
import { history, useParams } from 'umi';
import { ReportAddOptions } from '@/services/intelligentReport/report-plan';
import moment from 'moment';
import dayjs from 'dayjs';
import { useSyncCallback } from '@/hooks/useGetState';
const ReportAddPage = () => {
  //属性列表
  const [attributeList, setAttributeList] = useState<ReportAttributeOptions[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [forms] = Form.useForm();
  const { id } = useParams<{ id: string }>();
  const detailFormShowHandlerSync = useSyncCallback(detailFormShowHandler);
  useEffect(() => {
    getFormAttribute();
  }, []);
  useEffect(() => {
    if (attributeList.length > 0) {
      setTimeout(() => {
        forms.setFieldValue('plan_cycle', PlanTypeEnum.MONTH);
        forms.setFieldValue('create_date', dayjs(new Date().getTime()).format('YYYY-MM-DD'));
      }, 500);
    }
  }, [attributeList]);
  //详情回显处理
  function detailFormShowHandler(data: any): void {
    attributeList.forEach((item) => {
      if (item.dataType === 'Date') {
        forms.setFieldValue(item.code, dayjs(data[item.attributeName]));
      } else {
        forms.setFieldValue(item.code, data[item.attributeName]);
      }
    });
  }
  //获取计划详情
  function getDetail(id: string): void {
    setLoading(true);
    reportDetail(id).then((res) => {
      if (res.code === '1' && res.data) {
        detailFormShowHandlerSync(res.data[0]);
      }
      setLoading(false);
    });
  }
  //获取填写属性
  function getFormAttribute(): void {
    setLoading(true);
    planAttributeList().then((res) => {
      if (res.code === '1') {
        setAttributeList(res.data);
        if (id !== 'add') {
          getDetail(id);
        } else {
          setLoading(false);
        }
      }
      setLoading(false);
    });
  }
  //处理提交参数
  function formAddDataHandler(fields: string[], res: any): ReportAddOptions[] {
    let data: ReportAddOptions[] = [];
    fields.forEach((item) => {
      const curData = attributeList.find((citem) => citem.code === item);
      if (curData) {
        data.push({
          attributeId: curData.id,
          value: curData.code === 'plan_time' ? moment(res[item].$d).format('YYYY-MM') : res[item],
        });
      }
    });
    return data;
  }
  //处理更新参数
  function formUpdateDataHandler(fields: string[], res: any): ReportUpdateOptions[] {
    let data: ReportUpdateOptions[] = [];
    fields.forEach((item) => {
      const curData = attributeList.find((citem) => citem.code === item);
      if (curData) {
        data.push({
          submissionId: id,
          name: curData.attributeName,
          value: curData.code === 'plan_time' ? moment(res[item].$d).format('YYYY-MM') : res[item],
        });
      }
    });
    return data;
  }
  //新增
  function add(res: any): void {
    const fields = Object.keys(res);
    const data = formAddDataHandler(fields, res);
    setLoading(true);
    planReportAdd(data)
      .then((res) => {
        if (res.code === '1') {
          message.success('提交成功！');
          history.goBack();
        } else {
          message.warning(res.error || '提交异常');
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //更新
  function update(res: any): void {
    const fields = Object.keys(res);
    const data = formUpdateDataHandler(fields, res);
    setLoading(true);
    planReportUpdate(data)
      .then((res) => {
        if (res.code === '1') {
          message.success('提交成功！');
          history.goBack();
        } else {
          message.warning(res.error || '提交异常');
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  //提交
  function commit(): void {
    forms.validateFields().then((res) => {
      if (id === 'add') {
        add(res);
      } else {
        update(res);
      }
    });
  }
  //控制按钮模板
  function controllerComponent(): ReactNode {
    return (
      <div className={styles['controller-container']}>
        <type-button loading={loading} onClick={commit}>
          提交
        </type-button>
        <span className={styles.back} onClick={backPage}>
          返回
          <DoubleRightOutlined />
        </span>
      </div>
    );
  }
  //返回页面
  function backPage(): void {
    history.goBack();
  }
  return (
    <div className={styles['report-add-page']}>
      <section className={styles['title-container']}>
        <div className={styles.title}>
          <span>{id === 'add' ? '新增报表计划' : '编辑报表计划'}</span>
        </div>
        {controllerComponent()}
      </section>
      <Spin wrapperClassName={styles.spin} spinning={loading}>
        <section className={styles.form}>
          <Form labelCol={{ span: 2 }} form={forms}>
            {attributeList?.map((item) => (
              <Form.Item
                style={{
                  display: !['create_date', 'plan_cycle'].includes(item.code) ? 'block' : 'none',
                }}
                key={item.id}
                rules={[
                  { required: item.isRequired === '1', message: item.attributeName + '是必填项' },
                ]}
                name={item.code}
                label={item.attributeName}
              >
                {item.dataType === 'String' && (
                  <Input placeholder="请输入..." addonAfter={item.dataUnit}></Input>
                )}
                {item.dataType === 'Date' && item.code !== 'plan_time' && (
                  <Input placeholder="请输入..." addonAfter={item.dataUnit}></Input>
                )}
                {item.code === 'plan_time' && (
                  <DatePicker disabled={id !== 'add'} style={{ width: '100%' }} picker="month" />
                )}
              </Form.Item>
            ))}
          </Form>
        </section>
      </Spin>
    </div>
  );
};

export default ReportAddPage;
