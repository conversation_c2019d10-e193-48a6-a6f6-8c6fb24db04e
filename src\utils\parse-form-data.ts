// 将对象转换成FormData
export function parseParamFromData(param: any) {
  const formData = new FormData();
  for (const key in param) {
    if (key && param[key] !== undefined) {
      if (Object.prototype.toString.call(param[key]) === '[object Array]') {
        for (let i = 0; i < param[key].length; i += 1) {
          formData.append(key, param[key][i]);
        }
      } else {
        formData.append(key, param[key]);
      }
    }
  }
  return formData;
}
