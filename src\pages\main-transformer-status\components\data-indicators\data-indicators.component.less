.data-indicators {
  position: absolute;
  right: 10px;
  bottom: 10px;

  &-container {
    background: rgba(30, 78, 134, 0.5);
    border-radius: 2px;
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(2, 165px);
    grid-template-rows: repeat(6, 32px);
    column-gap: 10px;
    row-gap: 4px;
  }
}

.data-item {
  color: #fff;
  font-size: 15px;
  background-size: contain;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 6px;

  &-unit {
    display: inline-block;
    width: 33px;
    text-align: right;
  }
}
