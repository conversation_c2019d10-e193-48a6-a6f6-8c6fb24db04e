import { FC, forwardRef, memo, useEffect, useRef, useState } from 'react';
import { PropOptions } from './line-chart-double';
import styles from './line-chart-double.component.less';
import { Empty } from 'antd';
import { DualAxes } from '@antv/g2plot';
import { chartAxisStyle } from '@/pages/equip-status-bim/components/right-equip-modal/right-equip-modal';
import { DataOptions } from '@/pages/equip-status-bim/components/left-equip-modal/left-equip-modal';
//排序后的数据
let sortList: [DataOptions[], DataOptions[]] = [[], []];
export const DoubleLineChart: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        unitY = 50,
        sliderColor = '#fff',
        geometryOptions,
        appendPadding,
        padding = [60, 10, 70, 40],
        legend = true, //是否需要图例
        chartData, // 统计图数据
        loading = false, // 加载状态
        unit = ['', ''], // 统计图单位
      },
      ref,
    ) => {
      const [chart, setChart] = useState<DualAxes>(); // 统计图实例
      const chartRef = useRef<any>(null);
      useEffect(() => {
        if (chartData.length > 0) {
          chartInit();
        }
      }, [chartData]);
      // 初始化统计图
      function chartInit(): void {
        // 数据排序
        sortList = JSON.parse(JSON.stringify(chartData));
        if (chart) {
          chart.changeData(chartData);
        } else {
          const line = new DualAxes(chartRef.current, {
            padding,
            appendPadding,
            data: chartData,
            xField: 'label', // x 轴字段
            yField: ['value', 'value'], // y 轴字段
            geometryOptions,
            xAxis: {
              ...chartAxisStyle.xAxis,
              range: [0, 1], // 强制 x 轴范围从 0 到数据的最大值
            },
            yAxis: [
              {
                tickCount: 5,
                max: 30,
                min: 0,
                grid: chartAxisStyle.yAxis.grid,
                label: chartAxisStyle.yAxis.label,
                title: {
                  ...chartAxisStyle.yAxis.title,
                  text: unit[0],
                  style: {
                    ...chartAxisStyle.yAxis.title.style,
                    y: unitY,
                  },
                },
              },
              {
                tickCount: 5,
                max: 30,
                min: 0,
                grid: chartAxisStyle.yAxis.grid,
                label: chartAxisStyle.yAxis.label,
                title: {
                  ...chartAxisStyle.yAxis.title,
                  text: unit[1],
                  style: {
                    ...chartAxisStyle.yAxis.title.style,
                    y: unitY,
                  },
                },
              },
            ],
            legend: legend
              ? {
                  position: 'top-right',
                  itemHeight: 30, // 设置每个 item 的高度
                  maxHeight: 50, // 设置图例最大高度，避免溢出
                  itemName: {
                    style: {
                      inactiveFill: '#753',
                      fill: '#fff',
                      fontSize: 12,
                    },
                  },
                }
              : false,
            tooltip: {
              enterable: true,
              domStyles: {
                'g2-tooltip-title': {
                  fontSize: '14px',
                  color: '#CCE8FF',
                },
              },
              formatter: (datum: any) => {
                const data: DataOptions = datum;
                const index = sortList.findIndex((item) => item[0].type === data.type);
                const units = unit[index] || '';
                const value = `<span style="color:#fff">${data.value + ' ' + units}</span>`;
                return { name: data.type, value };
              },
              showCrosshairs: true,
              crosshairs: {
                line: {
                  style: {
                    stroke: '#1E89F4', // 设置竖线的颜色
                  },
                },
              },
            },
            slider: {
              //缩略图
              start: 0,
              end: 1,
              trendCfg: {
                lineStyle: {
                  //趋势样式
                  stroke: sliderColor,
                },
              },
              handlerStyle: {
                //手柄样式
                stroke: 'transparent',
                fill: '#5192F3',
              },
              textStyle: {
                //文本样式
                fill: '#fff',
                fillOpacity: 0.9,
              },
              foregroundStyle: {
                //前景样式
                fill: '#2356B0',
                fillOpacity: 0.7,
              },
            },
            //线条动画
            animation: {
              appear: {
                animation: 'path-in',
                duration: 5000,
              },
            },
          });
          line.render();
          setChart(line);
        }
      }
      return (
        <div ref={chartRef} className={styles['chart-container']}>
          {/* 统计区域 */}
          {loading === false && chartData[0].length === 0 ? (
            <div className={styles.empty}>
              <Empty description="暂无数据" />
            </div>
          ) : null}
        </div>
      );
    },
  ),
);
