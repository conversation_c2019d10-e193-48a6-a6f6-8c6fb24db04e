import { FC, MouseEvent, ReactNode, memo, useEffect, useRef, useState } from 'react';
import styles from './equip-type-tab.component.less';
import { PropOptions, TAB_LIST, WAR_LIST } from './equip-type-tab';
import { WarningOutlined } from '@ant-design/icons';
import { TranslMove } from '@/utils/translMove';
export const EquipTypeTab: FC<PropOptions> = memo(
  ({ socketClient, equipStatus, parentRef, bimDefViewSet, tabChange }) => {
    const [tabKey, setTabKey] = useState<string>();
    const warRef = useRef<any>(null);
    const tabRef = useRef<any>(null);
    useEffect(() => {
      if (socketClient) {
        const key = TAB_LIST[0].value;
        setTabKey(key);
        tabChange(key, TAB_LIST[0]);
        if (parentRef.current && warRef.current) {
          const trans = new TranslMove(warRef.current, parentRef.current);
          trans.dragEvent();
        }
        if (parentRef.current && tabRef.current) {
          const trans = new TranslMove(tabRef.current, parentRef.current);
          trans.dragEvent();
        }
      }
    }, [socketClient]);
    //机组tab渲染模板
    function tabComponent(): ReactNode {
      return TAB_LIST.map((item) => {
        //当前机组状态
        const node = equipStatus.find((citem) => citem.id === item.startId);
        const defaultStyle = styles['equip-type-tab-component-container-tab-item'];
        const warningStyle = `${styles['equip-type-tab-component-container-tab-item']} ${styles['equip-type-tab-component-container-tab-item-warning']}`;
        let style = node && node.status === 1 ? defaultStyle : warningStyle;
        return (
          <div
            data-key={item.value}
            key={item.value}
            className={`${style} ${
              tabKey === item.value
                ? styles['equip-type-tab-component-container-tab-item-active']
                : ''
            }`}
          >
            <span data-key={item.value}>{item.label}</span>
          </div>
        );
      });
    }
    //恢复视角模板
    function bimDefaultViewComponent(): ReactNode {
      return (
        <div
          onClick={bimDefViewSet}
          className={`${styles['equip-type-tab-component-container-tab-item']} ${styles['equip-type-tab-component-container-tab-other']}`}
        >
          <span>恢复视角</span>
        </div>
      );
    }
    //tab切换处理
    function tabChangeHandler(e: MouseEvent<HTMLElement>): void {
      const event: any = e.target;
      const key = event.getAttribute('data-key');
      if (key && key !== tabKey) {
        const node = TAB_LIST.find((item) => item.value === key);
        bimDefViewSet();
        setTabKey(key);
        tabChange(key, node);
      }
    }
    //警告信息模板
    function warningComponent(): ReactNode {
      return (
        <div className={styles['equip-type-tab-component-container-warning-tab']}>
          <p>
            <WarningOutlined />
            <span>警报</span>
          </p>
          <p>
            {WAR_LIST.map((item) => (
              <span
                className={styles['equip-type-tab-component-container-warning-tab-item']}
                data-key={item.value}
                key={item.value}
                style={{ backgroundColor: item.color }}
              >
                {item.label}
              </span>
            ))}
          </p>
        </div>
      );
    }
    return (
      <section className={styles['equip-type-tab-component-container']}>
        {/* 警告信息模板 */}
        <div ref={warRef} className={styles['equip-type-tab-component-container-warning']}>
          {warningComponent()}
        </div>
        <div
          ref={tabRef}
          onClick={tabChangeHandler}
          className={styles['equip-type-tab-component-container-tab']}
        >
          {/* tab切换模板 */}
          {equipStatus.length > 0 ? tabComponent() : null}
          {bimDefaultViewComponent()}
        </div>
      </section>
    );
  },
);
