import React from 'react';
import { Table, Button, Space, Divider } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import TableComponent from './table.component';
import styles from './work-cond.component.less';

// 现场设备状态数据类型
interface EquipmentStatusData {
  key: string;
  index: number;
  equipment: string;
  status: string;
}

// 接地线/接地刀闸数据类型
interface GroundingData {
  key: string;
  index: number;
  status: string;
}

export default function WorkCondComponent() {
  // 现场设备状态数据
  const equipmentStatusData: EquipmentStatusData[] = [
    {
      key: '1',
      index: 1,
      equipment: '主变压器',
      status: '运行中',
    },
    {
      key: '2',
      index: 2,
      equipment: '断路器',
      status: '待机',
    },
  ];

  // 已装设接地线数据
  const groundingWireData: GroundingData[] = [
    {
      key: '1',
      index: 1,
      status: '已装设',
    },
    {
      key: '2',
      index: 2,
      status: '已装设',
    },
  ];

  // 已合接地刀闸数据
  const groundingSwitchData: GroundingData[] = [
    {
      key: '1',
      index: 1,
      status: '已合闸',
    },
    {
      key: '2',
      index: 2,
      status: '已合闸',
    },
  ];

  // 现场设备状态列定义
  const equipmentStatusColumns: ColumnsType<EquipmentStatusData> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
    },
    {
      title: '现场设备',
      dataIndex: 'equipment',
      key: 'equipment',
      align: 'center',
    },
    {
      title: '运行状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <EditOutlined
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleEdit(record)}
          />
          <DeleteOutlined
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  // 接地线/接地刀闸列定义
  const groundingColumns: ColumnsType<GroundingData> = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
    },
    {
      title: '运行状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <EditOutlined
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleEdit(record)}
          />
          <DeleteOutlined
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  // 事件处理函数
  const handleAdd = () => {
    console.log('新增按钮点击');
  };

  const handleEdit = (record: any) => {
    console.log('编辑', record);
  };

  const handleDelete = (record: any) => {
    console.log('删除', record);
  };

  return (
    <div className={styles['work-cond-container']}>
      {/* 第一个表格：现场设备状态 */}
      <TableComponent
        title="现场设备状态"
        columns={equipmentStatusColumns}
        dataSource={equipmentStatusData}
        onAdd={handleAdd}
      />

      {/* 分割线 */}
      <Divider
        style={{
          backgroundColor: '#2F4F81',
          margin: '24px 0',
        }}
      />

      {/* 第二个表格：已装设接地线 */}
      <TableComponent
        title="已装设接地线"
        columns={groundingColumns}
        dataSource={groundingWireData}
        onAdd={handleAdd}
      />

      {/* 分割线 */}
      <Divider
        style={{
          backgroundColor: '#2F4F81',
          margin: '24px 0',
        }}
      />

      {/* 第三个表格：已合接地刀闸 */}
      <TableComponent
        title="已合接地刀闸"
        columns={groundingColumns}
        dataSource={groundingSwitchData}
        onAdd={handleAdd}
      />
    </div>
  );
}
