import type { Effect, Reducer } from 'umi';

interface Window {
  __POWERED_BY_QIANKUN__: string;
}

interface Dict {
  id: string;
  dictName: string;
  dictCode: string;
  dictValue: string;
}

interface PageInfo {
  pageNum: number;
  pageSize: number;
}

interface IPageInfo {
  pageNum: number;
  pageSize: number;
  total: number;
}

/** dva */
interface Effects {
  [actionName: string]: Effect;
}
interface Reducers {
  [actionName: string]: Reducer;
}
