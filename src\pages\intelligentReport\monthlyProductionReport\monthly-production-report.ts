import { ReportStateEnum, StateMap } from '@/services/intelligentReport/report-management';

export interface PageParams {
  pageNum: number;
  pageSize: number;
  name: string;
  type: string;
  state?: ReportStateEnum;
  startDate?: string;
  endDate?: string;
}
export interface TableOptions {
  name: string;
  createDate: string; //报送日期
  auditUserName: string; //审核人
  auditUserId: string;
  state: ReportStateEnum;
  workflowState: ReportStateEnum; //流程状态
  curTask: boolean; //是否能审核
  month: string;
  year: string;
  createUserId: string; //创建人id
  id: string;
}

export const params: PageParams = {
  pageNum: 1,
  pageSize: 10,
  name: '',
  type: '',
  state: undefined,
  startDate: undefined,
  endDate: undefined,
};
export enum PageType {
  EDIT = 'edit',
  LOOK = 'look',
  EXAMINE = 'examine',
}
export const stateList = Array.from(StateMap).map(([value, label]) => ({ label, value }));
