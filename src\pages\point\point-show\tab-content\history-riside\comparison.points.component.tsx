import { useState } from 'react';
import styles from './comparison.points.component.less';
import { Pointlist } from '@/pages/point/type';
import { Button, Popconfirm, Card } from 'antd';
import classNames from 'classnames';
import { InfoOutlined } from '@ant-design/icons';

interface IProps {
  handleSelectPoint: () => void; // 新增测点
  handleRemovePoint: (point: Pointlist.List) => void; // 删除测点
  list: Pointlist.List[];
}
/**
 * <AUTHOR>
 * @description 对比测点组件
 */
export default function ComparisonPoints(props: IProps) {
  const { handleSelectPoint, list, handleRemovePoint } = props;
  const [activePoint, setActivePoint] = useState<string>('');
  const renderPoint = (item: Pointlist.List) => {
    const pointInfo = [
      { label: '测点名称', value: item.pointName },
      { label: '测点编码', value: item.pointCode },
      { label: '测点类型', value: item.pointType },
    ];
    return (
      <div className={styles['point-info']}>
        {pointInfo.map(({ label, value }) => (
          <div key={label} className={styles['point-info-item']}>
            <span className={styles['point-info-item-label']}>{label}：</span>
            <span className={styles['point-info-item-value']}>{value}</span>
          </div>
        ))}
      </div>
    );
  };
  return (
    <div className={styles['comparison-points-card-container']}>
      <Card title="对比测点" extra={<Button onClick={handleSelectPoint}>新增测点</Button>}>
        {list.map((item) => {
          return (
            <Popconfirm
              // destroyTooltipOnHide
              autoAdjustOverflow
              align={{ offset: [-8, 0] }}
              placement="left"
              okText="删除"
              // cancelText="关闭"
              cancelButtonProps={{ style: { display: 'none' } }}
              title="测点信息"
              icon={<InfoOutlined />}
              onConfirm={() => handleRemovePoint(item)}
              description={renderPoint(item)}
              trigger={'hover'}
              key={item.pointCode}
            >
              <div
                key={item.pointCode}
                className={classNames(styles['column-content-item'], {
                  [styles['active']]: activePoint === item.pointCode,
                })}
                onClick={() => setActivePoint(item.pointCode)}
              >
                {item.pointName}
              </div>
            </Popconfirm>
          );
        })}
      </Card>
    </div>
  );
}
