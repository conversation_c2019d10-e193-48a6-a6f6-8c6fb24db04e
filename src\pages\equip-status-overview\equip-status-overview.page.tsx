import React, { memo } from 'react';
import type { <PERSON>actNode, FC } from 'react';

import { EpuipStatusOverviewWrapper } from './epuip-status-overview.style';
import SummaryBar from './c-cpns/summary-bar/summary-bar.component';
import GraphContent from './c-cpns/graph-content/graph-content.component';

interface IProps {
  children?: ReactNode;
}

const EpuipStatusOverview: FC<IProps> = () => {
  return (
    <EpuipStatusOverviewWrapper>
      <SummaryBar />
      <GraphContent />
    </EpuipStatusOverviewWrapper>
  );
};

export default memo(EpuipStatusOverview);
