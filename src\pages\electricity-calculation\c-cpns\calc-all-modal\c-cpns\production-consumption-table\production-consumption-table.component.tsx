import { memo, useEffect, useState } from 'react';
import type { TableProps } from 'antd';
import { ConfigProvider, Table } from 'antd';
import type { FC, ReactNode } from 'react';

import { ProductionConsumptionTableWrapper } from './production-consumption-table.style';

type RecordType = {
  key: string;
  capacity: string;
  '11B': string;
  '12B': string;
  '13B': string;
};

interface IProps {
  children?: ReactNode;
  calcResult: {
    [key: string]: string;
  };
}

const ProductionConsumptionTable: FC<IProps> = (props) => {
  const { calcResult } = props;

  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  useEffect(() => {
    setDataSource([
      {
        key: '1',
        capacity: '期初电字',
        '11B': calcResult['生产11B期初电字'] ?? '-',
        '12B': calcResult['生产12B期初电字'] ?? '-',
        '13B': calcResult['生产13B期初电字'] ?? '-',
      },
      {
        key: '2',
        capacity: '期末电字',
        '11B': calcResult['生产11B期末电字'] ?? '-',
        '12B': calcResult['生产12B期末电字'] ?? '-',
        '13B': calcResult['生产13B期末电字'] ?? '-',
      },
      {
        key: '3',
        capacity: '电字差',
        '11B': calcResult['生产11B电字差'] ?? '-',
        '12B': calcResult['生产12B电字差'] ?? '-',
        '13B': calcResult['生产13B电字差'] ?? '-',
      },
      {
        key: '4',
        capacity: '计算电量',
        '11B': calcResult['生产11B电量'] ?? '-',
        '12B': calcResult['生产12B电量'] ?? '-',
        '13B': calcResult['生产13B电量'] ?? '-',
      },
      {
        key: '5',
        capacity: '合计',
        '11B': calcResult['生产合计电量'] ?? '-',
        '12B': '',
        '13B': '',
      },
    ]);
  }, [calcResult]);

  const columns: TableProps<RecordType>['columns'] = [
    {
      key: 'name',
      align: 'center',
      title: '名称',
      dataIndex: 'name',
      width: '22%',
      children: [
        {
          key: 'capacity',
          align: 'center',
          title: '电量',
          dataIndex: 'capacity',
          rowScope: 'row',
        },
      ],
    },
    {
      key: '11B',
      align: 'center',
      title: '11B',
      dataIndex: '11B',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '合计' ? 3 : 1,
      }),
    },
    {
      key: '12B',
      align: 'center',
      title: '12B',
      dataIndex: '12B',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '合计' ? 0 : 1,
      }),
    },
    {
      key: '13B',
      align: 'center',
      title: '13B',
      dataIndex: '13B',
      width: '26%',
      onCell: (data: RecordType) => ({
        colSpan: data.capacity === '合计' ? 0 : 1,
      }),
    },
  ];

  const tableHeaderRenderer = () => (
    <div style={{ position: 'relative' }}>
      <span>三、生产厂用电量</span>
      <span
        style={{
          position: 'absolute',
          left: '0',
          right: '0',
          margin: 'auto',
          textAlign: 'center',
        }}
      >
        单位：万千瓦时
      </span>
    </div>
  );

  return (
    <ProductionConsumptionTableWrapper>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              borderColor: '#2f4f81',
            },
          },
        }}
      >
        <Table
          bordered
          size="small"
          title={tableHeaderRenderer}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </ConfigProvider>
    </ProductionConsumptionTableWrapper>
  );
};

export default memo(ProductionConsumptionTable);
