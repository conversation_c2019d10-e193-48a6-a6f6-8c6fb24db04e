import {
  FC,
  ReactNode,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  DateType,
  PropOptions,
  SelectOptions,
  UseImperativeOptions,
  dateList,
  typeList,
} from './influxes-table';
import { ColumnsType } from 'antd/es/table';
import { ColumnsOptions } from '@/components/table-component/table';
import {
  CellControllerOptions,
  DataTypeEnum,
  InfluxesEnums,
  InfluxesOptions,
} from '@/components/spread-table/spread-table';
import { Dropdown, Input, Space, Tooltip, message } from 'antd';
import { TableComponent } from '@/components/table-component/table-component';
import styles from '../../add-template-version.page.less';
import { CheckOutlined, DeleteOutlined, DownOutlined } from '@ant-design/icons';
import { validateExcelPosition } from '@/utils/utils';
import PointImg from '@/assets/images/point_small.png';
import { PointModal } from '../../../../components/point-modal/point-modal.component';
import { TreeOptions } from '@/pages/monitoring-point-annotation/components/monitoring-point/monitoring-point';
import { TabItems } from '../../add-template-version';

const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
export const InfluxesTable: FC<PropOptions> = memo(
  forwardRef(
    (
      {
        infoTabChange,
        collapseChange,
        otherTableList,
        inputFocusFn,
        tableList,
        allTableList,
        tableListChange,
        isEdit,
        spreadRef,
      },
      ref,
    ) => {
      const otherTableLRef = useRef<CellControllerOptions[]>([]);
      //当前操作表格项
      const [curTableItem, setCurTableItem] = useState<InfluxesOptions>();
      //测点弹框控制
      const [pointModalShow, setPointModalShow] = useState<boolean>(false);
      //测点表格字段配置
      const columns: ColumnsType<ColumnsOptions> = [
        {
          title: '位置',
          dataIndex: 'startPosition',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => tableInput(record, 'startPosition', 'input'),
        },
        {
          title: '测点',
          dataIndex: 'measureName',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => pointNameComponent(record),
        },
        {
          title: '值类型',
          dataIndex: 'type',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => tableInput(record, 'type', 'select', typeList),
        },
        {
          title: '日期类型',
          dataIndex: 'dateType',
          align: 'center',
          ellipsis: true,
          render: (text: string, record: any) => {
            let list = dateList;
            if (record.type === InfluxesEnums.INSTANTANEOUS) {
              //瞬时值只能有月数据
              list = list.filter((item) => item.key === DateType.MONTH);
            }
            return tableInput(record, 'dateType', 'select', list, '月数据');
          },
        },
        {
          title: '操作',
          render: (text: string, record: any) => controllerComponent(record),
          align: 'center',
          ellipsis: true,
        },
      ];
      useEffect(() => {
        otherTableLRef.current = otherTableList;
      }, [otherTableList]);
      useImperativeHandle<any, UseImperativeOptions>(ref, () => ({
        cellImgHandler,
        cellImgBatchHandler,
        addPointRange,
        tablePointEvent,
      }));
      //Input聚焦处理
      function onFoucsHandler(data: InfluxesOptions): void {
        if (data.isEdit) {
          inputFocusFn(data, DataTypeEnum.INFLUXES);
        }
      }
      //测点表格输入框
      function tableInput(
        data: InfluxesOptions,
        field: string,
        type: 'input' | 'select',
        options?: SelectOptions[],
        defaultVal: string = '',
      ): ReactNode {
        const curData = options?.find((item) => item.key === data[field]);
        if (data.isEdit) {
          return type === 'input' ? (
            <Input
              onFocus={() => onFoucsHandler(data)}
              onChange={(e) =>
                tableInputChange({
                  value: e.target.value,
                  field,
                  data,
                  type,
                })
              }
              value={data[field]}
            />
          ) : (
            <Dropdown
              menu={{
                items: options,
                onClick: ({ key }) =>
                  tableInputChange({
                    value: key,
                    field,
                    data,
                    type,
                  }),
              }}
            >
              <span title={curData?.label}>
                {curData ? curData.label : '请选择'}
                <DownOutlined />
              </span>
            </Dropdown>
          );
        } else {
          return curData ? curData.label : data[field] || defaultVal;
        }
      }
      //测点表格输入框改变
      function tableInputChange({
        value,
        field,
        data,
        type,
      }: {
        value: string;
        field: string;
        data: InfluxesOptions;
        type: 'input' | 'select';
      }): void {
        const list = allTableList.map((item) => {
          if (item.id === data.id) {
            item[field] = type === 'input' ? value.toUpperCase() : value;
            if (
              field === 'type' &&
              (item[field] === InfluxesEnums.MAX || item[field] === InfluxesEnums.MIN)
            ) {
              item.value = item[field] === InfluxesEnums.MAX ? 'max' : 'min';
            } else if (field === 'type') {
              item.value = '';
              item.dateType = DateType.MONTH;
            }
          }
          return item;
        });
        tableListChange(list);
      }
      //删除测点表格子项
      function delTableItem(data: InfluxesOptions): void {
        const list = allTableList.filter((item) => item.id !== data.id);
        tableListChange(list);
        if (!data.isEdit) {
          cellImgHandler(data, 1);
        }
      }
      //测点背景图修改
      function cellImgHandler(data: InfluxesOptions, status: 0 | 1 = 0): void {
        // spreadRef.current.cellImgChange(data, status, PointImg);
      }
      //测点背景图批量处理
      function cellImgBatchHandler(list: InfluxesOptions[] = [], status: 0 | 1): void {
        list.forEach((item) => {
          cellImgHandler(item, status);
        });
      }
      //确定提交测点表格数据
      function sureTableData(data: InfluxesOptions): void {
        if (data.measureName) {
          // 校验
          const row = validateExcelPosition(data.startPosition);
          if (!row.valid) {
            message.warning('请输入有效的Excel位置，例如 "A1"、"B2"');
          } else if (row.column) {
            const { startPosition } = data;
            const verify = verifyList(data);
            if (verify === false) {
              message.warning('该单元格已经有绑定的测点了！');
            } else {
              const list = allTableList.map((item) => {
                if (item.id === data.id) {
                  item.isEdit = false;
                  item.endPosition = startPosition;
                  cellImgHandler(item, 0);
                }
                return item;
              });
              tableListChange(list);
            }
          }
        } else {
          message.warning('请选择一个测点！');
        }
      }
      //校验测点绑定是否重复
      function verifyList(data: InfluxesOptions): boolean {
        const newList = otherTableLRef.current.filter((item) => item.id !== data.id);
        return !newList.some((item) => {
          if (item.startPosition === data.startPosition) {
            return true;
          } else {
            return false;
          }
        });
      }
      //测点表格操作栏渲染
      function controllerComponent(data: InfluxesOptions): ReactNode {
        const del = (
          <Tooltip title="删除">
            <DeleteOutlined onClick={() => delTableItem(data)} className={styles.icon} />
          </Tooltip>
        );
        const sure = (
          <Tooltip title="确定">
            <CheckOutlined onClick={() => sureTableData(data)} className={styles.icon} />
          </Tooltip>
        );
        return (
          <div className={styles['table-controller-container']}>
            {isEdit && (
              <Space size="middle">
                {data.isEdit && sure}
                {del}
              </Space>
            )}
          </div>
        );
      }
      //测点名称表格模板
      function pointNameComponent(data: InfluxesOptions): ReactNode {
        let name = '选择测点';
        if (data.measureName) {
          name = data.measureName;
        }
        return (
          <span
            className={styles['point-name']}
            onClick={() => {
              setCurTableItem(data);
              setPointModalShow(true);
            }}
          >
            {name}
          </span>
        );
      }
      //获取当前工作表数据
      function getWorkTableData(): any {
        const sheetSnapshot = spreadRef.current.getWorkTableData();
        return sheetSnapshot;
      }
      //测点绑定改变
      function pointChange(data: TreeOptions): void {
        const list = allTableList.map((item) => {
          if (curTableItem && item.id === curTableItem.id) {
            item.measureName = data.pointName;
            item.measureId = data.pointCode;
            item.dataType = data.pointType;
          }
          return item;
        });
        tableListChange(list);
        setCurTableItem(undefined);
        setPointModalShow(false);
      }
      //测试绑定取消
      function pointCancel(): void {
        if (curTableItem) {
          const data = tableList.find(
            (item) => !item.isEdit && curTableItem.id === item.id && item.measureId === '',
          );
          if (data) {
            const list = allTableList.filter((item) => item.id !== data.id);
            tableListChange(list);
            cellImgHandler(data, 1);
          }
          setCurTableItem(undefined);
        }
        setPointModalShow(false);
      }
      //折叠面板处理
      function collapseChangeHandler(): void {
        TabItems && infoTabChange(TabItems[1].key);
        collapseChange((prev) => {
          if (prev.includes(DataTypeEnum.INFLUXES)) {
            return prev;
          } else {
            return prev.concat(DataTypeEnum.INFLUXES);
          }
        });
      }
      //表格测点绑定事件
      function tablePointEvent(): void {
        const data = spreadRef.current.getActiveCell();
        if (data) {
          const table = getWorkTableData();
          const isMerged = spreadRef.current.cellIsMerged(table, data.selection);
          const { rowCount, colCount } = data.selection;
          if (isMerged || (rowCount === 1 && colCount === 1)) {
            const point: InfluxesOptions = {
              id: new Date().getTime().toString(),
              tableId: table.name(),
              tableName: table.name(),
              startPosition: data.start, //开始
              endPosition: data.end, //结束
              rowCount: 1, //行数
              colCount: 1, //列数
              editUserId: userId, //操作用户id
              isEdit: true,
              measureName: '',
              measureId: '',
              dataType: '',
              type: InfluxesEnums.MAX,
              dateType: DateType.MONTH,
              value: 'max',
            };
            tableListChange((prev) => {
              if (verifyList(point)) {
                collapseChangeHandler();
                setPointModalShow(true);
                setCurTableItem(point);
                cellImgHandler(point, 0);
                return prev.concat(point);
              } else {
                message.warning('该单元格已经有绑定的测点了！');
                return prev;
              }
            });
          } else {
            message.warning('只能绑定一个单元格');
          }
        }
      }
      //新增测点选区
      function addPointRange(): void {
        const table = getWorkTableData();
        const data: InfluxesOptions = {
          id: new Date().getTime().toString(),
          tableId: table.name(),
          tableName: table.name(),
          startPosition: '', //开始行
          endPosition: '', //开始列
          rowCount: 1, //行数
          colCount: 1, //列数
          editUserId: userId, //可操作用户id
          isEdit: true,
          measureName: '',
          measureId: '',
          dataType: '',
          type: InfluxesEnums.MAX,
          dateType: DateType.MONTH,
          value: 'max',
        };
        tableListChange((prev) => {
          return prev.concat(data);
        });
      }
      return (
        <>
          <TableComponent isIndex={false} columns={columns} tableList={tableList}></TableComponent>
          {/* 测点弹框 */}
          <PointModal onOk={pointChange} close={pointCancel} open={pointModalShow}></PointModal>
        </>
      );
    },
  ),
);
