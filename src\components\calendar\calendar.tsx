// src/components/Calendar.jsx
import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import dayjs from 'dayjs';
import { EyeOutlined } from '@ant-design/icons';
import { But<PERSON>, Select } from 'antd';
interface Iprops {
  dataInfo: any[];
}
const colorTypes = {
  '1': '#7176EE',
  '2': '#95F204',
  '3': '#F59A23',
  '4': '#01F5F8',
};

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const slideIn = keyframes`
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

const CalendarContainer = styled.div`
  width: 100%;
  margin: auto;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: ${fadeIn} 0.5s ease-out;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 2rem;
  padding-bottom: 1rem;
`;

const Title = styled.h1`
  color: #2c3e50;
  font-size: 2.5rem;
  margin: 0;
  font-weight: 600;
  background: linear-gradient(90deg, #3498db, #2c3e50);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Controls = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;
const Weekdays = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #3498db;
  background: rgba(33, 122, 255, 0.2);
  padding: 7px 0px;
  font-size: 0.8rem;
`;

const DaysGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  // gap: 8px;
`;

const Day = styled.div`
  height: 180px;
  padding: 0 8px 15px 0px;
  border-bottom: 1px solid #3498db;
  // background: ${(props: any) => (props.isToday ? '#e1f0fa' : 'white')};
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  transition: all 0.2s;
  animation: ${slideIn} 0.3s ease-out;
  animation-delay: ${(props: any) => props.delay * 0.03}s;
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
  }

  &.prev-month,
  &.next-month {
    background: rgba(189, 195, 199, 0.1);
    background-clip: content-box;
  }

  &.weekend {
    // background: ${(props: any) => (props.isToday ? '#e1f0fa' : '#f8f9fa')};
    // color: ${(props: any) => (props.isToday ? '#3498db' : '#e74c3c')};
  }
`;

const DayNumber = styled.div`
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 4px;
  text-align: right;
  padding-right: 8px;
  color: ${(props: any) => (props.isToday ? '#3498db' : 'inherit')};
`;

const EventsIndicator = styled.div`
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background-color: #3498db;
  margin-top: 4px;
`;

const EmptyDay = styled.div`
  height: 90px;
  padding: 10px;
  border-radius: 8px;
  background: transparent;
`;

const Calendar: React.FC<Iprops> = ({ dataInfo }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [displayedDate, setDisplayedDate] = useState(new Date());
  const [isAnimating, setIsAnimating] = useState(false);

  // // 获取月份名称
  // const getMonthName = (date) => {
  //   return date.toLocaleString('default', { month: 'long' });
  // };

  // 生成年份范围
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 10; i <= currentYear + 10; i++) {
      years.push(i);
    }
    return years;
  };

  // 生成月份选项
  const months = Array.from({ length: 12 }, (_, i) =>
    new Date(0, i).toLocaleString('default', { month: 'long' }),
  );

  // 处理年份变化
  const handleYearChange = (e: string) => {
    const year = parseInt(e);
    const newDate = new Date(displayedDate);
    newDate.setFullYear(year);
    setDisplayedDate(newDate);
  };

  // 处理月份变化
  const handleMonthChange = (e: string) => {
    const month = parseInt(e);
    const newDate = new Date(displayedDate);
    newDate.setMonth(month);
    setDisplayedDate(newDate);
  };

  // 导航到上个月
  const goToPreviousMonth = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    const newDate = new Date(displayedDate);
    newDate.setMonth(newDate.getMonth() - 1);

    setTimeout(() => {
      setDisplayedDate(newDate);
      setIsAnimating(false);
    }, 300);
  };

  // 导航到下个月
  const goToNextMonth = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    const newDate = new Date(displayedDate);
    newDate.setMonth(newDate.getMonth() + 1);

    setTimeout(() => {
      setDisplayedDate(newDate);
      setIsAnimating(false);
    }, 300);
  };

  // 导航到当前月
  const goToCurrentMonth = () => {
    setDisplayedDate(new Date());
  };

  // 生成日历日期
  const generateCalendarDays = () => {
    console.log('dataInfo', dataInfo);
    const year = displayedDate.getFullYear();
    const month = displayedDate.getMonth();
    // 当前月的第一天
    const firstDayOfMonth = new Date(year, month, 1);
    // 当前月的最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0);
    // 日历开始日期（包括上个月的部分日期）
    const startDay = new Date(firstDayOfMonth);
    startDay.setDate(firstDayOfMonth.getDate() - firstDayOfMonth.getDay() + 1);
    // 日历结束日期（包括下个月的部分日期）
    const endDay = new Date(lastDayOfMonth);
    endDay.setDate(lastDayOfMonth.getDate() + (7 - lastDayOfMonth.getDay()));
    const days = [];
    const today = new Date();

    let day = new Date(startDay);
    let delayCounter = 0;
    while (day <= endDay) {
      const isCurrentMonth = day.getMonth() === month;
      const isToday =
        day.getDate() === today.getDate() &&
        day.getMonth() === today.getMonth() &&
        day.getFullYear() === today.getFullYear();
      const isWeekend = day.getDay() === 0 || day.getDay() === 6;

      days.push(
        <Day
          key={day.toISOString()}
          isToday={isToday}
          className={`${
            isCurrentMonth ? '' : day < firstDayOfMonth ? 'prev-month' : 'next-month'
          } ${isWeekend ? 'weekend' : ''}`}
          delay={delayCounter++}
        >
          <DayNumber isToday={isToday}>{day.getDate()}</DayNumber>
          {isCurrentMonth && dayMain()}
        </Day>,
      );

      day.setDate(day.getDate() + 1);
    }
    return days;
  };
  const dayMain = () => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          justifyContent: 'space-between',
        }}
      >
        <div
          style={{
            height: '30px',
            padding: '0px 10px',
            background: ' rgba(33, 122, 255, 0.1)',
            fontSize: '0.8rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>
            <span style={{ color: '#7176EE', marginRight: '5px' }}>夜</span>
            <span>张三</span>
          </div>
          <EyeOutlined style={{ color: '#3498db', cursor: 'pointer' }} />
        </div>
        <div
          style={{
            height: '30px',
            paddingLeft: '10px',
            background: ' rgba(33, 122, 255, 0.1)',
            fontSize: '0.8rem',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <span style={{ color: '#95F204', marginRight: '5px' }}>早</span>
          <span>张三</span>
        </div>
        <div
          style={{
            height: '30px',
            paddingLeft: '10px',
            background: ' rgba(33, 122, 255, 0.1)',
            fontSize: '0.8rem',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <span style={{ color: '#F59A23', marginRight: '5px' }}>中</span>
          <span>张三</span>
        </div>
        <div
          style={{
            height: '30px',
            paddingLeft: '10px',
            background: ' rgba(33, 122, 255, 0.1)',
            fontSize: '0.8rem',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <span style={{ color: '#01F5F8', marginRight: '5px' }}>晚</span>
          <span>张三</span>
        </div>
      </div>
    );
  };
  // 生成星期标题
  const generateWeekdays = () => {
    const weekdays = ['一', '二', '三', '四', '五', '六', '日'];
    return weekdays.map((day, index) => <div key={index}>{day}</div>);
  };

  return (
    <CalendarContainer>
      <Header>
        {/* <Title>
          {getMonthName(displayedDate)} {displayedDate.getFullYear()}
        </Title> */}
        <Controls>
          <Button onClick={goToPreviousMonth} disabled={isAnimating}>
            上个月
          </Button>
          <Button onClick={goToCurrentMonth} disabled={isAnimating}>
            今日
          </Button>
          <Button onClick={goToNextMonth} disabled={isAnimating}>
            下个月
          </Button>
          <Select
            value={displayedDate.getMonth()}
            onChange={handleMonthChange}
            style={{ width: '120px' }}
          >
            {months.map((month, index) => (
              <option key={month} value={index}>
                {month}
              </option>
            ))}
          </Select>
          <Select value={displayedDate.getFullYear()} onChange={handleYearChange}>
            {generateYearOptions().map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </Select>
        </Controls>
      </Header>
      <Weekdays>{generateWeekdays()}</Weekdays>
      <DaysGrid>{generateCalendarDays()}</DaysGrid>
    </CalendarContainer>
  );
};

export default Calendar;
