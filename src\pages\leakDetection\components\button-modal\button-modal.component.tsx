import { memo, useState } from 'react';
import styles from './button-modal.component.less';
import { WarningModal } from './components/warning-modal/warning-modal.component';
import { ItemEnum, ItemOptions } from './button-modal';
import { TrendModal } from './components/trend-modal/trend-modal.component';
import { EventModal } from './components/event-modal/event-modal.component';
import { LogModal } from './components/log-modal/log-modal.component';

export const ButtonModal = memo(() => {
  const [warOpen, setWarOpen] = useState<boolean>(false);
  const [trendOpen, setTrendOpen] = useState<boolean>(false);
  const [eventOpen, setEventOpen] = useState<boolean>(false);
  const [logOpen, setLogOpen] = useState<boolean>(false);
  const [buttonList, setButtonList] = useState<ItemOptions[]>([
    {
      label: '报警', // 根据设定的阈值显示报警图标颜色：蓝色、橙色和红色三种状态
      value: ItemEnum.WARNING,
      icon: ItemEnum.WARNING + '-icon',
    },
    {
      label: '趋势',
      value: ItemEnum.TREND,
      icon: ItemEnum.TREND + '-icon',
    },
    {
      label: '事件',
      value: ItemEnum.EVENT,
      icon: ItemEnum.EVENT + '-icon',
    },
    {
      label: '日志',
      value: ItemEnum.LOG,
      icon: ItemEnum.LOG + '-icon',
    },
  ]);
  //子项点击
  function itemClickChange(data: ItemOptions): void {
    switch (data.value) {
      case ItemEnum.WARNING:
        setWarOpen(true);
        break;
      case ItemEnum.TREND:
        setTrendOpen(true);
        break;
      case ItemEnum.EVENT:
        setEventOpen(true);
        break;
      case ItemEnum.LOG:
        setLogOpen(true);
        break;
    }
  }
  return (
    <section className={styles['button-modal-container']}>
      {buttonList.map((item) => (
        <div
          onClick={() => itemClickChange(item)}
          className={styles['button-modal-item']}
          key={item.value}
        >
          <span className={`${styles['button-modal-item-icon']} ${styles[item.icon]}`}></span>
          <span className={styles['button-modal-item-label']}>{item.label}</span>
        </div>
      ))}
      {/* 报警弹框列表 */}
      <WarningModal open={warOpen} close={() => setWarOpen(false)} />
      {/* 趋势弹框列表 */}
      <TrendModal open={trendOpen} close={() => setTrendOpen(false)}></TrendModal>
      {/* 事件弹框列表 */}
      <EventModal open={eventOpen} close={() => setEventOpen(false)}></EventModal>
      {/* 日志弹框列表 */}
      <LogModal open={logOpen} close={() => setLogOpen(false)}></LogModal>
    </section>
  );
});
